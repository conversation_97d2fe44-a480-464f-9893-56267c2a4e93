USE memberCentral;
GO

-- Add reportID column to rpt_documents table to track which report generated the document
ALTER TABLE dbo.rpt_documents ADD reportID int NULL;
GO

-- Add foreign key constraint to link reportID to rpt_SavedReports
ALTER TABLE dbo.rpt_documents WITH CHECK ADD CONSTRAINT FK_rpt_documents_rpt_SavedReports
    FOREIGN KEY(reportID) REFERENCES dbo.rpt_SavedReports (reportID);
GO

-- Add index on reportID for better query performance
CREATE NONCLUSTERED INDEX IX_rpt_documents_reportID ON dbo.rpt_documents (reportID);
GO

ALTER PROC dbo.rpt_createReportDocument
@siteID INT,
@documentID INT,
@dateExpire DATETIME = NULL,
@reportID INT = NULL,
@documentUID UNIQUEIDENTIFIER OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @documentUID = NEWID();

	IF(@dateExpire IS NULL)
		SET @dateExpire = DATEADD(DAY, 7, GETDATE());

	INSERT INTO dbo.rpt_documents (siteID, [uid], documentID, dateExpire, reportID)
	VALUES (@siteid, @documentUID, @documentID, @dateExpire, @reportID);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
