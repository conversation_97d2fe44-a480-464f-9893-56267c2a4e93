USE membercentral;
GO

IF NOT EXISTS (
    SELECT * FROM sys.indexes 
    WHERE object_id = OBJECT_ID('dbo.cms_categories') 
      AND name = 'ux_categories_categoryID'
)
BEGIN
    CREATE UNIQUE INDEX ux_categories_categoryID 
    ON dbo.cms_categories(categoryID);
END
GO

IF NOT EXISTS (
    SELECT * FROM sys.fulltext_indexes 
    WHERE object_id = OBJECT_ID('dbo.cms_categories')
)
BEGIN
    CREATE FULLTEXT INDEX ON dbo.cms_categories(categoryPath)
    KEY INDEX ux_categories_categoryID
    ON [membercentral-content]
    WITH CHANGE_TRACKING AUTO;
END
GO