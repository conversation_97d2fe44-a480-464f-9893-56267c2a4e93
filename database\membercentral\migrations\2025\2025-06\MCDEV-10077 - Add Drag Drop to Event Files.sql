USE membercentral
GO

-- delete method doRateMove, doEventDocMove
DECLARE @componentID int, @methodID int;

SELECT @componentID = componentID
FROM dbo.ajax_components
WHERE componentName = 'ADMINEVENT';

SELECT @methodID = methodID
FROM dbo.ajax_componentMethods
WHERE componentID = @componentID
AND methodName = 'doRateMove';


IF @methodID IS NOT NULL BEGIN
	DELETE FROM dbo.ajax_componentMethodAccess WHERE methodID = @methodID;
	DELETE FROM dbo.ajax_componentMethods WHERE methodID = @methodID;
END


SELECT @methodID = methodID
FROM dbo.ajax_componentMethods
WHERE componentID = @componentID
AND methodName = 'doEventDocMove';
IF @methodID IS NOT NULL BEGIN
	DELETE FROM dbo.ajax_componentMethodAccess WHERE methodID = @methodID;
	DELETE FROM dbo.ajax_componentMethods WHERE methodID = @methodID;
END

GO

drop proc ev_moveEventDocument;
drop proc ev_moveRate;
GO