USE membercentral
GO

ALTER PROC dbo.tr_autoCloseSystemBatches
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @enteredByMemberID int, @dateToUse datetime, @orgID int, @closedStatusID int, @openStatusID int, 
		@openForModificationStatusID int, @exceptionBatchTypeID int;
	select @enteredByMemberID = dbo.fn_ams_getMCSystemMemberID();
	set @dateToUse = DATEADD(dd, DATEDIFF(dd,0,getdate()), 0);

    select @closedStatusID = statusID from dbo.tr_batchStatuses where status = 'closed';
    select @openStatusID = statusID from dbo.tr_batchStatuses where status = 'open';
    select @openForModificationStatusID = statusID from dbo.tr_batchStatuses where status = 'Open for Modification';
	select @exceptionBatchTypeID = batchTypeID from dbo.tr_batchTypes where batchType = 'Exceptions';

	IF OBJECT_ID('tempdb..#mcBatchesToClose') IS NOT NULL 
		DROP TABLE #mcBatchesToClose;
	IF OBJECT_ID('tempdb..#tmpMCPayECheckMPProfiles') IS NOT NULL 
		DROP TABLE #tmpMCPayECheckMPProfiles;
	CREATE TABLE #mcBatchesToClose (batchID int, statusID int);
	CREATE TABLE #tmpMCPayECheckMPProfiles (profileID int);

	select @orgID = min(orgID) from dbo.organizations;
	while @orgID is not null begin

		-- MCPayEcheck profiles
		INSERT INTO #tmpMCPayECheckMPProfiles (profileID)
		SELECT mp.profileID
		FROM dbo.mp_profiles AS mp
		INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
		INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID
		WHERE s.orgID = @orgID
		AND mp.[status] IN ('A','I')
		AND g.gatewayType = 'MCPayEcheck'
		AND g.isActive = 1;

		-- get batches that need to be closed
		INSERT INTO #mcBatchesToClose (batchID, statusID)
		SELECT b.batchID, b.statusID
		FROM dbo.tr_batches AS b
		LEFT OUTER JOIN #tmpMCPayECheckMPProfiles AS tmp ON tmp.profileID = b.payProfileID
		WHERE b.orgID = @orgID
		AND b.statusID IN (@openStatusID,@openForModificationStatusID)
		AND b.isSystemCreated = 1
		AND b.depositDate < @dateToUse
		AND ISNULL(b.batchCode,'') <> 'PENDINGPAYMENTS'
		AND tmp.profileID IS NULL;

		IF @@ROWCOUNT > 0 BEGIN
			BEGIN TRAN;
				-- record status history
				INSERT INTO dbo.tr_batchStatusHistory (batchID, updateDate, statusID, oldStatusID, enteredByMemberID)
				select batchID, getdate(), @closedStatusID, statusID, @enteredByMemberID
				FROM #mcBatchesToClose;
				
				-- close batch
				update b
				set b.statusID = @closedStatusID
				from dbo.tr_batches as b
				inner join #mcBatchesToClose as tmp on tmp.batchID = b.batchID
				where b.orgID = @orgID;
			COMMIT TRAN;
		END

		TRUNCATE TABLE #tmpMCPayECheckMPProfiles;
		TRUNCATE TABLE #mcBatchesToClose;

		select @orgID = min(orgID) from dbo.organizations where orgID > @orgID;
	end

	IF OBJECT_ID('tempdb..#mcBatchesToClose') IS NOT NULL 
		DROP TABLE #mcBatchesToClose;
	IF OBJECT_ID('tempdb..#tmpMCPayECheckMPProfiles') IS NOT NULL 
		DROP TABLE #tmpMCPayECheckMPProfiles;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.tr_autoPostSystemBatches
@orgID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	/*
	If orgID is null this proc is called by the nightly jobs and will feed the batchPost queue.
	If orgID is passed in, it is called by either ca_insertPACPaymentHistory or WA_addEagleProgramHistory and needs to post immediately.
	*/
	DECLARE @runImmediate bit = 0;
	IF @orgID is not null
		SET @runImmediate = 1;

	IF OBJECT_ID('tempdb..#tmpMCPayECheckMPProfiles') IS NOT NULL 
		DROP TABLE #tmpMCPayECheckMPProfiles;
	CREATE TABLE #tmpMCPayECheckMPProfiles (profileID int);

	declare @enteredByMemberID int, @batchIDList varchar(max), @dateToUse datetime, @postedStatusID int, 
		@closedStatusID int, @openStatusID int, @openForModificationStatusID int, @thisAutoID int, 
		@exceptionBatchTypeID int, @itemIDAsStr varchar(20), @runUID uniqueidentifier = NEWID();
	declare @orgs TABLE (autoID int IDENTITY(1,1) PRIMARY KEY, orgID int);

	set @dateToUse = DATEADD(dd, DATEDIFF(dd,0,getdate()), 0);

	select @enteredByMemberID = dbo.fn_ams_getMCSystemMemberID();
    select @postedStatusID = statusID from dbo.tr_batchStatuses where status = 'Posted';
    select @closedStatusID = statusID from dbo.tr_batchStatuses where status = 'Closed';
	select @exceptionBatchTypeID = batchTypeID from dbo.tr_batchTypes where batchType = 'Exceptions';

	IF @orgID is null BEGIN
		insert into @orgs(orgID)
		select orgID from organizations

		-- remove orgs with no batches to POST
		delete o
		from @orgs o
		left outer join dbo.tr_batches b on b.orgID = o.orgID
			and b.statusID = @closedStatusID
			and b.isSystemCreated = 1
			and b.depositDate < @dateToUse
			and isnull(b.batchCode,'') <> 'PENDINGPAYMENTS'
		where b.batchID is null;
	END ELSE
		insert into @orgs(orgID)
		VALUES (@orgID);

	-- do this by org
	select @thisAutoID = min(autoID) from @orgs
	while @thisAutoID is not null begin
		set @batchIDList = null;
		select @orgID = orgID from @orgs where autoID = @thisAutoID;

		-- MCPayEcheck profiles
		INSERT INTO #tmpMCPayECheckMPProfiles (profileID)
		SELECT mp.profileID
		FROM dbo.mp_profiles AS mp
		INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
		INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID
		WHERE s.orgID = @orgID
		AND mp.[status] IN ('A','I')
		AND g.gatewayType = 'MCPayEcheck'
		AND g.isActive = 1;

		-- get closed batches that need to be posted (tr_autoCloseSystemBatches closed them)
		IF @runImmediate = 0
			INSERT INTO platformQueue.dbo.queue_batchPost (itemGroupID, orgID, batchID, addedByMemberID, dateAdded, dateUpdated)
			SELECT @runUID, @orgID, b.batchID, @enteredByMemberID, getdate(), getdate()
			FROM dbo.tr_batches AS b
			LEFT OUTER JOIN #tmpMCPayECheckMPProfiles AS tmp ON tmp.profileID = b.payProfileID
			WHERE b.orgID = @orgID
			AND b.statusID = @closedStatusID
			AND b.isSystemCreated = 1
			AND b.depositDate < @dateToUse
			AND ISNULL(b.batchCode,'') <> 'PENDINGPAYMENTS'
			AND tmp.profileID IS NULL;

		ELSE BEGIN
			SELECT @batchIDList = COALESCE(@batchIDList+',','') + CAST(b.batchID AS varchar(10)) 
			FROM dbo.tr_batches AS b
			LEFT OUTER JOIN #tmpMCPayECheckMPProfiles AS tmp ON tmp.profileID = b.payProfileID
			WHERE b.orgID = @orgID
			AND b.statusID = @closedStatusID
			AND b.isSystemCreated = 1
			AND b.depositDate < @dateToUse
			AND ISNULL(b.batchCode,'') <> 'PENDINGPAYMENTS'
			AND tmp.profileID IS NULL;

			IF @batchIDList is not null and len(@batchIDList) > 0
				EXEC dbo.tr_postBatch @orgID=@orgID, @batchIDList=@batchIDList, @enteredByMemberID=@enteredByMemberID;
		END

		TRUNCATE TABLE #tmpMCPayECheckMPProfiles;

		select @thisAutoID = min(autoID) from @orgs where autoID > @thisAutoID;
	end

	IF @runImmediate = 0 BEGIN
		-- send message to service broker to create all the individual messages
		DECLARE @xmlMessage xml;
		select @xmlMessage = isnull((
			select 'batchPostLoad' as t, cast(@runUID as varchar(60)) as u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');
		EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
	END

	IF OBJECT_ID('tempdb..#tmpMCPayECheckMPProfiles') IS NOT NULL 
		DROP TABLE #tmpMCPayECheckMPProfiles;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO