USE [platformStatsMC]
GO

CREATE TABLE dbo.ams_memberLoginsFailed (
	[loginid] [int] IDENTITY(1,1) NOT NULL,
	[username] [varchar](75) NOT NULL,
	[password] [varchar](100) NOT NULL,
	[siteID] [int] NOT NULL,
	[statsSessionID] [int] NULL,
	[dateentered] [datetime] NOT NULL,
	[loginAsMemberID] [int] NULL,
 CONSTRAINT [PK__ams_memberLoginsFailed] PRIMARY KEY CLUSTERED 
(
	[loginid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

use membercentral
GO

ALTER PROC dbo.ams_up_attemptUser
@sessionID int,
@siteID int,
@username varchar(75),
@password varchar(100),
@loginAsMemberID int,
@result varchar(10) OUTPUT,
@memberID int OUTPUT,
@memberStatus varchar(1) OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @sf_sitePasswords bit, @orgID int;
	SELECT @sf_sitePasswords = sitePasswords FROM dbo.siteFeatures where siteID = @siteID;
	SELECT @orgID = orgID FROM dbo.sites WHERE siteID = @siteID;
	SET @result = '';
	SET @memberStatus = '';

	IF @sf_sitePasswords = 1 BEGIN
		DECLARE @mnpPasswordSalt uniqueidentifier, @mnpPasswordHash binary(64), @testPasswordHash binary(64);

		-- get mnp based on username only
		SELECT @memberID = m.memberID, @mnpPasswordSalt = mnp.PasswordSalt, @mnpPasswordHash = mnp.PasswordHash,
			@memberStatus = m.[status]
		FROM dbo.ams_networkProfiles as np
		INNER JOIN dbo.ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID
			AND mnp.siteID = @siteID
			AND mnp.username = @username
			AND mnp.[status] = 'A'
		INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = mnp.memberID and m.[status] in ('A','I')
		WHERE np.[status] = 'A'
		AND mnp.PasswordSalt IS NOT NULL
		AND mnp.PasswordHash IS NOT NULL;

		IF @memberID IS NULL
			SET @result = 'fail';
		ELSE BEGIN
			IF @loginAsMemberID > 0 BEGIN
				IF @loginAsMemberID <> @memberID
					SET @result = 'fail';
				ELSE
					SET @result = 'pass.mnp';
			END ELSE BEGIN
				SET @testPasswordHash = HASHBYTES('SHA2_512', @password+CAST(@mnpPasswordSalt AS NVARCHAR(36)));
				IF @testPasswordHash <> @mnpPasswordHash
					SET @result = 'fail';
				ELSE
					SET @result = 'pass.mnp';
			END
		END
	END ELSE BEGIN
		DECLARE @loginNetworkID int;
		SELECT @loginNetworkID = dbo.fn_getLoginNetworkFromSiteID(@siteID);

		IF @loginAsMemberID > 0
			SELECT @memberID = m.memberID, @memberStatus = m.[status]
			FROM dbo.ams_networkProfiles as np
			INNER JOIN dbo.ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID
				AND mnp.siteID = @siteID
				AND mnp.[status] = 'A'
			INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = mnp.memberID and m.[status] in ('A','I')
				AND m.memberID = @loginAsMemberID
			WHERE np.networkID = @loginNetworkID
			AND np.username = @username
			AND np.[status] = 'A';
		ELSE
			SELECT @memberID = m.memberID, @memberStatus = m.[status]
			FROM dbo.ams_networkProfiles as np
			INNER JOIN dbo.ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID
				AND mnp.siteID = @siteID
				AND mnp.[status] = 'A'
			INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = mnp.memberID and m.[status] in ('A','I')
			WHERE np.networkID = @loginNetworkID
			AND np.username = @username
			AND np.[password] = @password
			AND np.[status] = 'A';

		IF @memberID IS NULL
			SET @result = 'fail';
		ELSE
			SET @result = 'pass.np';
	END

	-- if failure to this point, try member site defaults
	IF @result = 'fail' BEGIN
		DECLARE @defaultID int;
		SET @memberID = null;
		SET @memberStatus = null;
		
		IF @loginAsMemberID > 0
			SELECT @defaultID = msd.defaultID, @memberID = m.memberID, @memberStatus = m.[status]
			FROM dbo.ams_memberSiteDefaults as msd
			INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = msd.memberID and m.[status] in ('A','I')
				AND m.memberID = @loginAsMemberID
			WHERE msd.siteID = @siteID
			AND msd.defaultUsername = @username
			AND msd.[status] = 'A';
		ELSE
			SELECT @defaultID = msd.defaultID, @memberID = m.memberID, @memberStatus = m.[status]
			FROM dbo.ams_memberSiteDefaults as msd
			INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = msd.memberID and m.[status] in ('A','I')
			WHERE msd.siteID = @siteID
			AND msd.defaultUsername = @username
			AND msd.defaultPassword = @password
			AND msd.[status] = 'A';

		IF @defaultID IS NOT NULL
			SET @result = 'pass.msd';
	END

	SET @memberID = ISNULL(@memberID,0);
	SET @memberStatus = ISNULL(@memberStatus,'');

	-- If failed login, log it
	IF @result = 'fail'
		INSERT INTO platformStatsMC.dbo.ams_memberLoginsFailed (username, [password], siteID, statsSessionID, dateEntered, loginAsMemberID)
		VALUES (@username, @password, @siteID, @sessionID, getdate(), @loginAsMemberID);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO

ALTER PROC dbo.ams_up_attemptSuperUser
@sessionID int,
@siteID int,
@username varchar(75),
@password varchar(100),
@loginAsMemberID int = NULL,
@result varchar(10) OUTPUT,
@memberID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @sf_sitePasswords bit;
	SELECT @sf_sitePasswords = sitePasswords FROM dbo.siteFeatures where siteID = @siteID;
	SET @result = '';

	IF @sf_sitePasswords = 1 BEGIN
		DECLARE @mnpPasswordSalt uniqueidentifier, @mnpPasswordHash binary(64), @testPasswordHash binary(64);

		-- get mnp based on username only
		SELECT @memberID = m.memberID, @mnpPasswordSalt = mnp.PasswordSalt, @mnpPasswordHash = mnp.PasswordHash
		FROM dbo.ams_networkProfiles as np
		INNER JOIN dbo.ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID
			AND mnp.siteID = @siteID
			AND mnp.username = @username
			AND mnp.[status] = 'A'
		INNER JOIN dbo.ams_members as m on m.orgID = 1 and m.memberID = mnp.memberID and m.[status] in ('A','I')
		WHERE np.networkID = 1
		AND np.[status] = 'A'
		AND mnp.PasswordSalt IS NOT NULL
		AND mnp.PasswordHash IS NOT NULL;

		IF @memberID IS NULL
			SET @result = 'fail';
		ELSE BEGIN
			IF @loginAsMemberID > 0 BEGIN
				IF @loginAsMemberID <> @memberID
					SET @result = 'fail';
				ELSE
					SET @result = 'pass';
			END ELSE BEGIN
				SET @testPasswordHash = HASHBYTES('SHA2_512', @password+CAST(@mnpPasswordSalt AS NVARCHAR(36)));
				IF @testPasswordHash <> @mnpPasswordHash
					SET @result = 'fail';
				ELSE
					SET @result = 'pass';
			END
		END

	END ELSE BEGIN
		
		IF @loginAsMemberID > 0
			SELECT @memberID = m.memberID
			FROM dbo.ams_networkProfiles as np
			INNER JOIN dbo.ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID
				AND mnp.siteID = @siteID
				AND mnp.[status] = 'A'
			INNER JOIN dbo.ams_members as m on m.orgID = 1 and m.memberID = mnp.memberID and m.[status] in ('A','I')
				AND m.memberID = @loginAsMemberID
			WHERE np.networkID = 1
			AND np.username = @username
			AND np.[status] = 'A';
		ELSE
			SELECT @memberID = m.memberID
			FROM dbo.ams_networkProfiles as np
			INNER JOIN dbo.ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID
				AND mnp.siteID = @siteID
				AND mnp.[status] = 'A'
			INNER JOIN dbo.ams_members as m on m.orgID = 1 and m.memberID = mnp.memberID and m.[status] in ('A','I')
			WHERE np.networkID = 1
			AND np.username = @username
			AND np.[password] = @password
			AND np.[status] = 'A';

		IF @memberID IS NULL
			SET @result = 'fail';
		ELSE
			SET @result = 'pass';
	END

	SET @memberID = ISNULL(@memberID,0);

	-- If failed login, dont log it yet. This is because we still check superusers first in all login attempts. No need to record all those failures.
	/*
	IF @result = 'fail'
		INSERT INTO platformStatsMC.dbo.ams_memberLoginsFailed (username, [password], siteID, statsSessionID, dateEntered, loginAsMemberID, isSuperUserLogin)
		VALUES (@username, @password, @siteID, @sessionID, getdate(), @loginAsMemberID, 1);
	*/

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO
