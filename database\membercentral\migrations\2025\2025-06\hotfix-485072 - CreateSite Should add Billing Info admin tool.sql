use membercentral
GO

-- add to the sites that had it missing
declare @roleID int, @superBillingAdminsGroupID int;
SET @roleID = dbo.fn_getResourceRoleID('Super Billing Administrator');
SET @superBillingAdminsGroupID = dbo.fn_getGroupIDFromGroupName(1,'Super Billing Admins');

declare @tblSites TABLE (siteID int);
INSERT INTO @tblSites (siteID)
SELECT s.siteID
FROM dbo.sites as s 
INNER JOIN dbo.cms_siteResources as sr on sr.siteID = s.siteID 
	AND sr.siteResourceID = s.siteResourceID 
	AND sr.siteResourceStatusID = 1
LEFT OUTER JOIN dbo.cms_siteResourceRights as srr on srr.siteID = sr.siteID
	and srr.resourceID = sr.siteResourceID
	and srr.roleID = @roleID
WHERE srr.resourceRightsID IS NULL;

declare @siteID int, @orgID int, @SiteSiteResourceID int;
select @siteID = min(siteID) from @tblSites;
while @siteID is not null begin
	SELECT @SiteSiteResourceID = siteResourceID, @orgID = orgID from dbo.sites where siteID = @siteID;

	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@SiteSiteResourceID, @include=1,
		@functionIDList=null, @roleID=@roleID, @groupID=@superBillingAdminsGroupID, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

	EXEC dbo.createAdminSuite @siteid=@siteID;

	select @siteID = min(siteID) from @tblSites where siteID > @siteID;
end
GO

ALTER PROC dbo.createSite
@orgID int,
@environmentName varchar(50),
@sitecode varchar(10),
@siteName varchar(60),
@mainNetworkID int,
@isLoginNetwork bit,
@isMasterSite bit,
@hasDepoTLA bit,
@defaultLanguageID int,
@defaultTimeZoneID int,
@defaultCurrencyTypeID int,
@showCurrencyType bit,
@allowGuestAccounts bit,
@forceLoginPage bit,
@useRemoteLogin bit,
@affiliationRequired bit,
@enforceSiteAgreement bit,
@immediateMemberUpdates bit,
@emailMemberUpdates varchar(200),
@defaultPostalState varchar(10),
@joinURL varchar(100),
@alternateGuestAccountCreationLink varchar(400),
@alternateGuestAccountPopup bit,
@alternateForgotPasswordLink varchar(400),
@norightsContent varchar(max),
@norightsNotLoggedInContent varchar(max),
@inactiveUserContent varchar(max),
@siteagreementContent varchar(max),
@welcomeMessageContent varchar(max),
@firstTimeLoginContent varchar(max),
@defaultOrgIdentityID int,
@qualifyFID int,
@siteID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- start in snapshot so we can go in and out as needed
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @templateID int, @modeID int, @sectionID int, @sectionResourceTypeID int, @siteResourceTypeID int, 
		@accountingAdminRoleID int, @clientAdminRoleID int, @contentEditorRoleID int, @siteAdminRoleID int, @superAdminRoleID int, 
		@reportAdminRoleID int, @eventAdminRoleID int, @platformWideNetworkID int, @superBillingAdminRoleID int, 
		@siteAdminGroupID int, @superAdminGroupID int, @trashID int, @superBillingAdminsGroupID int, 
		@siteResourceID int, @hostnameID int, @templateTypeID int, @sysCreatedContentResourceTypeID int, @nowDate datetime,
		@newContentid int, @newresourceid int, @superNetworkID int, @sysMemberID int, @loginLimitModeID int,
		@mcapiSecret varchar(40), @apiusername varchar(40), @apipassword varchar(40), @sysAdminRoleID int, @mcDevGroupID int,
		@consentListTypeID int, @consentListModeID int, @consentListID int, @generalCommunicationsConsentListTypeName varchar(100),
		@environmentID int, @searchJoinURL varchar(200),  @customHeadContent varchar(max), @loginPolicyID int, @policySRID int,
		@tier varchar(12), @recCountOfFactors int, @subscriptionIssuesEmail VARCHAR(MAX), @memberAdminSiteResourceID int;

	SET @siteID = null;
	SET @siteCode = UPPER(@sitecode);

	IF dbo.fn_isValidUsageCode(@siteCode,'site') = 0
		RAISERROR('Invalid SiteCode.',16,1);

	SET @siteResourceTypeID = dbo.fn_getResourceTypeID('Site');
	SET @accountingAdminRoleID = dbo.fn_getResourceRoleID('Accounting Administrator');
	SET @clientAdminRoleID = dbo.fn_getResourceRoleID('Client Administrator');
	SET @contentEditorRoleID = dbo.fn_getResourceRoleID('Content Editor');
	SET @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator');
	SET @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator');
	SET @reportAdminRoleID = dbo.fn_getResourceRoleID('Report Administrator');
	SET @eventAdminRoleID = dbo.fn_getResourceRoleID('Event Administrator');
	SET @sysAdminRoleID = dbo.fn_getResourceRoleID('System Operations Administrator');
	SET @superBillingAdminRoleID = dbo.fn_getResourceRoleID('Super Billing Administrator');
	select @siteAdminGroupID = groupID from dbo.ams_groups where orgID = @orgID and groupCode = 'SiteAdmins';
	select @superAdminGroupID = groupID from dbo.ams_groups where orgID = 1 and groupCode = 'SuperAdmins';
	select @mcDevGroupID = groupID from dbo.ams_groups where orgID = 1 and groupCode = 'Developers';
	SET @superBillingAdminsGroupID = dbo.fn_getGroupIDFromGroupName(1,'Super Billing Admins');
	select @loginLimitModeID = loginLimitModeID from dbo.siteLoginLimitModes where loginLimitModeCode = 'Unlimited';
	SET @templateTypeID = dbo.fn_getTemplateTypeID('Page');
	SET @modeID = dbo.fn_getModeID('Normal');
	SET @sectionResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedSection');
	SET @sysCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('SystemCreatedContent');
	SET @superNetworkID = dbo.fn_getNetworkID('MemberCentral Super Administrators');
	SET @sysMemberID = dbo.fn_ams_getMCSystemMemberID();
	SET @platformWideNetworkID = dbo.fn_getNetworkID('Platform Wide');
	select @tier = tier from dbo.fn_getServerSettings();
	SET @nowDate = getdate();
	SET @subscriptionIssuesEmail = '<EMAIL>';

	BEGIN TRAN;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		-- add to sites
		INSERT INTO dbo.sites (orgID, sitecode, siteName, defaultLanguageID, defaultTimeZoneId, defaultCurrencyTypeID,
			allowGuestAccounts, forceLoginPage, useRemoteLogin, affiliationRequired, enforceSiteAgreement, immediateMemberUpdates, emailMemberUpdates, 
			defaultPostalState, joinURL, alternateGuestAccountCreationLink, alternateGuestAccountPopup, 
			alternateForgotPasswordLink, showCurrencyType, enableMobile, enableDeviceDetection, loginLimitModeID, defaultOrgIdentityID, loginOrgIdentityID, subscriptionIssuesEmail)
		VALUES (@orgID, @sitecode, @siteName, @defaultLanguageID, @defaultTimeZoneId, @defaultCurrencyTypeID, 
			@allowGuestAccounts, @forceLoginPage, @useRemoteLogin, @affiliationRequired, @enforceSiteAgreement, @immediateMemberUpdates, @emailMemberUpdates, 
			@defaultPostalState, @joinURL, @alternateGuestAccountCreationLink, @alternateGuestAccountPopup, 
			@alternateForgotPasswordLink, @showCurrencyType, 0, 0, @loginLimitModeID, @defaultOrgIdentityID, @defaultOrgIdentityID, @subscriptionIssuesEmail);
		SET @siteID = SCOPE_IDENTITY();

		-- add to siteFeatures with all defaults
		INSERT INTO dbo.siteFeatures (siteID)
		VALUES (@siteID);

		-- createSiteLanguage		
		EXEC dbo.createSiteLanguage @siteID=@siteID, @languageID=@defaultLanguageID;

		-- create a resourceID for the site
		exec dbo.cms_createSiteResource @resourceTypeID=@siteResourceTypeID, @siteResourceStatusID=1,
			@siteID=@siteid, @isVisible=1, @parentSiteResourceID=null, @siteResourceID=@siteResourceID OUTPUT;
	
		-- update site with new resource
		UPDATE dbo.sites
		SET siteResourceID = @siteResourceID
		WHERE siteID = @siteID;

		-- update org defaultSiteID
		UPDATE dbo.organizations
		SET defaultSiteID = @siteID
		WHERE orgID = @orgID
		AND defaultSiteID IS NULL;
		
		-- give clientAdmin Role to SiteAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@clientAdminRoleID, @groupID=@siteAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give accountingAdmin Role to siteAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@accountingAdminRoleID, @groupID=@siteAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give reportAdminRoleID Role to siteAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@reportAdminRoleID, @groupID=@siteAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give eventAdminRoleID Role to siteAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@eventAdminRoleID, @groupID=@siteAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give ContentEditor Role to siteAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@contentEditorRoleID, @groupID=@siteAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give siteAdmin Role to siteAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@siteAdminRoleID, @groupID=@siteAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give superAdmin Role to superAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@superAdminRoleID, @groupID=@superAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give sysOpsAdmin Role to developer Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@sysAdminRoleID, @groupID=@mcDevGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give superBillingAdmin to SuperBillingAdmin group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@superBillingAdminRoleID, @groupID=@superBillingAdminsGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- add default 4 part hostname for all environments
		insert into dbo.siteHostnames (siteID, hostname, environmentID, rootDomain, sslCertFilename,sslPrivateKeyFilename,internalIPAddress,hasSSL)
		select s.siteID, lower(s.sitecode + '.' + wh.wildcardHostname), e.environmentID, 
			dbo.fn_getRootDomainFromSiteHostName(wh.wildcardHostname),
			wh.sslCertFilename,wh.sslPrivateKeyFilename,wh.internalIPAddress, wh.hasSSL
		from dbo.platform_environments as e
		inner join dbo.platform_wildcardHostnames as wh on e.defaultWildcardHostnameID = wh.wildcardHostnameID
		inner join dbo.sites as s on s.siteID = @siteID;

		-- set siteEnvironment Defaults
		insert into dbo.siteEnvironments (siteID, environmentID, mainHostnameID, enableNGINXConf)
		select s.siteID, e.environmentID, sh.hostnameID, 1
		from dbo.sites as s
		inner join dbo.sitehostnames as sh on s.siteID = sh.siteID
			and s.siteID = @siteID
		inner join dbo.platform_wildcardHostnames as wh on wh.environmentID = sh.environmentID
		inner join dbo.platform_environments as e on e.defaultWildcardHostnameID = wh.wildcardHostnameID
		order by s.siteID, e.environmentID;

		select @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;

		select @searchJoinURL = 'http://' + sh.hostname
		from dbo.siteHostnames as sh 
		inner join dbo.siteEnvironments se on se.siteID = sh.siteID
			and se.environmentID = @environmentID
			and se.mainHostnameID = sh.hostNameID
			and sh.siteID = @siteID;

		-- add to depoTLA and memberCentralBilling if not exists
		IF @hasDepoTLA = 0 BEGIN
			INSERT INTO trialsmith.dbo.depoTLA ([state], [description], websitename, shortname, display, isLiveOnNewPlatform, 
				includeInTSEmailMarketing, includeInSWEmailMarketing, searchJoinURL)
			VALUES (@sitecode, @siteName, @siteName, @sitecode, -1, 1, 0, 0, @searchJoinURL);

			INSERT INTO trialsmith.dbo.memberCentralBilling (orgCode) VALUES (@sitecode);

			EXEC trialsmith.dbo.site_createDefaultBillingFeeSchedules @orgcode=@siteCode;
		END
		ELSE
			UPDATE trialsmith.dbo.depoTLA SET searchJoinURL = @searchJoinURL WHERE [state] = @sitecode;

		-- use default template
		select @templateID = templateID from dbo.cms_pageTemplates where siteID is null and templateName = 'MemberCentral Standard Template v1';

		-- add default page sections
		EXEC dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, 
			@ovTemplateID=@templateID, @ovTemplateIDMobile=null, @ovModeID=@modeID, @parentSectionID=null, 
			@sectionName='Root', @sectionCode='Root',@sectionBreadcrumb = null, @inheritPlacements=1, @sectionID=@sectionID OUTPUT;
		EXEC dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, 
			@ovTemplateID=null, @ovTemplateIDMobile=null, @ovModeID=null, @parentSectionID=@sectionID, 
			@sectionName='MCAMSMemberDocuments', @sectionCode='MCAMSMemberDocuments',@sectionBreadcrumb = null, @inheritPlacements=0, 
			@sectionID=@trashID OUTPUT;
		EXEC dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, 
			@ovTemplateID=null, @ovTemplateIDMobile=null, @ovModeID=null, @parentSectionID=@sectionID, 
			@sectionName='MCAMSEventDocuments', @sectionCode='MCAMSEventDocuments',@sectionBreadcrumb = null, @inheritPlacements=0, 
			@sectionID=@trashID OUTPUT;
		EXEC dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID,
			@ovTemplateID=null, @ovTemplateIDMobile=null, @ovModeID=null, @parentSectionID=@sectionID,
			@sectionName='MCAMSReportDocuments', @sectionCode='MCAMSReportDocuments',@sectionBreadcrumb = null, @inheritPlacements=0,
			@sectionID=@trashID OUTPUT;

		-- add default pages
		EXEC dbo.cms_createDefaultPages @siteid=@siteID, @sectionid=@sectionID, @languageID=@defaultLanguageID;

		-- set memberAdminSiteResourceID column after creating admin tool resources
		SELECT @memberAdminSiteResourceID = sr.siteResourceID
		FROM dbo.cms_siteResources AS sr
		INNER JOIN dbo.cms_siteResourceTypes AS srt ON srt.resourceTypeID = sr.resourceTypeID
			AND srt.resourceType = 'memberAdmin'
		WHERE sr.siteID = @siteID
		AND sr.siteResourceStatusID = 1;

		UPDATE dbo.sites
		SET memberAdminSiteResourceID = @memberAdminSiteResourceID
		WHERE siteID = @siteID;

		-- default fieldsets - Needs to be run after admin has been created.
		EXEC dbo.cms_createDefaultFieldsets @siteid=@siteID;

		-- add default History (notes) Categories
		EXEC dbo.cms_createDefaultHistoryCategories @siteid=@siteID, @contributingMemberID=@sysMemberID;

		-- add default email template trees
		EXEC dbo.cms_createDefaultEmailTemplateCategories @siteid=@siteID, @contributingMemberID=@sysMemberID;

		-- add default advance formulas
		EXEC dbo.cms_createDefaultAdvanceFormulas @siteid=@siteID;		

		-- add to networks
		EXEC dbo.createNetworkSite @networkID=@mainNetworkID, @siteID=@siteID, @isLoginNetwork=@isLoginNetwork, @isMasterSite=@isMasterSite;
		EXEC dbo.createNetworkSite @networkID=@platformWideNetworkID, @siteID=@siteID, @isLoginNetwork=0, @isMasterSite=0;

		-- add content objects
		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID, 
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1, 
			@languageID=1, @isActive=1, @contentTitle='NoRights', @contentDesc=null, @rawContent=@norightsContent, 
			@memberID=NULL, @contentID=@newContentID OUTPUT, @siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET noRightsContentID = @newContentid where siteID = @siteID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID,
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1, 
			@languageID=1, @isActive=1, @contentTitle='NoRightsNotLoggedIn', @contentDesc=null, 
			@rawContent=@norightsNotLoggedInContent, @memberID=NULL, @contentID=@newContentID OUTPUT, 
			@siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET noRightsNotLoggedInContentID = @newContentid where siteID = @siteID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID,
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1,
			@languageID=1, @isActive=1, @contentTitle='InactiveUser', @contentDesc=null,
			@rawContent=@inactiveUserContent, @memberID=NULL, @contentID=@newContentID OUTPUT,
			@siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET InactiveUserContentID = @newContentid where siteID = @siteID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID,
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1,
			@languageID=1, @isActive=1, @contentTitle='SiteAgreement', @contentDesc=null,
			@rawContent=@siteagreementContent, @memberID=NULL, @contentID=@newContentID OUTPUT,
			@siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET SiteAgreementContentID = @newContentid where siteID = @siteID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID,
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1,
			@languageID=1, @isActive=1, @contentTitle='Welcome Message', @contentDesc=null,
			@rawContent=@welcomeMessageContent, @memberID=NULL, @contentID=@newContentID OUTPUT,
			@siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET welcomeMessageContentID = @newContentid where siteID = @siteID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID,
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1,
			@languageID=1, @isActive=1, @contentTitle='First Time Login Message', @contentDesc=null,
			@rawContent=@firstTimeLoginContent, @memberID=NULL, @contentID=@newContentID OUTPUT,
			@siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET firstTimeLoginContentID = @newContentid where siteID = @siteID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID, 
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1, 
			@languageID=1, @isActive=1, @contentTitle='Initial Visit Warning', @contentDesc=null, 
			@rawContent='', @memberID=NULL, @contentID=@newContentID OUTPUT, @siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET HomePageWarningContentID = @newContentid where siteID = @siteID;

		SET @customHeadContent = '<meta name="viewport" content="width=device-width, initial-scale=1">';
		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID, 
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1, 
			@languageID=1, @isActive=1, @contentTitle='CustomHead', @contentDesc=null, 
			@rawContent=@customHeadContent, @memberID=NULL, @contentID=@newContentID OUTPUT, @siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET CustomHeadContentID = @newContentid where siteID = @siteID;

		-- link up superusers to all new sites
		INSERT INTO dbo.ams_memberNetworkProfiles (memberID, profileID, [status], dateCreated, siteID)
		SELECT distinct mnp.memberID, mnp.profileID, 'A', @nowDate, @siteID
		FROM dbo.ams_memberNetworkProfiles AS mnp 
		INNER JOIN dbo.ams_networkProfiles AS np ON mnp.profileID = np.profileID
		WHERE mnp.status = 'A'
		AND np.networkID = @superNetworkID
		AND np.status = 'A'
		AND mnp.siteID <> @siteID;

		-- enable site-specific passwords
		EXEC dbo.enableSiteFeature_sitePasswords @siteID=@siteID;

		-- add API token
		select @mcapiSecret = mcapiSecret from dbo.fn_getServerSettings();
		EXEC dbo.api_createLogin @siteID=@siteID, @nickname='Internal Use Only', @rmIDList=NULL, @ovPassword=@mcapiSecret,
			@isSystemToken=1, @username=@apiusername OUTPUT, @password=@apipassword OUTPUT;

		-- add default post type
		INSERT INTO dbo.cms_postTypes (siteID, typeName, typeUID, singularName, pluralName, description, dateCreated, dateUpdated, createdByMemberID)
		VALUES (@siteID, 'Articles', NEWID(), 'Article', 'Articles', NULL, @nowDate, @nowDate, @sysMemberID);

		-- add global opt-out consent list
		SELECT @consentListModeID = consentListModeID FROM platformMail.dbo.email_consentListModes WHERE modeName = 'GlobalOptOut';
		EXEC platformMail.dbo.email_addConsentListType @orgID=@orgID, @consentListTypeName='Global Lists', @isSystemType=1, 
			@consentListTypeID=@consentListTypeID OUTPUT;
		IF NOT EXISTS (SELECT TOP 1 consentListID FROM platformMail.dbo.email_consentLists WHERE consentListTypeID = @consentListTypeID AND consentListName = 'Global Opt-Out List')
			EXEC platformMail.dbo.email_addConsentList @siteID=@siteID, @consentListTypeID=@consentListTypeID, 
				@consentListName='Global Opt-Out List', @consentListDesc='You are currently on the global opt-out list.',
				@consentListModeID=@consentListModeID, @orgIdentityID=@defaultOrgIdentityID, @isHidden=1, 
				@enteredByMemberID=@sysMemberID, @consentListID=@consentListID OUTPUT;

		-- add general communications opt-out consent list
		select @generalCommunicationsConsentListTypeName = organizationShortName + ' Communications'
		from dbo.orgIdentities
		where orgIdentityID = @defaultOrgIdentityID;

		SET @consentListTypeID = null;
		EXEC platformMail.dbo.email_addConsentListType @orgID=@orgID, @consentListTypeName=@generalCommunicationsConsentListTypeName, @isSystemType=0, 
			@consentListTypeID=@consentListTypeID OUTPUT;
		IF NOT EXISTS (SELECT TOP 1 consentListID FROM platformMail.dbo.email_consentLists WHERE consentListTypeID = @consentListTypeID AND consentListName = 'General') BEGIN
			SELECT @consentListModeID = consentListModeID FROM platformMail.dbo.email_consentListModes WHERE modeName = 'Opt-Out';
			EXEC platformMail.dbo.email_addConsentList @siteID=@siteID, @consentListTypeID=@consentListTypeID, 
				@consentListName='General', @consentListDesc='General news and information',
				@consentListModeID=@consentListModeID, @orgIdentityID=@defaultOrgIdentityID, @isHidden=0, 
				@enteredByMemberID=@sysMemberID, @consentListID=@consentListID OUTPUT;
		END

		-- update site with consentListID
		UPDATE dbo.sites
		SET defaultConsentListID = @consentListID
		WHERE siteID = @siteID;

		-- add sendgrid subuser
		EXEC platformMail.dbo.sendgrid_createSubusers @siteID=@siteID, @siteCode=@siteCode;

		-- create Login Policy for Site Admin group
		SET @recCountOfFactors = CASE WHEN @tier = 'Production' THEN 2 ELSE 1 END;
		SELECT @loginLimitModeID = loginLimitModeID FROM dbo.siteLoginLimitModes WHERE loginLimitModeCode = 'SingleTest';

		EXEC dbo.site_createLoginPolicy
			@siteID=@siteID,
			@policyName='Site Admin Security',
			@policyCode='siteadminsecurity',
			@isMCStaffControlled=1,
			@loginLimitModeID=@loginLimitModeID,
			@maxDaysBetweenVerifications=14,
			@complianceDeadline=@nowDate,
			@complianceDaysForNewAccounts=2,
			@reqCountOfFactors=1,
			@recCountOfFactors=@recCountOfFactors,
			@noOfDaysForRecCountPrompts=14,
			@loginPolicyID=@loginPolicyID OUTPUT;

		SELECT @policySRID = siteResourceID FROM dbo.siteLoginPolicies WHERE loginPolicyID = @loginPolicyID;

		INSERT INTO dbo.siteLoginPolicyVerificationMethods (loginPolicyID, siteID, verificationMethodID, isRequired)
		SELECT @loginPolicyID, @siteID, verificationMethodID, isRequired = CASE methodCode WHEN 'Email' THEN 1 ELSE  0 END
		FROM dbo.platform_verificationMethods
		WHERE (@tier = 'Production' AND methodCode IN ('MFATOTP','MFASMS','Email'))
		OR (@tier <> 'Production' AND methodCode IN ('MFASMS'))

		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@policySRID, @include=1, 
			@functionIDList=@qualifyFID, @roleID=NULL, @groupID=@siteAdminGroupID, @inheritedRightsResourceID=NULL,
			@inheritedRightsFunctionID=NULL;


		-- add default soliciation message
		INSERT INTO dbo.tr_solicitationMessages (siteID, title, message)
		VALUES (@siteID, 'Voluntary Processing Fee Donation', 'Please help us recover our processing fees for accepting your payment.');
	COMMIT TRAN;

	EXEC dbo.cms_populateSiteResourceRightsCache @siteID=@siteID;

	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
