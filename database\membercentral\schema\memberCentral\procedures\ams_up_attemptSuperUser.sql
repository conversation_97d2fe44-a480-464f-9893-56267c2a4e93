ALTER PROC dbo.ams_up_attemptSuperUser
@sessionID int,
@siteID int,
@username varchar(75),
@password varchar(100),
@loginAsMemberID int = NULL,
@result varchar(10) OUTPUT,
@memberID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @sf_sitePasswords bit;
	SELECT @sf_sitePasswords = sitePasswords FROM dbo.siteFeatures where siteID = @siteID;
	SET @result = '';

	IF @sf_sitePasswords = 1 BEGIN
		DECLARE @mnpPasswordSalt uniqueidentifier, @mnpPasswordHash binary(64), @testPasswordHash binary(64);

		-- get mnp based on username only
		SELECT @memberID = m.memberID, @mnpPasswordSalt = mnp.PasswordSalt, @mnpPasswordHash = mnp.PasswordHash
		FROM dbo.ams_networkProfiles as np
		INNER JOIN dbo.ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID
			AND mnp.siteID = @siteID
			AND mnp.username = @username
			AND mnp.[status] = 'A'
		INNER JOIN dbo.ams_members as m on m.orgID = 1 and m.memberID = mnp.memberID and m.[status] in ('A','I')
		WHERE np.networkID = 1
		AND np.[status] = 'A'
		AND mnp.PasswordSalt IS NOT NULL
		AND mnp.PasswordHash IS NOT NULL;

		IF @memberID IS NULL
			SET @result = 'fail';
		ELSE BEGIN
			IF @loginAsMemberID > 0 BEGIN
				IF @loginAsMemberID <> @memberID
					SET @result = 'fail';
				ELSE
					SET @result = 'pass';
			END ELSE BEGIN
				SET @testPasswordHash = HASHBYTES('SHA2_512', @password+CAST(@mnpPasswordSalt AS NVARCHAR(36)));
				IF @testPasswordHash <> @mnpPasswordHash
					SET @result = 'fail';
				ELSE
					SET @result = 'pass';
			END
		END

	END ELSE BEGIN
		
		IF @loginAsMemberID > 0
			SELECT @memberID = m.memberID
			FROM dbo.ams_networkProfiles as np
			INNER JOIN dbo.ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID
				AND mnp.siteID = @siteID
				AND mnp.[status] = 'A'
			INNER JOIN dbo.ams_members as m on m.orgID = 1 and m.memberID = mnp.memberID and m.[status] in ('A','I')
				AND m.memberID = @loginAsMemberID
			WHERE np.networkID = 1
			AND np.username = @username
			AND np.[status] = 'A';
		ELSE
			SELECT @memberID = m.memberID
			FROM dbo.ams_networkProfiles as np
			INNER JOIN dbo.ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID
				AND mnp.siteID = @siteID
				AND mnp.[status] = 'A'
			INNER JOIN dbo.ams_members as m on m.orgID = 1 and m.memberID = mnp.memberID and m.[status] in ('A','I')
			WHERE np.networkID = 1
			AND np.username = @username
			AND np.[password] = @password
			AND np.[status] = 'A';

		IF @memberID IS NULL
			SET @result = 'fail';
		ELSE
			SET @result = 'pass';
	END

	SET @memberID = ISNULL(@memberID,0);

	-- If failed login, dont log it yet. This is because we still check superusers first in all login attempts. No need to record all those failures.
	/*
	IF @result = 'fail'
		INSERT INTO platformStatsMC.dbo.ams_memberLoginsFailed (username, [password], siteID, statsSessionID, dateEntered, loginAsMemberID, isSuperUserLogin)
		VALUES (@username, @password, @siteID, @sessionID, getdate(), @loginAsMemberID, 1);
	*/

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO
