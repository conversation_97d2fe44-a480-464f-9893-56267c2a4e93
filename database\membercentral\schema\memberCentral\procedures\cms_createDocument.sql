ALTER PROC dbo.cms_createDocument
@siteID int,
@resourceTypeID int,
@siteResourceStatusID int,
@languageID int,
@sectionID int,
@contributorMemberID int,
@recordedByMemberID int,
@isActive bit,
@isVisible bit,
@docTitle varchar(200),
@docDesc varchar(max),
<AUTHOR>
@publicationDate datetime,
@fileName varchar(255),
@fileExt varchar(20),
@documentID int OUTPUT,
@documentVersionID int OUTPUT,
@documentSiteResourceID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteResourceID int, @SID int;

	-- ensure documentID is null (can be passed in)
	SELECT @documentVersionID = null, @documentID = null, @documentSiteResourceID = null;

	-- ensure active contributer memberid
	SELECT @contributorMemberID = activeMemberID from dbo.ams_members where memberID = @contributorMemberID;

	-- ensure the siteID/sectionID combo is valid
	SELECT TOP 1 @SID = sectionID FROM dbo.cms_pageSections WHERE siteID = @siteID AND sectionID = @sectionID;
	IF @SID IS NOT NULL
	EXEC dbo.cms_createDocumentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, @siteResourceStatusID=@siteResourceStatusID, 
			@sectionID=@sectionID, @languageID=@languageID, @contributorMemberID=@contributorMemberID, @recordedByMemberID=@recordedByMemberID, 
			@isActive=@isActive, @isVisible=@isVisible, @docTitle=@docTitle, @docDesc=@docDesc, @author=@author, @publicationDate=@publicationDate, 
			@fileName=@fileName, @fileExt=@fileExt, @documentID=@documentID OUTPUT, @documentVersionID=@documentVersionID OUTPUT, 
			@siteResourceID=@documentSiteResourceID OUTPUT;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
