ALTER PROC dbo.rpt_createReportDocument
@siteID INT,
@documentID INT,
@dateExpire DATETIME = NULL,
@reportID INT = NULL,
@documentUID UNIQUEIDENTIFIER OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @documentUID = NEWID();

	IF(@dateExpire IS NULL)
		SET @dateExpire = DATEADD(DAY, 7, GETDATE());

	INSERT INTO dbo.rpt_documents (siteID, [uid], documentID, dateExpire, reportID)
	VALUES (@siteid, @documentUID, @documentID, @dateExpire, @reportID);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
