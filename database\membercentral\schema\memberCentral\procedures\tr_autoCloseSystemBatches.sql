ALTER PROC dbo.tr_autoCloseSystemBatches
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @enteredByMemberID int, @dateToUse datetime, @orgID int, @closedStatusID int, @openStatusID int, 
		@openForModificationStatusID int, @exceptionBatchTypeID int;
	select @enteredByMemberID = dbo.fn_ams_getMCSystemMemberID();
	set @dateToUse = DATEADD(dd, DATEDIFF(dd,0,getdate()), 0);

    select @closedStatusID = statusID from dbo.tr_batchStatuses where status = 'closed';
    select @openStatusID = statusID from dbo.tr_batchStatuses where status = 'open';
    select @openForModificationStatusID = statusID from dbo.tr_batchStatuses where status = 'Open for Modification';
	select @exceptionBatchTypeID = batchTypeID from dbo.tr_batchTypes where batchType = 'Exceptions';

	IF OBJECT_ID('tempdb..#mcBatchesToClose') IS NOT NULL 
		DROP TABLE #mcBatchesToClose;
	IF OBJECT_ID('tempdb..#tmpMCPayECheckMPProfiles') IS NOT NULL 
		DROP TABLE #tmpMCPayECheckMPProfiles;
	CREATE TABLE #mcBatchesToClose (batchID int, statusID int);
	CREATE TABLE #tmpMCPayECheckMPProfiles (profileID int);

	select @orgID = min(orgID) from dbo.organizations;
	while @orgID is not null begin

		-- MCPayEcheck profiles
		INSERT INTO #tmpMCPayECheckMPProfiles (profileID)
		SELECT mp.profileID
		FROM dbo.mp_profiles AS mp
		INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
		INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID
		WHERE s.orgID = @orgID
		AND mp.[status] IN ('A','I')
		AND g.gatewayType = 'MCPayEcheck'
		AND g.isActive = 1;

		-- get batches that need to be closed
		INSERT INTO #mcBatchesToClose (batchID, statusID)
		SELECT b.batchID, b.statusID
		FROM dbo.tr_batches AS b
		LEFT OUTER JOIN #tmpMCPayECheckMPProfiles AS tmp ON tmp.profileID = b.payProfileID
		WHERE b.orgID = @orgID
		AND b.statusID IN (@openStatusID,@openForModificationStatusID)
		AND b.isSystemCreated = 1
		AND b.depositDate < @dateToUse
		AND ISNULL(b.batchCode,'') <> 'PENDINGPAYMENTS'
		AND tmp.profileID IS NULL;

		IF @@ROWCOUNT > 0 BEGIN
			BEGIN TRAN;
				-- record status history
				INSERT INTO dbo.tr_batchStatusHistory (batchID, updateDate, statusID, oldStatusID, enteredByMemberID)
				select batchID, getdate(), @closedStatusID, statusID, @enteredByMemberID
				FROM #mcBatchesToClose;
				
				-- close batch
				update b
				set b.statusID = @closedStatusID
				from dbo.tr_batches as b
				inner join #mcBatchesToClose as tmp on tmp.batchID = b.batchID
				where b.orgID = @orgID;
			COMMIT TRAN;
		END

		TRUNCATE TABLE #tmpMCPayECheckMPProfiles;
		TRUNCATE TABLE #mcBatchesToClose;

		select @orgID = min(orgID) from dbo.organizations where orgID > @orgID;
	end

	IF OBJECT_ID('tempdb..#mcBatchesToClose') IS NOT NULL 
		DROP TABLE #mcBatchesToClose;
	IF OBJECT_ID('tempdb..#tmpMCPayECheckMPProfiles') IS NOT NULL 
		DROP TABLE #tmpMCPayECheckMPProfiles;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
