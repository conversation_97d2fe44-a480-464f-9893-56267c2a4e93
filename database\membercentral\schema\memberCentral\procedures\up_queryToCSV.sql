ALTER PROC dbo.up_queryToCSV
@selectsql varchar(max),
@csvfilename varchar(400),
@returnColumns bit = 0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @tblSuffix varchar(36) = replace(cast(NEWID() as varchar(36)),'-','');
	declare @tblName varchar(41) = '##csv' + @tblSuffix;

	-- @selectsql must have a *FROM* clause (with asterisks) which tells us where to inject the into clause. 
	-- parsing sql for "from" was problematic because it could exist in the select fields or in derived tables after the select
	IF isnull(CHARINDEX('*FROM*',@selectsql),0) = 0
		RAISERROR('*FROM* clause not specified in query.',16,1);

	-- @selectsql must have a mcCSVorder field which tells us the order by. It will not appear in the csv.
	IF isnull(CHARINDEX('mcCSVorder',@selectsql),0) = 0
		RAISERROR('CSV order not specified in query.',16,1);

	-- populate a global temp table from sql
	declare @combinedSQL varchar(max) = REPLACE(@selectsql, '*FROM*', ' INTO '+@tblName+' FROM ');
	set @selectsql = REPLACE(@selectsql, '*FROM*', 'FROM');
	EXEC(@combinedSQL);

	-- get metadata of the query to get final column names (needs to be nvarchar(max))
	declare @tblColumns TABLE (is_hidden bit, column_ordinal int, name sysname NULL, is_nullable bit, system_type_id int, system_type_name nvarchar(256) NULL,
		max_length smallint NULL, precision tinyint, scale tinyint, collation_name sysname NULL, user_type_id int NULL, user_type_database sysname NULL, 
		user_type_schema sysname NULL, user_type_name sysname NULL, assembly_qualified_type_name nvarchar(4000) NULL, xml_collection_id int NULL, 
		xml_collection_database sysname NULL, xml_collection_schema sysname NULL, xml_collection_name sysname NULL, is_xml_document bit, is_case_sensitive bit, 
		is_fixed_length_clr_type bit, source_server sysname NULL, source_database sysname NULL, source_schema sysname NULL, source_table sysname NULL, 
		source_column sysname NULL, is_identity_column bit NULL, is_part_of_unique_key bit NULL, is_updateable bit NULL, is_computed_column bit NULL, 
		is_sparse_column_set bit NULL, ordinal_in_order_by_list smallint NULL, order_by_list_length smallint NULL, order_by_is_descending smallint NULL, 
		tds_type_id int, tds_length int, tds_collation_id int NULL, tds_collation_sort_id tinyint NULL);
	declare @sqln nvarchar(max) = @selectsql;
	INSERT INTO @tblColumns
	EXEC dbo.sp_describe_first_result_set @tsql=@sqln;

	-- get select clause for bcp statement based on final column names in the order specified (loop to guarantee order)
	-- do not include mcCSVorder
	declare @columnOrder int, @columnName sysname, @selectClause varchar(max) = '', @headerRow varchar(max) = '';
	select @columnOrder = min(column_ordinal) from @tblColumns where [name] <> 'mcCSVorder';
	while @columnOrder is not null begin
		select @columnName = [name] from @tblColumns where column_ordinal = @columnOrder;
		set @selectClause = @selectClause + ',' + quotename(@columnName);
		set @headerRow = @headerRow + ',"' + @columnName + '"';
		select @columnOrder = min(column_ordinal) from @tblColumns where [name] <> 'mcCSVorder' and column_ordinal > @columnOrder;
	end
	set @selectClause = substring(@selectClause,2,len(@selectClause));
	set @headerRow = substring(@headerRow,2,len(@headerRow));

	-- bcp out the data as chr(31) column delimiter and chr(30) row delimiter
	-- total select statement cannot be more than 7900 chars to fit on the bcp statement. 
	-- if it is larger, create a proc and exec that proc instead.
	declare @bcpcmd varchar(8000), @isProcCreated bit = 0;
	set @combinedSQL = 'select ' + @selectClause + ' from ' + @tblName + ' order by mcCSVorder';
	IF len(@combinedSQL) > 7900 BEGIN
		DECLARE @spProcsql nvarchar(MAX), @UseAndExecStatement nvarchar(MAX);
		SET @UseAndExecStatement = 'use dataTransfer; exec sp_executesql @spProcsql;';
		SET @spProcsql = N'CREATE PROC dbo.queryToCsv_' + @tblSuffix + ' AS BEGIN 
			' + @combinedSQL + '; 
			END';
		EXEC sp_executesql @UseAndExecStatement, N'@spProcsql nvarchar(MAX)', @spProcsql=@spProcsql;

		SET @isProcCreated = 1;

		set @combinedSQL = 'EXEC dataTransfer.dbo.queryToCsv_' + @tblSuffix;
	END
	
	set @bcpcmd = 'bcp "' + @combinedSQL + '" queryout "' + @csvfilename + '" -C 65001 -c -t0x1F -r0x1E -T -S' + CAST(serverproperty('servername') as varchar(40));
	exec master..xp_cmdshell @bcpcmd, NO_OUTPUT;

	-- If we create a proc because statement is too long, drop the proc now
	IF @isProcCreated = 1 BEGIN
		SET @spProcsql = N'USE dataTransfer; DROP PROC dbo.queryToCsv_' + @tblSuffix + ';';
		EXEC sp_executesql @spProcsql;
	END

	-- read in file to replace beeps with commas, escape quotes, and replace nulls with empty string
	declare @tmpFile varchar(max), @trash bit;
	select @tmpFile = dbo.fn_ReadFile(@csvfilename,0,0);
	if len(@tmpFile) > 0 BEGIN
		set @tmpFile = replace(left(@tmpFile,len(@tmpFile)-1),'"','""'); -- escape all quotes
		set @tmpFile = replace(replace(@tmpFile,char(13),' '),char(10),' '); -- replace cr and lf with space
		set @tmpFile = replace(@tmpFile COLLATE SQL_Latin1_General_CP1_CS_AS,char(0),''); -- replace nulls with empty string
		set @tmpFile = replace(@tmpFile,char(31),'","');	-- replace 31 with column separator
		set @tmpFile = replace(@tmpFile,char(30),'"' + char(13) + char(10) + '"'); -- replace 30 with crlf
		set @tmpFile = '"' + @tmpFile + '"';
	end

	-- svae file with column header row
	select @trash = dbo.fn_WriteFile(@csvfilename,@headerRow + char(13) + char(10) + @tmpFile,1);

	-- get fields returned
	IF @returnColumns = 1
		SELECT [name] 
		FROM @tblColumns
		where [name] <> 'mcCSVorder'
		order by column_ordinal;

	IF OBJECT_ID('tempdb..'+@tblName) IS NOT NULL 
		EXEC('DROP TABLE ' + @tblName);
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF OBJECT_ID('tempdb..'+@tblName) IS NOT NULL 
		EXEC('DROP TABLE ' + @tblName);
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
