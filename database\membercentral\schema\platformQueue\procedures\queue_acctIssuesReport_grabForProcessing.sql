ALTER PROC dbo.queue_acctIssuesReport_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='acctIssuesReport', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;
	CREATE TABLE #tmpQueueItem (itemID int);

	-- dequeue in order of dateAdded
	UPDATE qid 
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItem
	FROM dbo.queue_acctIssuesReport AS qid
	INNER JOIN (
		SELECT TOP 1 itemID
		FROM dbo.queue_acctIssuesReport
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	-- org info
	SELECT qid.itemID, qid.orgID, o.orgcode, oi.organizationName as orgName, o.accountingEmail, s.sitecode
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_acctIssuesReport AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.organizations AS o ON o.orgID = qid.orgID
	INNER JOIN membercentral.dbo.orgIdentities AS oi ON oi.orgID = o.orgID 
		AND oi.orgIdentityID = o.defaultOrgIdentityID
	INNER JOIN membercentral.dbo.sites AS s ON s.siteID = o.defaultSiteID;

	-- open invoices
	SELECT qid.itemID, qid.orgID, ri.dateDue, ri.invoiceNumber, ri.memberID, 
		ri.firstName, ri.lastName, ri.memberNumber, ri.hasCard, ri.invDue
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_acctIssuesReport AS qid ON qid.itemID = tmp.itemID
	INNER JOIN datatransfer.dbo.tr_reportIssues_openInv AS ri ON ri.orgID = qid.orgID
	ORDER BY ri.lastName, ri.firstName, ri.memberNumber, ri.dateDue;

	-- non posted batches
	SELECT qid.itemID, qid.orgID, ri.batchID, ri.status, ri.batchName, 
		ri.depositDate, ri.profileName
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_acctIssuesReport AS qid ON qid.itemID = tmp.itemID
	INNER JOIN datatransfer.dbo.tr_reportIssues_nonPostedBatches AS ri ON ri.orgID = qid.orgID
	ORDER BY ri.depositDate, ri.batchName;

	-- out of order invoices
	SELECT qid.itemID, qid.orgID, ri.memberID, ri.memberName, ri.memberNumber
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_acctIssuesReport AS qid ON qid.itemID = tmp.itemID
	INNER JOIN datatransfer.dbo.tr_reportIssues_outOfOrderInv AS ri ON ri.orgID = qid.orgID
	ORDER BY ri.memberName, ri.memberNumber;

	-- inv prof alloc violations
	SELECT qid.itemID, qid.orgID, ri.dateRecorded, ri.memberName, ri.memberNumber, ri.allocAmount,
		ri.detail, ri.invoiceNumber, ri.invoiceProfileName, ri.payProfileName
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_acctIssuesReport AS qid ON qid.itemID = tmp.itemID
	INNER JOIN datatransfer.dbo.tr_reportIssues_invProfAllocViolations AS ri ON ri.orgID = qid.orgID
	ORDER BY ri.payProfileName, ri.memberName, ri.memberNumber, ri.invoiceNumber;

	-- flagged transactions
	SELECT qid.itemID, qid.orgID, ri.dateRecorded, ri.message, ri.memberID, ri.memberName
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_acctIssuesReport AS qid ON qid.itemID = tmp.itemID
	INNER JOIN datatransfer.dbo.tr_reportIssues_flaggedTransactions AS ri ON ri.orgID = qid.orgID
	ORDER BY ri.dateRecorded, ri.memberName;

	-- Non Surcharge Payment Profile
	SELECT qid.itemID, qid.orgID, ri.siteCode, ri.applicationTypeName, ri.title, ri.linkToAppAdmin
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_acctIssuesReport AS qid ON qid.itemID = tmp.itemID
	INNER JOIN datatransfer.dbo.tr_reportIssues_noNonSurchargePaymentProfile AS ri ON ri.orgID = qid.orgID
	ORDER BY ri.siteCode, ri.applicationTypeName, ri.title;


	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
