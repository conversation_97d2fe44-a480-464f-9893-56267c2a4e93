$(document).ready(function(){

  $(".btn-navbar").click(function(){
    $("body").toggleClass("overlay");
    });

  $(document).on('mouseenter mouseleave', '.navCollapse ul > li > ul > li', function(){
     if ($(this).children("ul").length !== 0 ) {
        $(this).toggleClass('dropdown');
      }
  });

  $(document).on('mouseenter mouseleave', '.navCollapse ul > li > ul > li > ul > li', function(){
     if ($(this).children("ul").length !== 0 ) {
        $(this).toggleClass('subdropdown');
      }
  });

  $(".Banner .owl-carousel").owlCarousel({
    items: 1,
    margin: 0,
    loop: true,
    autoplay: true,
    autoplayTimeout:7000,
    autoPlaySpeed: 1000,
    animateIn: 'fadeIn',
    animateOut: 'fadeOut',
    touchDrag: false,
    mouseDrag: false,
    dots:false,
    nav:true,
    navText: ["<img src='images/slideShowPrev.png'>","<img src='images/slideShowNext.png'>"]
  });

  if($(".innerPage-content .TitleText").length){
    $("#TitleTextHolder").html($(".innerPage-content .TitleText").eq(0).html());
  }
  $(".innerPage-content .TitleText").eq(0).remove();
  $(".innerPage-Holder").show();
  
});