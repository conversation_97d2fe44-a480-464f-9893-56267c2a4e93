<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getResourceTemplates" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"tt.templateTypeName")>
		<cfset arrayAppend(local.arrCols,"tf.templateFormat")>
		<cfset arrayAppend(local.arrCols,"t.templateName")>
		<cfset arrayAppend(local.arrCols,"t.dateCreated")>
		<cfset arrayAppend(local.arrCols,"cv.dateCreated")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

		<cfquery name="local.qryTemplates" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			IF OBJECT_ID('tempdb..##tmpTemplates') IS NOT NULL
				DROP TABLE ##tmpTemplates;
			CREATE TABLE ##tmpTemplates (templateID int, siteID int, templateTypeName varchar(50), templateFormat varchar(25), templateName varchar(50), 
				templateDesc varchar(300), dateCreated datetime, dateModified datetime, usageCount int, row int);

			DECLARE @siteID int, @resourceTypeID int, @posStartAndCount int, @posStart int, @searchValue varchar(300), @totalCount int;
			SET @siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="CF_SQL_INTEGER">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;

			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>
			
			SELECT @resourceTypeID = dbo.fn_getResourceTypeID(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('resourceType')#">);

			INSERT INTO ##tmpTemplates (templateID, siteID, templateTypeName, templateFormat, templateName, templateDesc,
				dateCreated, dateModified, usageCount, row)
			select t.templateID, t.siteID, tt.templateTypeName, tf.templateFormat, t.templateName, t.templateDesc, 
				t.dateCreated, cv.dateCreated as dateModified,
				(select count(templateUsageID) from dbo.template_usages	where templateID = t.templateID) as usageCount,
				ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row
			from dbo.template_templates as t
			inner join dbo.cms_siteResources as sr on sr.siteID = t.siteID
				and sr.siteResourceID = t.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.template_typeFormats as ttf on ttf.templateTypeFormatID = t.templateTypeFormatID
				and ttf.editorID = t.editorID
			inner join dbo.template_types as tt on tt.templateTypeID = ttf.templateTypeID
			inner join dbo.template_formats as tf on tf.templateFormatID = ttf.templateFormatID
				and tt.controllingSiteResourceTypeID = @resourceTypeID
			inner join dbo.cms_content as c on c.siteID = t.siteID
				and c.contentID = t.contentID
			inner join dbo.cms_contentLanguages as cl on cl.siteID = c.siteID
				and c.contentID = cl.contentid 
				and cl.languageID = 1
			inner join dbo.cms_contentVersions as cv on cv.siteID = cl.siteID
				and cv.contentID = cl.contentID
				and cv.contentLanguageID = cl.contentLanguageID 
				and cv.isActive = 1
			inner join dbo.template_libraries as tl on tl.libraryID = t.libraryID
			left outer join dbo.networks as n
				inner join dbo.networkSites ns on ns.networkID = n.networkID
					and ns.siteID = @siteID
				on n.networkID = tl.syndicationNetworkID
			where (t.siteID = @siteID or ns.siteID is not null)
			and t.libraryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('_rtlibraryID',0)#">
			<cfif len(local.searchValue)>
				and (tt.templateTypeName LIKE @searchValue OR t.templateName LIKE @searchValue OR t.templateDesc LIKE @searchValue)
			</cfif>;

			SET @totalCount = @@ROWCOUNT;

			SELECT templateID, siteID, templateTypeName, templateFormat, templateName, templateDesc,
				dateCreated, dateModified, usageCount, @totalCount as totalCount
			FROM ##tmpTemplates
			WHERE row > @posStart
			AND row <= @posStartAndCount
			ORDER by row;

			IF OBJECT_ID('tempdb..##tmpTemplates') IS NOT NULL
				DROP TABLE ##tmpTemplates;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.data = []>
		<cfloop query="local.qryTemplates">
			<cfset local.tmpStr = {
				"templateid": local.qryTemplates.templateID,
				"templatetypename": htmlEditFormat(local.qryTemplates.templateTypeName),
				"templateformat": htmlEditFormat(local.qryTemplates.templateFormat),
				"templatename": htmlEditFormat(local.qryTemplates.templateName),
				"templatedesc": htmlEditFormat(local.qryTemplates.templateDesc),
				"created": DateTimeFormat(local.qryTemplates.dateCreated,'m/d/yyyy h:nn tt'),
				"modified": DateTimeFormat(local.qryTemplates.dateModified,'m/d/yyyy h:nn tt'),
				"canedit": local.qryTemplates.siteID EQ arguments.event.getValue('mc_siteinfo.siteID'),
				"candelete": local.qryTemplates.siteID EQ arguments.event.getValue('mc_siteinfo.siteID') AND local.qryTemplates.usageCount EQ 0,
				"totalcount": local.qryTemplates.totalcount,
				"DT_RowId": "resourceTemplateRow_#local.qryTemplates.templateID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryTemplates.totalcount),
			"recordsFiltered": val(local.qryTemplates.totalcount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getCategories" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.catTreeID = arguments.event.getValue('categoryTreeID',0);
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		
		<cfquery name="local.qryCategories" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			IF OBJECT_ID('tempdb..##tmpCats') IS NOT NULL
				DROP TABLE ##tmpCats;

			CREATE TABLE ##tmpCats (categoryID int PRIMARY KEY, CategoryName varchar(200), parentCategoryID int, categoryPath varchar(max), 
				sortOrder int, treeOrder varchar(100), minParentSort int, maxParentSort int, catLevel int, hasChildren bit);
			
			DECLARE @categoryTreeID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.catTreeID#">;

			WITH Categories AS (
				SELECT c.categoryID, c.categoryName, c.parentCategoryID, c.categoryPath, c.sortOrder, c.treeOrder, 0 AS catLevel
				FROM dbo.cms_categories AS c
				INNER JOIN dbo.cms_categoryTrees AS ct ON ct.categoryTreeID = c.categoryTreeID
				INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ct.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID 
					AND srs.siteResourceStatusDesc = 'Active'
				WHERE c.categoryTreeID = @categoryTreeID
				AND c.isActive = 1
				AND c.parentCategoryID IS NULL

				UNION ALL

				SELECT c.categoryID, c.categoryName, c.parentCategoryID, c.categoryPath, c.sortOrder, c.treeOrder, cat.catLevel+1 as catLevel
				FROM dbo.cms_categories AS c
				INNER JOIN dbo.cms_categoryTrees AS ct ON ct.categoryTreeID = c.categoryTreeID
				INNER JOIN dbo.cms_siteResources AS sr 
					ON sr.siteResourceID = ct.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID 
					AND srs.siteResourceStatusDesc = 'Active'
				INNER JOIN Categories as cat ON c.parentCategoryID = cat.categoryID
				WHERE c.categoryTreeID = @categoryTreeID
				AND c.isActive = 1
			)
			INSERT INTO ##tmpCats (categoryID, categoryName, parentCategoryID, categoryPath, sortOrder, treeOrder, minParentSort, maxParentSort, catLevel)
			SELECT categoryID, categoryName, parentCategoryID, categoryPath, sortOrder, treeOrder, 0, 0, catLevel
			FROM Categories
			ORDER BY treeOrder;

			UPDATE tmp
			SET tmp.minParentSort = tmpAggr.minSort,
				tmp.maxParentSort = tmpAggr.maxSort
			FROM ##tmpCats AS tmp
			INNER JOIN (
				select parentCategoryID, min(sortOrder) as minSort, max(sortOrder) as maxSort
				from ##tmpCats
				group by parentCategoryID
			) as tmpAggr on isnull(tmpAggr.parentCategoryID,0) = isnull(tmp.parentCategoryID,0);

			UPDATE tmp1
			SET tmp1.hasChildren = CASE WHEN tmp2.categoryID IS NOT NULL THEN 1 ELSE 0 END
			FROM ##tmpCats AS tmp1
			INNER JOIN ##tmpCats AS tmp2 on tmp2.parentCategoryID = tmp1.categoryID;

			SELECT @categoryTreeID as categoryTreeID, tmp.categoryID, tmp.categoryName, tmp.parentCategoryID, tmp.categoryPath, tmp.sortOrder,
				tmp.treeOrder, tmp.listFirstItem, tmp.listLastItem, tmp.resCount, tmp.catLevel, tmp.hasChildren
			FROM (
				SELECT tmp.categoryID, tmp.categoryName, isnull(tmp.parentCategoryID,0) as parentCategoryID, tmp.categoryPath, tmp.sortOrder, tmp.treeOrder,
					CASE WHEN tmp.sortOrder = tmp.minParentSort then 1 else 0 end as listFirstItem,
					CASE WHEN tmp.sortOrder = tmp.maxParentSort then 1 else 0 end as listLastItem,
					COUNT(distinct sr.siteResourceID) as resCount, tmp.catLevel, tmp.hasChildren
				FROM ##tmpCats AS tmp
				LEFT OUTER JOIN dbo.cms_categorySiteResources AS csr 
					INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = csr.siteResourceID 
					INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID AND srs.siteResourceStatusDesc = 'Active'
					ON csr.categoryID = tmp.categoryID
				GROUP BY tmp.categoryID, tmp.categoryName, tmp.parentCategoryID, tmp.categoryPath, tmp.sortOrder, tmp.treeOrder, tmp.minParentSort, tmp.maxParentSort, tmp.catLevel, tmp.hasChildren
			) AS tmp
			ORDER BY tmp.treeOrder;

			IF OBJECT_ID('tempdb..##tmpCats') IS NOT NULL
				DROP TABLE ##tmpCats;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryCategories">
			<cfset>
			<cfset local.tmpStr = {
				"categorytreeid": local.qryCategories.categoryTreeID,
				"categoryid": local.qryCategories.categoryID,
				"categoryname": local.qryCategories.categoryName,
				"parentcategoryid": local.qryCategories.parentCategoryID,
				"canmoveup": local.qryCategories.listFirstItem neq 1,
				"canmovedown": local.qryCategories.listLastItem neq 1,
				"candelete": local.qryCategories.resCount eq 0,
				"categorylevel": local.qryCategories.catLevel,
				"treeorder": local.qryCategories.treeOrder,
				"haschildren": local.qryCategories.hasChildren,
				"DT_RowId": "mcrt_category_#local.qryCategories.categoryTreeID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryCategories.recordCount),
			"recordsFiltered": val(local.qryCategories.recordCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getPreviousResourceTemplateVersions" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"cv.dateCreated")>
		<cfset arrayAppend(local.arrCols,"mActive.firstName + mActive.lastName")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

		<cfquery name="local.qryTemplateVersions" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpTemplateVersions') IS NOT NULL
				DROP TABLE ##tmpTemplateVersions;
			CREATE TABLE ##tmpTemplateVersions (contentVersionID int, dateCreated datetime, memberName varchar(151), row int);

			DECLARE @siteID int, @resourceTypeID int, @templateID int, @posStart int, @posStartAndCount int, @totalCount int;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
			SELECT @resourceTypeID = dbo.fn_getResourceTypeID(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('resourceType','')#">);
			SET @templateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('templateID'))#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;

			INSERT INTO ##tmpTemplateVersions (contentVersionID, dateCreated, memberName, row)
			select cv.contentVersionID, cv.dateCreated, mActive.firstName + ' ' + mActive.lastname,
				ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row
			from dbo.template_templates as t
			inner join dbo.cms_siteResources as sr on t.siteID = sr.siteID
				and sr.siteResourceID = t.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.template_typeFormats as ttf on ttf.templateTypeFormatID = t.templateTypeFormatID
				and ttf.editorID = t.editorID
			inner join dbo.template_types as tt on tt.templateTypeID = ttf.templateTypeID
			inner join dbo.template_formats as tf on tf.templateFormatID = ttf.templateFormatID
				and tt.controllingSiteResourceTypeID = @resourceTypeID
			inner join dbo.cms_content as c on c.siteID = t.siteID
				and c.contentID = t.contentID 
			inner join dbo.cms_contentLanguages as cl on cl.siteID = c.siteID
				and cl.contentID = c.contentID 
				and cl.languageID = 1
			inner join dbo.cms_contentVersions as cv on cv.siteID = cl.siteID
				and cv.contentID = cl.contentID
				and cv.contentLanguageID = cl.contentLanguageID
				and cv.isActive = 0
			inner join dbo.ams_members as m on m.memberID = cv.contributorMemberID
			inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID 
			where t.siteID = @siteID
			and t.templateID = @templateID;

			SET @totalCount = @@ROWCOUNT;

			SELECT contentVersionID, dateCreated, memberName, @totalCount as totalCount
			FROM ##tmpTemplateVersions
			WHERE row > @posStart
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpTemplateVersions') IS NOT NULL
				DROP TABLE ##tmpTemplateVersions;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.data = []>
		<cfloop query="local.qryTemplateVersions">
			<cfset local.tmpStr = {
				"contentversionid": local.qryTemplateVersions.contentVersionID,
				"datecreated": DateTimeFormat(local.qryTemplateVersions.dateCreated,'m/d/yyyy h:nn tt'),
				"membername": encodeForHTML(local.qryTemplateVersions.memberName),
				"DT_RowId": "templateVersionRow_#local.qryTemplateVersions.contentVersionID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryTemplateVersions.totalcount),
			"recordsFiltered": val(local.qryTemplateVersions.totalcount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

</cfcomponent>