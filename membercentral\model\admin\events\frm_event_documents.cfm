<cfsavecontent variable="local.registrantJS">
	<cfoutput>
	<script language="javascript">
	let evDocumentsTable;

	function initializeDocumentsTable(){
		evDocumentsTable = $('##evDocumentsTable').DataTable({
			"processing": true,
			"serverSide": true,
			"paging": false,
			"info": false,
			"language": {
				"emptyTable": "<div class='mt-2 alert alert-warning'>No eventDocuments"
			},
			"ajax": { 
				"url": '#local.eventDocumentsLink#',
				"type": "post",
				"data": function(d) { 
						if (window.reorderData && window.reorderData.length > 0) { 
							d.reorderData = JSON.stringify(window.reorderData); 
							window.reorderData = [];
						} 
						return d; 
					}
			},
			"autoWidth": false,
			"columns": [
				{
					"data": null,
					"render": function (data, type) {
						let renderData = '';
						if (type === 'display') {
							if (data.rowType == 'eventDocument') {
								renderData += '<div class="row-drag-handle"><i class="fa-light fa-bars"></i></div>';
							}
						}
						return type === 'display' ? renderData : data;
					},
					"orderable": false
				},
				{ 
					"data": null,
					"render": function ( data, type, row, meta ) {
						let thisRowID = data['DT_RowId'];
						let renderData = '';
						if (type === 'display')	{
							let cellPadding = data.level > 1 ? 24 * (data.level-1) : 0;
							let rowTypeIcon = '';
							renderData += '<div style="padding-left:'+cellPadding+'px;">';
							if (data.hasChildren) {
								renderData += '<a href="javascript:toggleDocumentParentDisplay(\''+thisRowID+'\');" id="displayLabel_'+thisRowID+'"><i class="'+ (data.rowType == 'eventDocumentGroup' ? 'fas fa-folder-minus' : 'far fa-minus-square') +' fa-fw rowToggleBtn pr-2"></i> '+ rowTypeIcon + data.displayName+'</a>';
							} else {
								renderData += '<span><i class="fas fa-folder-tree fa-fw pr-2 invisible"></i> '+ rowTypeIcon + data.displayName+'</span>';
							}
							
							renderData += '</div>';
						}
						return type === 'display' ? renderData : data;
					},
					"width": "55%",
					"className": "align-top"
				},
				{ 
					"data": null,
					"render": function ( data, type, row, meta ) {
						return type === 'display' ? (data.rowType == 'eventDocument' ? data.author : '') : data;
					},
					"width": "20%",
					"className": "align-top"
				},
				{ 
					"data": null,
					"render": function ( data, type, row, meta ) {
						let thisRowID = data['DT_RowId'];
						let renderData = '';
						if (type === 'display') {
							switch(data.rowType) {
								case "eventDocumentGroup":
									renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1'+(data.eventDocumentGroupingID > 0 ? "" : " invisible")+'" onclick="editDocumentGrouping('+data.eventDocumentGroupingID+');return false;" title="Edit eventDocument Grouping"><i class="fas fa-pencil"></i></a>';
									renderData += '<a href="##" class="btn btn-xs px-1 m-1 invisible"><i class="fa-solid fa-pencil"></i></a>';
									renderData += '<a href="##" class="btn btn-xs text-danger p-1 mx-1'+(data.eventDocumentGroupingID > 0 ? "" : " invisible")+'" onclick="removeDocumentGrouping('+data.eventDocumentGroupingID+');return false;" title="Remove eventDocument Grouping"><i class="fas fa-trash-alt"></i></a>';
									renderData += '<a href="##" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-copy"></i></a>';
									renderData += '<a href="##" class="btn btn-xs text-green p-1 mx-1 docRowMoveUp'+(data.eventDocumentGroupingID > 0 && data.canMoveUp ? "" : " invisible")+'" title="Move Document Grouping Up" onclick="moveEvDocGrp('+data.eventDocumentGroupingID+',\''+thisRowID+'\',\''+data.parentRowID+'\',\'up\');return false;"><i class="fas fa-arrow-up"></i></a>';
									renderData += '<a href="##" class="btn btn-xs text-green p-1 mx-1 docRowMoveDown'+(data.eventDocumentGroupingID > 0 && data.canMoveDown ? "" : " invisible")+'" title="Move Document Grouping Down" onclick="moveEvDocGrp('+data.eventDocumentGroupingID+',\''+thisRowID+'\',\''+data.parentRowID+'\',\'down\');return false;"><i class="fas fa-arrow-down"></i></a>';
									break;
								case "eventDocument":
									renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1" onclick="editDocument('+data.documentID+','+data.eventID+','+data.eventDocumentID+');return false;" title="Edit Document"><i class="fas fa-pencil"></i></a>';
									renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1" onclick="replaceEventFile('+data.documentID+','+data.eventID+','+data.eventDocumentID+');return false;" title="Replace Document"><i class="fas fa-arrow-right-arrow-left"></i></a>';
									renderData += '<a href="##" class="btn btn-xs text-danger p-1 mx-1" id="btnDelDoc'+data.documentID+'" onclick="confirmDeleteDocument('+data.documentID+','+data.eventID+','+data.eventDocumentID+');return false;" title="Remove eventDocument"><i class="fas fa-trash-alt"></i></a>';
									renderData += '<a href="'+data.previewLink+'" class="btn btn-xs text-info p-1 mx-1" target="_blank"><i class="fa-solid fa-eye"></i></a>';
									renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1 invisible"></a>';
									renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1 invisible"></a>';
									renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1 invisible"></a>';
									break;
							}
						}
						return type === 'display' ? renderData : data;
					},
					"className": "text-center align-top"
				}
			],
			"searching": false,
			"ordering": false,
			rowReorder: {
				dataSrc: "columnid",
				selector: '.row-drag-handle' 
			},
			createdRow: function (row, data, index) {
				$(row).attr('data-rowType', data['rowType']);
				$(row).attr('data-eventDocumentGroupingID', data['eventDocumentGroupingID']);
				if (data.rowType !== 'eventDocument') {
					$(row).find('.row-drag-handle').css('pointer-events', 'none');
				}
			},
			drawCallback: function( row, data, dataIndex ) {
			}
		});

		evDocumentsTable.on('row-reorder', function (e, diff, edit) {
			let orderData = [];
			let isValidReorder = true;
			const $draggedTr = $(edit.triggerRow.node()); /* jQuery object of dragged row*/
			let $eventDocumentGroupTr = $draggedTr.prevAll('tr[data-rowtype="eventDocumentGroup"]').first(); /* Find nearest above*/

			let nearesteventDocumentGroupIndex = -1;
			if ($eventDocumentGroupTr.length) {
				nearesteventDocumentGroupIndex = evDocumentsTable.row($eventDocumentGroupTr).index(); /* Get DataTable index*/
			}
			diff = diff.filter(function(item) {
				return item.node.getAttribute("data-rowtype") === "eventDocument";
			});

			diff.forEach(function (item) {
				let movedRow = evDocumentsTable.row(item.node).data();
				let oldIdx = item.oldPosition;
				let newIdx = item.newPosition;
		
				let newRow = evDocumentsTable.row(newIdx).data();			
				if (movedRow.eventDocumentGroupingID !== newRow.eventDocumentGroupingID) {
					isValidReorder = false;
					return;
				}
				orderData.push({
					id: movedRow.eventDocumentID,
					eventDocumentGroupingID: movedRow.eventDocumentGroupingID,
					newOrder: item.newPosition - nearesteventDocumentGroupIndex
				});
			});

			if (isValidReorder) {
				window.reorderData = orderData;
			} else {
				window.reorderData = [];
				evDocumentsTable.draw(false);
				alert("Event documents can only be dragged within the same document grouping.");
			}
		});
	}
	function toggleDocumentParentDisplay(rowID) {
		let rowType = $('##evDocumentsTable ##'+rowID).attr('data-rowType');
		let rowToggleBtn = $('##evDocumentsTable ##displayLabel_'+rowID+' i.rowToggleBtn');
		rowToggleBtn.toggleClass(rowType == 'eventDocumentGroup' ? 'fa-folder-plus fa-folder-minus' : 'fa-plus-square fa-minus-square');
		let showChildren = rowToggleBtn.hasClass(rowType == 'eventDocumentGroup' ? 'fa-folder-minus' : 'fa-minus-square');
		toggleDocumentChildRows(rowID,showChildren);
	}
	function toggleDocumentChildRows(rowID,f) {
		$('##evDocumentsTable tr.child-of-'+rowID).toggleClass('d-none',!f).each(function(i,thisRow) {
			let expandedIconClass = $(this).attr('data-rowType') == 'eventDocumentGroup' ? 'fa-folder-minus' : 'fa-minus-square';
			if ($(this).find('i.rowToggleBtn').hasClass(expandedIconClass)) toggleDocumentChildRows($(this).attr('id'),f);
		});
	}
	function reloadDocumentsTable(){
		evDocumentsTable.draw();
	}
	function editDocument(documentID,eventID,evdid) {
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: evdid > 0 ? 'Edit Event Document' : 'Add Event Document',
			iframe: true,
			contenturl: '#this.link.editDocument#&documentID=' + documentID + '&eventID=' + eventID + '&evdid=' + evdid,
			strmodalfooter : {
				classlist: 'text-right',
				showclose: false,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmDocument :submit").click',
				extrabuttonlabel: 'Save Document',
			}
		});
	} 
	function replaceEventFile(documentID,eventID,evdid) {
		let title = 'Replace Event File';
		let replaceEventFileLink = '#this.link.replaceEventFile#&evdid=' + evdid + '&did=' + documentID + '&eid=' + eventID;
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: title,
			iframe: true,
			contenturl: replaceEventFileLink,
			strmodalfooter: {
				classlist: 'd-none'
			}
		});
	}
	function confirmDeleteDocument(documentID,eventID,evdid) {
		var removeDocumentResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				reloadDocumentsTable();
			} else {
				alert('We were unable to remove this event document.');
				delBtnElement.attr('data-confirm',0).removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			}
		};

		let delBtnElement = $('##btnDelDoc'+documentID);
		mca_initConfirmButton(delBtnElement, function(){
			let objParams = { eventID:eventID, eventDocumentID:evdid, documentID:documentID, eventSRID:#this.siteResourceID# };
			TS_AJX('ADMINEVENT','deleteEventDocument',objParams,removeDocumentResult,removeDocumentResult,10000,removeDocumentResult);		
		});
	}
	function removeDocumentGrouping(evDocGrpID) {
		MCModalUtils.showModal({
			verticallycentered: true,
			size: 'md',
			title: 'Remove Document Grouping',
			strmodalbody: {
				content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want to remove this document grouping?<br/>Any documents in this event document grouping will be ungrouped.</span></div>',
			},
			strmodalfooter : {
				classlist: 'text-right',
				showclose: false,
				showextrabutton: true,
				extrabuttonclass: 'btn-outline-danger',
				extrabuttonlabel: 'Remove Document Grouping',
			}
		});

		$('##btnMCModalSave').on('click', function(){
			doRemoveDocumentGrouping(evDocGrpID);
		});
	}
	function doRemoveDocumentGrouping(evDocGrpID) {
		var removeDocumentGroupingResult = function(r) {
			MCModalUtils.hideModal();
			if (r.success && r.success.toLowerCase() == 'true'){
				reloadDocumentsTable();
			} else {
				alert('We were unable to remove this event document grouping.');
			}
		};
		$('##btnMCModalSave').prop('disabled',true).html('Removing...');
		var objParams = { eventDocumentGroupingID:evDocGrpID, eventID:#arguments.event.getValue('eID')# };
		TS_AJX('ADMINEVENT','deleteEventDocumentGrouping',objParams,removeDocumentGroupingResult,removeDocumentGroupingResult,10000,removeDocumentGroupingResult);
	}
	function moveEvDocGrp(evDocGrpID,rowID,pRowID,dir) {
		var moveResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				moveDocumentsGridRow(rowID,pRowID,dir);
			}
			else {
				alert('We were unable to move this event document grouping.');
			}
		};
		var objParams = { eventDocumentGroupingID:evDocGrpID, eventID:#arguments.event.getValue('eID')#, dir:dir };
		TS_AJX('ADMINEVENT','doEventDocGroupingMove',objParams,moveResult,moveResult,10000,moveResult);
	}
	function moveDocumentsGridRow(rowID,pRowID,dir) {
		let movingRow, targetRow;

		if(dir == 'up'){
			movingRow = $('##evDocumentsTable ##'+rowID);
			targetRow = movingRow.closest('tr').prevAll('tr.child-of-'+pRowID+':first');
		}
		else {
			movingRow = $('##evDocumentsTable ##'+rowID).closest('tr').nextAll('tr.child-of-'+pRowID+':first'); /*next row will be moved to top*/
			targetRow = $('##evDocumentsTable ##'+rowID);
		}

		let movingRowID = movingRow.attr('id');
		movingRow.addClass('moveRow-' + movingRowID);
		markAssocDocRows(movingRowID,movingRowID);

		let arrMoveRows = $('##evDocumentsTable tr.moveRow-'+movingRowID);
		arrMoveRows.remove().insertBefore(targetRow);
		$('##evDocumentsTable tr.moveRow-'+movingRowID).removeClass('moveRow-' + movingRowID);
		resetDocumentRows(pRowID);
	}
	function resetDocumentRows(pRowID) {
		let childRows = $('##evDocumentsTable tr.child-of-'+pRowID).not('.default-nogrouping');
		if (childRows.length > 1) {
			childRows.find('a.docRowMoveUp,a.docRowMoveDown').removeClass('invisible');
			childRows.find('a.docRowMoveUp').first().addClass('invisible');
			childRows.find('a.docRowMoveDown').last().addClass('invisible');
		} else {
			childRows.find('a.docRowMoveUp,a.docRowMoveDown').addClass('invisible');
		}
	}
	function markAssocDocRows(parentRowID,moveRowID) {
		let childRows = $('##evDocumentsTable tr.child-of-'+parentRowID);
		if (childRows.length) {
			childRows.addClass('moveRow-' + moveRowID).each(function() {
				markAssocDocRows($(this).attr('id'),moveRowID);
			});
		}
	}
	function editDocumentGrouping(evDocGrpID){
		mca_hideAlert('err_docgrouping');
		var editDocGrpResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				let evDocGrpFrmTemplate = Handlebars.compile($('##evDocGroupingFormTemplate').html());
				$('##MCModalBody').html(evDocGrpFrmTemplate({eventDocumentGroupingID:evDocGrpID,eventDocumentGrouping:r.eventdocumentgrouping}));
			} else {
				alert('We were unable to load event document grouping form. Try again.')
			}
		};

		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Edit Event Document Grouping',
			strmodalfooter : {
				classlist: 'd-flex',
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: 'saveEventDocGrouping',
				extrabuttonlabel: 'Save',
			}
		});

		var objParams = { eventDocumentGroupingID:evDocGrpID };
		TS_AJX('ADMINEVENT','getEventDocumentGroupingName',objParams,editDocGrpResult,editDocGrpResult,10000,editDocGrpResult);
	}
	function saveEventDocGrouping() {
		mca_hideAlert('err_docgrouping');
		var saveResult	= function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				reloadDocumentsTable();
				MCModalUtils.hideModal();
			} else {
				mca_showAlert('err_docgrouping', (r.errmsg && r.errmsg.length ? r.errmsg : 'We were unable to save this event document grouping. Try again.'));
				$('##btnMCModalSave').prop('disabled',false).html('Save');
			}
		};
		if($('##eventDocumentGrouping').val().trim()!=''){
			$('##btnMCModalSave').prop('disabled',true).html('Saving...');
			var objParams = { eventDocumentGroupingID:$('##eventDocumentGroupingID').val(),
				eventID:#arguments.event.getValue('eID')#, eventDocumentGrouping:$('##eventDocumentGrouping').val().trim() };
			TS_AJX('ADMINEVENT','saveEventDocumentGrouping',objParams,saveResult,saveResult,10000,saveResult);
		} else {
			mca_showAlert('err_docgrouping', 'Enter a name for this Document Grouping.');
			return false;
		}
	}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.registrantJS)#">

<cfoutput>
<div class="row mb-1">
	<div class="col">
		This tab allows you to upload documents that will be shared with event attendees, such as event materials for download. 
		Multiple files can be added, ordered, and grouped however you like.
		Only registrants will be allowed to download the files via confirmation emails or the event details page.
	</div>
</div>
<div class="row mb-1">
	<div class="col text-right">
		<button type="button" class="btn btn-sm btn-primary" onclick="editDocument(0,#arguments.event.getValue('eID')#,0);">
			<span class="btn-wrapper--icon">
			<i class="fa-regular fa-circle-plus"></i>
			</span>
			<span class="btn-wrapper--label">Add Document</span>
		</button>
	</div>
</div>
<table id="evDocumentsTable" class="table table-sm table-bordered" style="width:100%">
	<thead>
		<tr>
			<th id="columnid"></th>
			<th>Document Title</th>
			<th>Author</th>
			<th>Actions</th>
		</tr>
	</thead>
</table>

<script id="evDocGroupingFormTemplate" type="text/x-handlebars-template">
	<div id="err_docgrouping" class="alert alert-danger mb-2 d-none"></div>
	<form name="frmDocGrouping" id="frmDocGrouping" onsubmit="saveEventDocGrouping();return false;">
	<input type="hidden" name="eventDocumentGroupingID" id="eventDocumentGroupingID" value="{{eventDocumentGroupingID}}">
	<div class="form-group">
		<div class="form-label-group">
			<input type="text" name="eventDocumentGrouping" id="eventDocumentGrouping" value="{{eventDocumentGrouping}}" class="form-control" maxlength="200" autocomplete="off">
			<label for="eventDocumentGrouping">Document Grouping</label>
		</div>
	</div>
	</form>
</script>
</cfoutput>