<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfsavecontent variable="local.seminarWebJS">
	<cfoutput>
	<script type="text/javascript" src="/assets/admin/javascript/seminarweb.js#local.assetCachingKey#"></script>
	<script>
		var gridInitArray = new Array();
		gridInitArray["programsTab"] = false;
		gridInitArray["registrantSearchTab"] = false;
		gridInitArray["scheduledTaskTab"] = false;
	
		function onTabChangeHandler(ActiveTab) {
			if (!gridInitArray[ActiveTab.id]) {
				gridInitArray[ActiveTab.id] = true;
				switch(ActiveTab.id) {
					case "programsTab":
						loadProgramTab(); break;
					case "registrantSearchTab":
						loadRegistrantSearchTab(); break;
					case "scheduledTaskTab":
						loadSWLScheduledTaskTab(); break;
				}
			}
		}
		
		var #ToScript(arguments.event.getValue('mc_siteInfo.siteID'),'sw_siteid')#
		var #ToScript(arguments.event.getValue('mc_siteInfo.siteCode'),'sw_sitecode')#
		var #ToScript(local.seminarsLink,'link_listswlprograms')#
		var #ToScript(local.editSWLProgram,'link_editswlprogram')#
		var #ToScript(local.seminarsExportLink,'link_exportswlprogram')#
		var #ToScript(local.deleteSWProgramLink,'link_deleteswprogram')#
		var #ToScript(this.link.copySWProgramPrompt,'link_copyswprogram')#
		var #ToScript(local.timeZoneAbbr,'timeZoneAbbr')#
		var sw_itemtype = 'SWL';
		var sw_programgridmode = 'grid';
		let swlProgramsTable;

		function refreshGrids() { 
			mcg_reloadGrid();
			SWLRegistrantsListTable.draw(false);
		}
		function loadProgramTab() {
			initializeSWLProgramsTable();
			mca_setupDatePickerRangeFields('fDateFrom','fDateTo');
			mca_setupCalendarIcons('frmFilter');
			mca_setupCalendarIcons('frmAddSWLProgram');
		}

		<cfif local.hasManageSWLRegistrantsRights>
			var #ToScript(local.sendSWLReplayLink,'link_replaylink')#
			var #ToScript(local.SWLRegistrantsLink,'link_swlregistrantslist')#
			var SWLRegistrantsListTable, sw_showCreditCol = 1, sw_showAttendanceCol = 1;
			var sw_reggridmode = 'regsearchgrid';

			var #ToScript(local.exportRegPromptLink,'link_exportswregprompt')#
			var #ToScript(local.sendMaterialsLink,'link_viewswlmaterials')#
			var #ToScript(local.manageCreditLink,'link_managecredit')#
			var #ToScript(local.viewSWLProgressLink,'link_viewswlprogress')#
			var #ToScript(this.link.viewCertificate,'link_viewcertificate')#
			var #ToScript(local.getCommunicationLink,'link_viewcommunication')#
			var #ToScript(local.resendInstructionsLink,'link_resendinstructions')#
			var #ToScript(local.changeRegistrantPriceLink,'link_changeregprice')#
			var #ToScript(local.removeEnrollmentLink,'link_removeenrollment')#
			var #ToScript(this.link.editMember,'link_editmember')#

			var #ToScript(local.addSWLPaymentLink,'link_addswpayment')#
			var #ToScript(local.myRightsTransactionsAdmin.transAllocatePayment,'sw_hastransallocaterights')#
			<cfif local.myRightsTransactionsAdmin.transAllocatePayment is 1>
				var #ToScript(local.allocateSWLPaymentLink,'link_allocateswpayment')#
			</cfif>

			function loadRegistrantSearchTab() {
				initSWLRegistrants();
				mca_setupDatePickerRangeFields('frpDateFrom','frpDateTo'); 
				mca_setupDatePickerRangeFields('frrDateFrom','frrDateTo');
				mca_setupCalendarIcons('frmRegistrantFilter');
			}
		</cfif>

		$(function() {
			mca_initNavPills('SWLTabs', '#local.selectedTab#', '#local.lockTab#', onTabChangeHandler);
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.seminarWebJS)#">

<cfoutput>
<h4>#local.pageHeading#</h4>

<ul class="nav nav-pills nav-pills-dotted" id="SWLTabs">
	<cfset local.thisTabName = "programs">
	<cfset local.thisTabID = "programsTab">
	<li class="nav-item">
		<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
			aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Programs</a>
	</li>
	<cfif local.hasManageSWLRegistrantsRights>
		<cfset local.thisTabName = "registrantSearch">
		<cfset local.thisTabID = "registrantSearchTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
				aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Registrant Search</a>
		</li>
	</cfif>
	<cfset local.thisTabName = "scheduledTask">
	<cfset local.thisTabID = "scheduledTaskTab">
	<li class="nav-item">
		<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
			aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Scheduled Tasks</a>
	</li>
</ul>

<div class="tab-content mc_tabcontent p-2 pb-0" id="pills-tabContent">
	<div class="tab-pane fade" id="pills-programsTab" role="tabpanel" aria-labelledby="programsTab">
		<cfinclude template="dsp_SWL_programs.cfm">
	</div>
	<cfif local.hasManageSWLRegistrantsRights>
		<div class="tab-pane fade" id="pills-registrantSearchTab" role="tabpanel" aria-labelledby="registrantSearchTab">
			<cfinclude template="dsp_SWL_registrants.cfm">
		</div>
	</cfif>
	<div class="tab-pane fade" id="pills-scheduledTaskTab" role="tabpanel" aria-labelledby="scheduledTaskTab">
		<cfinclude template="dsp_SWL_scheduledTask.cfm">
	</div>
</div>
</cfoutput>