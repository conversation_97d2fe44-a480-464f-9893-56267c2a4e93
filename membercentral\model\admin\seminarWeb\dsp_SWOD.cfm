<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfsavecontent variable="local.seminarWebJS">
	<cfoutput>
	#application.objWebEditor.showEditorHeadScripts()#
	<script type="text/javascript" src="/assets/admin/javascript/seminarweb.js#local.assetCachingKey#"></script>
	<script>
		var gridInitArray = new Array();
		gridInitArray["programsTab"] = false;
		gridInitArray["registrantSearchTab"] = false;
		gridInitArray["scheduledTaskTab"] = false;
	
		function onTabChangeHandler(ActiveTab) {
			if (!gridInitArray[ActiveTab.id]) {
				gridInitArray[ActiveTab.id] = true;
				switch(ActiveTab.id) {
					case "programsTab":
						loadProgramTab(); break;
					case "registrantSearchTab":
						loadRegistrantSearchTab(); break;
					case "scheduledTaskTab":
						loadSWODScheduledTaskTab(); break;
				}
			}
		}

		var #ToScript(arguments.event.getValue('mc_siteInfo.siteID'),'sw_siteid')#
		var #ToScript(arguments.event.getValue('mc_siteInfo.siteCode'),'sw_sitecode')#

		var sw_itemtype = 'SWOD';
		var #ToScript(local.seminarsExportLink,'link_exportswodprogram')#
		var #ToScript(local.submitProgramLink,'link_submitswodprogram')#
		var #ToScript(local.editProgramLink,'link_editswodprogram')#	
		var #ToScript(local.seminarsLink,'link_listswodprograms')#
		var #ToScript(this.link.copySWProgramPrompt,'link_copyswprogram')#
		var #ToScript(local.deleteSWProgramLink,'link_deleteswprogram')#
		var #ToScript(local.deactivateSWODProgramsLink,'link_deactivateSWODPrograms')#
		let swodProgramsTable;
		
		function loadProgramTab() {
			initializeSWODProgramsTable();
			mca_setupDatePickerRangeFields('fDateFrom','fDateTo');
			mca_setupDatePickerRangeFields('fActivatedDateFrom','fActivatedDateTo');
			mca_setupDatePickerRangeFields('fOrigPublishDateFrom','fOrigPublishDateTo');
			mca_setupCalendarIcons('frmFilter');
		}

		<cfif local.hasManageSWODRegistrantsRights>
			var SWODRegistrantsListTable;
			var #ToScript(local.SWODRegistrantsLink,'link_swodregistrantslist')#;
			var sw_reggridmode = 'regsearchgrid';

			var #ToScript(local.exportRegPromptLink,'link_exportswregprompt')#
			var #ToScript(local.viewProgressLink,'link_viewswodprogress')#
			var #ToScript(local.manageCreditLink,'link_managecredit')#
			var #ToScript(local.resendInstructionsLink,'link_resendinstructions')#
			var #ToScript(local.getCommunicationLink,'link_viewcommunication')#
			var #ToScript(this.link.viewCertificate,'link_viewcertificate')#
			var #ToScript(this.link.editMember,'link_editmember')#
			var #ToScript(local.changeRegistrantPriceLink,'link_changeregprice')#
			var #ToScript(local.removeEnrollmentLink,'link_removeenrollment')#

			var #ToScript(local.addSWODPaymentLink,'link_addswpayment')#
			var #ToScript(local.myRightsTransactionsAdmin.transAllocatePayment,'sw_hastransallocaterights')#
			<cfif local.myRightsTransactionsAdmin.transAllocatePayment is 1>
				var #ToScript(local.allocateSWODPaymentLink,'link_allocateswpayment')#
			</cfif>
			
			function loadRegistrantSearchTab() {
				initSWODRegistrants(); 
				mca_setupDatePickerRangeFields('frrDateFrom','frrDateTo');
				mca_setupDatePickerRangeFields('frrDateCompletedFrom','frrDateCompletedTo');
				mca_setupCalendarIcons('frmRegistrantFilter');
			}
		</cfif>
		
		$(function() {
			mca_initNavPills('SWODTabs', '#local.selectedTab#', '#local.lockTab#', onTabChangeHandler);
			mca_setupDatePickerField('dateOrigPublished');
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.seminarWebJS)#">

<cfoutput>
<h4>#local.pageHeading#</h4>
<div id="SWODTabsContainer">
	<ul class="nav nav-pills nav-pills-dotted" id="SWODTabs">
		<cfset local.thisTabName = "programs">
		<cfset local.thisTabID = "programsTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
				aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Programs</a>
		</li>
		<cfif local.hasManageSWODRegistrantsRights>
			<cfset local.thisTabName = "registrantSearch">
			<cfset local.thisTabID = "registrantSearchTab">
			<li class="nav-item">
				<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
					aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Registrant Search</a>
			</li>
		</cfif>
		<cfset local.thisTabName = "scheduledTask">
		<cfset local.thisTabID = "scheduledTaskTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
				aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Scheduled Tasks</a>
		</li>
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfset local.thisTabName = "import">
			<cfset local.thisTabID = "importTab">
			<li class="nav-item">
				<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
					aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Import</a>
			</li>
		</cfif>
	</ul>

	<div class="tab-content mc_tabcontent p-2 pb-0" id="pills-tabContent">
		<div class="tab-pane fade" id="pills-programsTab" role="tabpanel" aria-labelledby="programsTab">
			<cfinclude template="dsp_SWOD_programs.cfm">
		</div>
		<cfif local.hasManageSWODRegistrantsRights>
			<div class="tab-pane fade" id="pills-registrantSearchTab" role="tabpanel" aria-labelledby="registrantSearchTab">
				<cfinclude template="dsp_SWOD_registrants.cfm">
			</div>
		</cfif>
		<div class="tab-pane fade" id="pills-scheduledTaskTab" role="tabpanel" aria-labelledby="scheduledTaskTab">
			<cfinclude template="dsp_SWOD_scheduledTask.cfm">
		</div>
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<div class="tab-pane fade" id="pills-importTab" role="tabpanel" aria-labelledby="importTab">
				<cfif local.showImpTemplate eq 1>
					<cfinclude template="dsp_SWOD_import.cfm">
				<cfelse>
					#local.impData#
				</cfif>
			</div>
		</cfif>
	</div>
</div>
<div id="divSubmitSWODProgramForm" class="d-none my-2"></div>
</cfoutput>