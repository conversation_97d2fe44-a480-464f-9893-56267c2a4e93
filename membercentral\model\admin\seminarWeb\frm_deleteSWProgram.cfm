<cfoutput>
<form name="frm_deleteSWProgram" id="frm_deleteSWProgram" method="post"  class="p-3" onsubmit="return top.dodeleteProgram(#local.seminarId#);">
	<h5 class="mb-1">#encodeForHTML(local.qrySeminar.seminarName	)#</h5>
	<cfif local.SWType eq 'SWL'>
		<h6>#local.formattedDateStart#  #local.formattedTimeStart# #local.timeZoneAbbr#</h6>
	</cfif>
	<div class="alert alert-danger">
		<b>Confirmation Needed</b><br/>
		Are you sure you want to delete this program? This action is not reversible.
		<cfif local.enrolledCount >
			There are #local.enrolledCount# registrants who will permanently lose access to the program.
		</cfif>	
	</div>
	<!--- hidden submit triggered from parent --->
	<button type="submit" class="d-none"></button>
</form>
</cfoutput>