<cfset local.showAsterisk = false>
<cfset local.refundJSMore = "">

<cfsavecontent variable="local.refundJS">
	<cfoutput>
	<style type="text/css">
		.dropdown .dropdown-menu { transform:translate3d(0px, -207px, 0px) !important; height:200px; overflow-y:auto; }
	</style>
	<script language="javascript">
	function hideAlert() { $('##everr').html('').addClass('d-none'); setBatch(); };
	function showAlert(msg) { $('##everr').html(msg).removeClass('d-none'); setBatch(); };

	<cfif local.hasRefundRights>
		var arrPayments = new Object();
		var pidspecs = new Object();
		<cfloop query="local.qryMerchantProfiles">
			pidspecs[#local.qryMerchantProfiles.profileid#] = #local.qryMerchantProfiles.gatewayid#;
		</cfloop>

		function onBatchDropdownChange(batchid, batchName){
			var pid = $('##paySource').val();
			$('a.selectedBatchOption_' + pid +' .selectedBatchOptionText').html(batchName);
			$('a.selectedBatchOption_' + pid).data('selectedbatchid', batchid);
			setBatch();
		}
		function initBatchDropdown(){
			$('div[id^=batchDropdown_]').find('.dropdown-menu .dropdown-item').unbind().on('click',function(e){
				onBatchDropdownChange($(this).data('batchid'), $(this).data('batchname'));
				$(this).addClass("active").siblings().removeClass("active");
				return true;
			});
		}
		function setBatch() {
			var pid = $('##paySource').val();
			
			<cfif arguments.event.getValue('mc_siteinfo.useBatches') is 1>
				$('##pf_' + pid + '_batchdiv2').hide();
				$('##pf_' + pid + '_batchdiv3').hide();
		
				<!--- if gateway 1 or 2, ask for batch. else gateway is online and put it on batch automatically --->
				if (pidspecs[pid] == 1 || pidspecs[pid] == 2) {
					$('##pf_' + pid + '_batchdiv2').show();
					var selectedBatchID = $('a.selectedBatchOption_' + pid).data('selectedbatchid');
					$('##batchid').val(selectedBatchID);
				} else {
					$('##batchid').val(-1);
					$('##pf_' + pid + '_batchdiv3').show();
				}
			<cfelse>
				$('##batchid').val(-1);
			</cfif>
				
			if ($('##profileid').val() == 0 || $('##batchid').val() == 0 || parseFloat($('##amount').val()) <= 0) disableSaveBtn(pid);
			else enableSaveBtn(pid);
		}
		function useProfile() {
			hideAlert();
			$('##paymentTabs').children().hide();
			
			var pid = $('##paySource').val();
			$('##profileid').val(pid);
			$('##paymentTab' + pid).show();
			<cfif listFindNoCase("subscriptions,registrants",local.tabMode)>
				$('button##btnSkipRefund').toggle(pid == 0);
			</cfif>
			setBatch();
		}
		function disableSaveBtn(pid) {
			$('##btnSaveRefund'+pid).attr('disabled',true);
		}
		function enableSaveBtn(pid) {
			$('##btnSaveRefund'+pid).attr('disabled',false);
		}
		function delayRefundFunc(ms) {
			return new Promise(resolve => setTimeout(resolve, ms));
		}
		function updateRefundPFAmts(ovtrID) {
			let refundPFAmts = typeof ovtrID != "undefined" ? $('##refund_pf_ratio_'+ovtrID) : $('input.refund_pf_ratio');

			if (! refundPFAmts.length) return false;
			
			refundPFAmts.each(function(el) {
				let trID = $(this).data('trid');
				let refundRatio = Number($('##refund_pf_ratio_'+trID).val());
				let refundAmt = Number(formatCurrency($('##amount_'+trID).val()));
				let maxRefundAmt = Number($(this).data('maxrefamt'));
				if (refundAmt > maxRefundAmt) {
					refundAmt = 0;
					$('##amount_'+trID).val(0);
				}
				
				$('##final_refund_pf_'+trID).html('$'+formatCurrency(parseFloat(refundRatio * refundAmt)));
			});
		}

		_FB_hasValue = function(obj, obj_type){
			if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){
				tmp = obj.value;
				tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
				if (tmp.length == 0) return false;
				else return true;
			} else if (obj_type == 'SELECT'){
				for (var i=0; i < obj.length; i++) {
					if (obj.options[i].selected){
						tmp = obj.options[i].value;
						tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
						if (tmp.length > 0) return true;
					}
				}
				return false;
			} else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){
				if (obj.checked) return true;
				else return false;	
			} else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){
				if (obj.length == undefined && obj.checked) return true;
				else{
					for (var i=0; i < obj.length; i++){
						if (obj[i].checked) return true;
					}
				}
				return false;
			}else{
				return true;
			}
		};
	</cfif>

	$(function() {
		top.MCModalUtils.setTitle('#encodeForJavaScript("Issue Refund for #local.qryMember.firstname# #local.qryMember.middlename# #local.qryMember.lastname# #local.qryMember.Suffix# (#local.qryMember.membernumber#)")#');
		top.MCModalUtils.buildFooter({});
		<cfif local.qryMerchantProfiles.recordcount is 0>
			showAlert('No payment profiles are setup to allow refunds.');
		</cfif>
		<cfif local.hasRefundRights>
			initBatchDropdown();
			updateRefundPFAmts();
			
			$('input.refundAmt').on('keyup',function() {
				let trID = $(this).data('trid');
				delayRefundFunc(1200).then(function() {
					updateRefundPFAmts(trID);
				});
			});
		<cfelse>
			top.$('##MCModalFooter').addClass('d-none');
			$('input.refundAmt').prop('disabled',true)
		</cfif>
	});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.refundJS)#">

<cfoutput>
<form name="frmRefund" id="frmRefund" class="p-3"<cfif local.hasRefundRights> action="#local.formlink#" method="post" onsubmit="return checkRefundForm();"</cfif>>
<input type="hidden" name="mid" id="mid" value="#local.qryMember.memberid#">
<input type="hidden" name="ptid"  id="ptid" value="#valuelist(local.qryMemberCredit.transactionid)#">
<input type="hidden" name="profileid" id="profileid" value="0">
<input type="hidden" name="batchid" id="batchid" value="0">
<input type="hidden" name="refundPFD" id="refundPFD" value="1">
<input type="hidden" name="tabMode" id="tabMode" value="#local.tabMode#">

<cfif not local.hasRefundRights>
	<div class="alert alert-warning mb-3">You do not have access to process refunds. Please contact your accounting administrator to complete the refund process.</div>
</cfif>

<cfif val(local.qryMemberCredit.processingFees)>
	<div class="alert alert-info">
		This payment includes a refundable #local.qryMemberCredit.processingFeeLabel# that will be automatically included in the total refund amount.
		It is proportionally calculated based on your entered refund amount.
	</div>
</cfif>

<table class="table table-sm table-borderless table-striped border">
	<thead>
		<th width="60%">Payments with Refundable Amounts</th>
		<th width="20%" class="text-right pr-3">Refundable</th>
		<th width="20%">Refund Amt</th>
	</thead>
	<tbody>
		<cfloop query="local.qryMemberCredit">
			<!--- restrict partial refunds for same-day apple/google pay --->
			<cfset local.allowPartialRefund = true>
			<cfset local.partialRefundDenyMsg = "">
			<cfif local.qryMemberCredit.gatewayID EQ 10 AND local.qryMemberCredit.paidToday EQ 1 AND (local.qryMemberCredit.isApplePay EQ 1 OR local.qryMemberCredit.isGooglePay EQ 1)>
				<cfset local.allowPartialRefund = false>
				<cfset local.partialRefundDenyMsg = "This form of payment does not allow same-day partial refunds.">
			<!--- MCPayECheck --->
			<cfelseif local.qryMemberCredit.gatewayID EQ 19>
				<cfset local.allowPartialRefund = false>
				<cfset local.partialRefundDenyMsg = "This form of payment does not allow partial refunds.">
			</cfif>
			<cfif local.qryMemberCredit.refundableAmount gt local.qryMemberCredit.unallocatedAmount>
				<cfset local.showAsterisk = true>
			</cfif>
			<tr>
				<td>
					#dollarformat(local.qryMemberCredit.amount)# &nbsp;#local.qryMemberCredit.detail# <span class="text-dark">on #dateFormat(local.qryMemberCredit.transactionDate,"m/d/yy")#</span>
					<cfif val(local.qryMemberCredit.processingFees)>
						<div class="small font-italic font-weight-bold pt-2">Payment includes #dollarFormat(local.qryMemberCredit.processingFees)# #local.qryMemberCredit.processingFeeLabel#</div>
					</cfif>
				</td>
				<td class="text-right pr-3">
					<cfif local.qryMemberCredit.refundableAmount gt local.qryMemberCredit.unallocatedAmount>* </cfif>#dollarformat(min(local.qryMemberCredit.unallocatedAmount,local.qryMemberCredit.refundableAmount))#
					<cfif val(local.qryMemberCredit.processingFees)>
						<div class="small font-italic font-weight-bold pt-2">#dollarFormat(local.qryMemberCredit.refundableProcessingFees)#</div>
					</cfif>
				</td>
				<td class="align-top">
					<div class="d-flex align-items-center">
						<div class="input-group input-group-sm">
							<div class="input-group-prepend">
								<span class="input-group-text px-1">$</span>
							</div>
							<cfif local.allowPartialRefund>
								<input type="text" name="amount_#local.qryMemberCredit.transactionID#" id="amount_#local.qryMemberCredit.transactionID#" class="form-control form-control-sm text-green px-1 refundAmt" data-trid="#local.qryMemberCredit.transactionID#" value="0.00" autocomplete="off" onblur="this.value=formatCurrency(this.value);hideAlert();">
							<cfelse>
								<input type="hidden" name="amount_#local.qryMemberCredit.transactionID#" id="amount_#local.qryMemberCredit.transactionID#" value="#local.qryMemberCredit.refundableAmount#">
								<input type="text" name="dspamount_#local.qryMemberCredit.transactionID#" id="dspamount_#local.qryMemberCredit.transactionID#" class="form-control form-control-sm text-green px-1" value="#local.qryMemberCredit.refundableAmount#" disabled>
							</cfif>
						</div>
					</div>
					<cfif val(local.qryMemberCredit.processingFees)>
						<input type="hidden" name="refund_pf_ratio_#local.qryMemberCredit.transactionID#" id="refund_pf_ratio_#local.qryMemberCredit.transactionID#" class="refund_pf_ratio" data-trid="#local.qryMemberCredit.transactionID#" data-maxrefamt="#min(local.qryMemberCredit.unallocatedAmount,local.qryMemberCredit.refundableAmount)#" value="#NumberFormat(precisionEvaluate(local.qryMemberCredit.processingFees/(local.qryMemberCredit.refundableAmount - local.qryMemberCredit.processingFees)),'0.0000')#">
						<div id="final_refund_pf_#local.qryMemberCredit.transactionID#" class="small font-italic font-weight-bold pl-3 pt-1">$0.00</div>
					</cfif>
				</td>
			</tr>
			<cfif NOT local.allowPartialRefund>
				<tr>
					<td colspan="3" class="text-right text-dim small">#local.partialRefundDenyMsg#</td>
				</tr>
			</cfif>
			<cfsavecontent variable="local.refundJSMore">
				#local.refundJSMore#
				arrPayments["#local.qryMemberCredit.transactionID#"] = #min(local.qryMemberCredit.unallocatedAmount,local.qryMemberCredit.refundableAmount)#;
			</cfsavecontent>
		</cfloop>
	</tbody>
</table>
<cfif local.showAsterisk>
	<div class="text-right mt-2">* Refunds can only be issued against unallocated funds. The refundable amount is shown.</div>
</cfif>

<cfif local.hasRefundRights>
	<cfif local.qryMerchantProfiles.recordcount>
		<div class="row no-gutters mt-3 mx-2">
			<label for="paySource" class="col-auto col-form-label-sm font-size-md">Refund Method:</label>
			<div class="col px-2">
				<select id="paySource" name="paySource" class="form-control form-control-sm" onchange="useProfile();">
					<option value="0"></option>
					<cfloop query="local.qryMerchantProfiles">
						<option value="#local.qryMerchantProfiles.profileID#">#local.qryMerchantProfiles.profileName#</option>
					</cfloop>
				</select>
			</div>
		</div>
	<cfelse>
		<input type="hidden" name="paySource"  id="paySource" value="0">
	</cfif>

	<div id="everr" class="alert alert-danger mb-2 d-none"></div>

	<cfif listFindNoCase("subscriptions,registrants",local.tabMode)>
		<button type="button" name="btnSkipRefund" id="btnSkipRefund" class="mt-3 btn btn-sm btn-primary btnSkipRefund" onclick="top.MCModalUtils.hideModal();">Skip Refund</button>
	</cfif>

	<div id="paymentTabs">
		<cfloop query="local.qryMerchantProfiles">
			<cfset local.thisProfileID = local.qryMerchantProfiles.profileID>
			
			<cfif NOT listFindNoCase("creditcard,bankdraft",local.qryMerchantProfiles.gatewayClass)>
				<cfset local.strPaymentForm = application.objPayments.showGatewayInputForm(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryMerchantProfiles.profileCode, pmid=local.qryMember.memberid, showCOF=true, usePopup=false, usePopupDIVName='pf_#local.thisProfileID#_', adminForm=1)>

				<cfif len(local.strPaymentForm.headcode)>
					<cfhtmlhead text="#application.objCommon.minText(local.strPaymentForm.headcode)#">
				</cfif>
			</cfif>

			<div id="paymentTab#local.thisProfileID#" style="display:none;">
			<div id="pf_#local.thisProfileID#_">
				<cfif NOT listFindNoCase("creditcard,bankdraft",local.qryMerchantProfiles.gatewayClass) and len(trim(local.strPaymentForm.inputForm))>
					<div class="bg-neutral-second p-2 mt-2">
						<div>#replaceNoCase(local.strPaymentForm.inputForm,'fld_','p_#local.thisProfileID#_fld_','ALL')#</div>
					</div>
				<cfelseif local.qryMerchantProfiles.gatewayClass eq "creditcard">
					<div class="bg-neutral-second p-2 mt-2">
						<div>Refund will be issued to the same credit card used for payment.</div>
					</div>
				<cfelseif local.qryMerchantProfiles.gatewayClass eq "bankdraft">
					<div class="bg-neutral-second p-2 mt-2">
						<div>Refund will be issued to the same checking account used for payment.</div>
					</div>
				</cfif>
				<cfif arguments.event.getValue('mc_siteinfo.useBatches') is 1>
					<cfif listFind("1,2",local.qryMerchantProfiles.gatewayID)>
						<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="tr_getOpenBatches">
							<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
							<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.thisProfileID#">
							<cfprocresult name="local.qryOpenBatches">
						</cfstoredproc>

						<div id="pf_#local.thisProfileID#_batchdiv2" class="pt-3" style="display:none;">
							<div class="font-weight-bold">Batch Identifier</div>
							<cfif local.qryOpenBatches.recordcount>
								<div>Select the batch this refund belongs to:</div>
								<div id="batchDropdown_#local.thisProfileID#" class="dropdown show mt-2">
									<a class="btn btn-secondary selectedBatchOption_#local.thisProfileID# w-100" href="javascript:void(0);" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-selectedbatchid="0">
										<div class="d-flex">
											<div class="selectedBatchOptionText mr-auto">Select an open batch</div>
											<div><i class="fa fa-chevron-down" aria-hidden="true"></i></div>
										</div>
									</a>
									<div class="dropdown-menu w-100" aria-labelledby="dropdownMenuLink">
										<a class="dropdown-item" href="javascript:void(0);" data-batchid="0" data-batchname="Select an open batch">Select an open batch</a>
										<cfloop query="local.qryOpenBatches">
											<cfset local.batchName = left(local.qryOpenBatches.batchname,65)>
											<cfif len(local.qryOpenBatches.batchname) gt 65>
												<cfset local.batchName &= "...">
											</cfif>
											<a class="dropdown-item py-1" href="javascript:void(0);" data-batchid="#local.qryOpenBatches.batchid#" data-batchname="#local.batchName#">
												<table class="table table-sm table-borderless mb-0">
													<tr>
														<td width="50%" class="text-wrap font-weight-bold">#local.batchName#</td>
														<td width="50%" class="text-bottom text-wrap font-italic">Created by #local.qryOpenBatches.createdByMember#</td>
													</tr>
													<tr>
														<td>#dateformat(local.qryOpenBatches.depositDate,'mm/dd/yyyy')#</td>
														<td>
															<span>Control: #dollarformat(local.qryOpenBatches.controlAmt)# (#local.qryOpenBatches.controlCount#)</span>
															<span class="ml-2">Actual: #dollarFormat(local.qryOpenBatches.actualAmt)# (#local.qryOpenBatches.actualCount#)</span>
														</td>
													</tr>
												</table>
											</a>
										</cfloop>
									</div>
								</div>
							<cfelse>
								<input type="hidden" name="pf_#local.thisProfileID#_batchid"  id="pf_#local.thisProfileID#_batchid" value="0">
								<div class="alert alert-danger mt-2">
									There are no open batches linked to this refund method.<br/>
									Open a batch before refunding.
								</div>
							</cfif>
						</div>

					<cfelseif listFind("16,19",local.qryMerchantProfiles.gatewayID)>
						<div id="pf_#local.thisProfileID#_batchdiv3" class="pt-3" style="display:none;">
							<div class="font-weight-bold">Batch Identifier</div>
							<div>This payment will be added to an open <i>PPD or CCD #local.qryMerchantProfiles.profilecode#</i> batch based on the account used.</div>
						</div>

					<cfelse>
						<div id="pf_#local.thisProfileID#_batchdiv3" class="pt-3" style="display:none;">
							<div class="font-weight-bold">Batch Identifier</div>
							<div>This refund will be added to an open <i>#dateformat(now(),'YYYYMMDD')# #local.qryMerchantProfiles.profilecode# XXXXX</i> batch.</div>
						</div>
					</cfif>
				</cfif>
				<cfif listFindNoCase("creditcard,bankdraft,check",local.qryMerchantProfiles.gatewayClass) and arguments.event.getValue('mc_siteinfo.useBatches') is not 1>
					Click "Save Refund" to continue.
				</cfif>

				<div id="divBtnWrapper#local.thisProfileID#" class="mt-3">
					<button type="submit" name="btnSaveRefund#local.thisProfileID#" id="btnSaveRefund#local.thisProfileID#" class="btn btn-sm btn-primary btnSaveRefund" disabled>Save Refund</button>
				</div>
			</div>
			</div>

			<cfif NOT listFindNoCase("creditcard,bankdraft",local.qryMerchantProfiles.gatewayClass) and len(local.strPaymentForm.jsvalidation)>
				<cfsavecontent variable="local.extrapayJS">
					#local.extrapayJS#
					if ($('##profileid').val()==#local.thisProfileID#) {
						#replaceNoCase(local.strPaymentForm.jsvalidation,'fld_','p_#local.thisProfileID#_fld_','ALL')#
					}
				</cfsavecontent>
			</cfif>
		</cfloop>
	</div>
<cfelse>
	<button type="button" name="btnCloseRefund" id="btnCloseRefund" class="mt-3 btn btn-sm btn-primary btnSkipRefund" onclick="top.MCModalUtils.hideModal();">Close</button>
</cfif>
</form>
</cfoutput>

<cfsavecontent variable="local.validationJS">
	<cfoutput>
	<script language="javascript">
	function checkRefundForm() {
		hideAlert();
		var arrReq = new Array();

		<cfif len(local.refundJSMore)>
			#local.refundJSMore#
		</cfif>
		
		var refundAmount = 0;
		var arrRefundAmount = $('##ptid').val().split(",");
		for (var i=0; i<arrRefundAmount.length; i++) {
			$('##amount_'+arrRefundAmount[i]).val(formatCurrency($('##amount_'+arrRefundAmount[i]).val()));
			if (parseFloat($('##amount_'+arrRefundAmount[i]).val()) > arrPayments[arrRefundAmount[i]]) arrReq[arrReq.length] = 'You may not issue a refund for more than a payment\'s refundable amount ($' + arrPayments[arrRefundAmount[i]] + ').';
			refundAmount += parseFloat($('##amount_'+arrRefundAmount[i]).val());
		}
		if (refundAmount <= 0) arrReq[arrReq.length] = 'You may not issue a zero dollar refund. Enter a valid refund amount.';
		if ($('##paySource').val() == '') arrReq[arrReq.length] = 'Select which refund method you are using for this refund.';
	
		<cfif len(local.extrapayJS)>
			var thisForm = document.forms["frmRefund"];
			#local.extrapayJS#
		</cfif>
		
		if (arrReq.length > 0) {
			var msg = '<b>The following requires your attention:</b><br/>' + arrReq.join('<br/>');
			showAlert(msg);
			return false;
		}
		$('.btnSaveRefund').prop('disabled',true);
		return true;
	};
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.validationJS)#">