<cfif NOT variables.isBot and local.qryMyInvoices.recordcount>
	<cfsavecontent variable="local.invCSS">
		<style type="text/css">
		div.invPayNotify { font-weight:bold; font-style:italic; }
		.d-none {display:none;}
		</style>
		<script language="javascript">
			function payInvoice(i) {
				var pAmt = Number(formatCurrency($('#invamt_'+i).val()).replace(',',''));
				var amtDue = Number(parseFloat($('#invamt_'+i).data('amtdue')).toFixed(2));

				if (pAmt > 0 && pAmt <= amtDue) {
					self.location.href = '/?pg=buyNow&item=INV-' + escape(i) + (pAmt < amtDue ? '-' + pAmt : '');
				} else {
					alert('Enter a payment amount less than or equal to $' + formatCurrency(amtDue) + '.');
					$('#invamt_'+i).val(formatCurrency(amtDue));
					return false;
				}
			}
			function payAnotherInvoice() {
				if (!$('#container').hasClass('d-none')) 
					$('#container').addClass('d-none');
				else
					$('#container').removeClass('d-none');

				if ($('#icon').hasClass('icon-caret-up')) {
					$('#icon').addClass('icon-caret-down');
					$('#icon').removeClass('icon-caret-up');
				}
				else {
					$('#icon').removeClass('icon-caret-down');
					$('#icon').addClass('icon-caret-up');
				}
			}
		</script>
	</cfsavecontent>
<cfelse>
	<cfsavecontent variable="local.invCSS">
		<style type="text/css">
			.d-none {display:none;}
		</style>
	</cfsavecontent>
</cfif>
<cfhtmlhead text="#local.invCSS#">

<div class="tsAppBodyText" style="background-color:#eee;padding:6px;margin-bottom:10px;"><b>Invoices</b></div>

<cfif NOT variables.isBot and local.qryMyInvoices.recordcount>
	<div class="tsAppBodyText">
		<table class="tsAppBodyText" width="100%">
		<cfoutput query="local.qryMyInvoices">
			<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.qryMyInvoices.fullinvoicenumber#|#right(GetTickCount(),5)#|#local.qryMyInvoices.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
			<tr valign="top">
				<td class="tsAppBodyText">
					<div style="margin:3px;">
						<b>Invoice #local.qryMyInvoices.fullinvoicenumber#</b> (<a href="/?pg=invoices&va=show&item=#local.stInvEnc#&mode=stream">View Invoice</a>)<br/>
						<i>#local.qryMyInvoices.invoiceprofile#</i>
					</div>
				</td>
				<td class="tsAppBodyText">
					#dollarformat(local.qryMyInvoices.InvCanPay)#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif> due by #dateFormat(local.qryMyInvoices.dateDue,"mmmm d, yyyy")#
					<cfif local.qryMyInvoices.enforcePayOldest eq 1 and local.qryMyInvoices.invCount gt 1>
						<div class="invPayNotify">
							<cfif local.qryMyInvoices.isOldestInv eq 1>
								This "#local.qryMyInvoices.invoiceprofile#" invoice must be paid first.
							<cfelse>
								<span style="color:##f00;"><i class="icon-warning-sign icon-large"></i> Older "#local.qryMyInvoices.invoiceprofile#" invoices must be paid before payment can be applied to this invoice.</span>
							</cfif>
						</div>
					</cfif>
				</td>
				<td class="tsAppBodyText">
					<cfif local.qryMyInvoices.showLink is 1>
						<cfif local.qryMyInvoices.enforcePayOldest eq 1>
							<cfif local.qryMyInvoices.invCount eq 1 or (local.qryMyInvoices.invCount gt 1 and local.qryMyInvoices.isOldestInv eq 1)>
								<cfif local.qryMyInvoices.allowPartialPayment eq 1>
									$ <input type="text" class="tsAppBodyText" name="invamt_#local.qryMyInvoices.invoicenumber#" id="invamt_#local.qryMyInvoices.invoicenumber#" data-amtdue="#local.qryMyInvoices.InvCanPay#" value="#NumberFormat(local.qryMyInvoices.InvCanPay,"9,999.00")#" onblur="this.value=formatCurrency(this.value);" size="10"><cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif>
								<cfelse>
									<input type="hidden" name="invamt_#local.qryMyInvoices.invoicenumber#" id="invamt_#local.qryMyInvoices.invoicenumber#" data-amtdue="#local.qryMyInvoices.InvCanPay#" value="#trim(local.qryMyInvoices.InvCanPay)#">
								</cfif>
								<button type="button" class="tsAppBodyButton" onClick="payInvoice('#local.qryMyInvoices.invoicenumber#');">Pay Now</button>
							<cfelse>
								<button type="button" disabled>Pay Now</button>
							</cfif>
						<cfelse>
							<cfif local.qryMyInvoices.allowPartialPayment eq 1>
								$ <input type="text" class="tsAppBodyText" name="invamt_#local.qryMyInvoices.invoicenumber#" id="invamt_#local.qryMyInvoices.invoicenumber#" data-amtdue="#local.qryMyInvoices.InvCanPay#" value="#NumberFormat(local.qryMyInvoices.InvCanPay,"9,999.00")#" onblur="this.value=formatCurrency(this.value);" style="width:80px !important;"><cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif>
							<cfelse>
								<input type="hidden" name="invamt_#local.qryMyInvoices.invoicenumber#" id="invamt_#local.qryMyInvoices.invoicenumber#" data-amtdue="#local.qryMyInvoices.InvCanPay#" value="#trim(local.qryMyInvoices.InvCanPay)#">
							</cfif>
							<button type="button" class="tsAppBodyButton" onClick="payInvoice('#local.qryMyInvoices.invoicenumber#');">Pay Now</button>
						</cfif>
					<cfelse>
						<button type="button" disabled>Pay Now</button>
					</cfif>
				</td>
			</tr>
		</cfoutput>
		</table>
		</div>
	</div>
	<br/>
	<a href="##" onclick="payAnotherInvoice(); return false;">I need to pay for another invoice<i id="icon" class="icon-caret-down"></i></a>
	</div>
	<br/>
<cfelse>
	<div class="tsAppBodyText">
		There are no outstanding invoices.<br/>
		<cfif NOT variables.isBot>
			However, you may pay another invoice by locating it below.
		</cfif>
	</div>
	<br/><br/>
</cfif>

<cfif NOT variables.isBot>
	<div id="container" class="d-none">
		<cfoutput>
			<cfif arguments.event.getValue('memberid') is 0>
				<table class="tsAppBodyText">
				<tr>
					<td nowrap><cfinclude template="frm_search.cfm"></td>
					<td width="46" align="center"><img src="/assets/common/images/line-or.gif" width="20" height="140" alt="" /></td>
					<td>
						<div class="tsAppLegendTitle" style="border:1px solid ##ccc;padding:20px;line-height:1.5em;">
							<a href="/?pg=login">Login to #arguments.event.getValue('mc_siteinfo.sitename')#</a><br/>
							to review and pay any of<br/>
							your outstanding invoices.
						</div>
					</td>
				</tr>
				</table>
			<cfelse>
				<cfinclude template="frm_search.cfm">
			</cfif>
		</cfoutput>
	</div>
</cfif>