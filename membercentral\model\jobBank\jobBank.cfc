<cfcomponent extends="model.AppLoader" output="no">

	<cfset defaultEvent = "controller">
	<cfset variables.instanceSettings = structNew()/>
	<cfset variables.myRights = structNew() />
	<cfset variables.applicationReservedURLParams = 'action,do,jobID,resumeID,page,mid,orderColumn,orderBy,documentid' />
	<cfset variables.applicationTabs = "SearchJobs,SearchResumes,MyJobs,ManageJob,MyResume,ManageResume,Manage" />
	<cfset variables.baseQueryString = '' />
	<cfset variables.baseURL = '' />

	<cffunction name="init" access="public" returntype="void" output="false">
		<cfset variables.instanceSettings = getInstanceSettings(this.appInstanceID) />
		<cfset variables.myRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=variables.instanceSettings.siteID) />
		<cfset variables.baseQueryString = getBaseQueryString(false,true) />
		<cfset variables.baseURL = '/?#variables.baseQueryString#' />
		<cfset variables.objJB	= CreateObject('component','model.admin.jobBank.jobBank')>
	</cffunction>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfscript>
			var local 				= structNew();
			local.thisApp 			= structNew();
			init();
		</cfscript>
		
		<cfif NOT isValidURLParams(arguments.event)>
			<cflocation url="#variables.baseURL#" addtoken="false">
		</cfif>

		<cfscript>
			local.jbSettings = variables.instanceSettings;
			variables.siteResourceID = local.jbSettings.siteResourceID;
			if( local.jbSettings.disabled AND arguments.event.getValue('action','') NEQ 'disabled'){
				// if jobBank is disabled then force page to the disabled screen
				arguments.event.setValue('action','disabled');
			}
			// -----------------------------------------------------------------------------
			arguments.event.setValue('action',arguments.event.getValue('action','SearchJobs'));
			local.pageHeader = buildHeader(arguments.event,local.jbSettings.ApplicationInstanceName);
			local.pageFooter = buildFooter();
			local.showHeaderFooter = FALSE;
			if( listFindNoCase(variables.applicationTabs,arguments.event.getValue('action','SearchJobs')) ){ local.showHeaderFooter = TRUE; }
			local.NoRightsURL = "#variables.baseURL#&Action=NoRights";
			
			local.refresh.JBJ											= structNew();
			local.refresh.JBJ.JobTitle 						= '';
			local.refresh.JBJ.City 								= '';
			local.refresh.JBJ.State 							= '';
			local.refresh.JBJ.Employer 						= '';
			local.refresh.JBJ.PositionDescription	= '';
			local.refresh.JBJ.EmploymentTypeID 		= '';
			local.refresh.JBJ.CategoryID 					= '';
			local.refresh.JBJ.Days 								= '';
			local.refresh.JBJ.isSubmit 						= 0;
			
			local.refresh.JBR										= structNew();
			local.refresh.JBR.Objective 							= '';
			local.refresh.JBR.City 							= '';
			local.refresh.JBR.State 						= '';
			local.refresh.JBR.EmploymentTypeID 	= '';
			local.refresh.JBR.CategoryID 				= '';
			local.refresh.JBR.Days 							= '';
			local.refresh.JBR.isSubmit 					= 0;
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif local.showHeaderFooter>#local.pageHeader#</cfif>
					<cfswitch expression="#arguments.event.getValue('action','SearchJobs')#">

						<cfcase value="redirectNewMember">
							<cfset local.strTemp = { memberID=arguments.event.getValue('mid',0), returnURL=arguments.event.getValue('retURL','') }>
							<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
							<cfif local.strTemp.memberID gt 0 and IsArray(local.newMemIdArr) and listFind(ArrayToList(local.newMemIdArr),local.strTemp.memberID) and len(local.strTemp.returnURL)>
								<cfif application.objMember.getMemberInfo(local.strTemp.memberID).recordCount>
									<cfset local.strRequest = CreateObject("component","model.login.login").generateResetRequest(
											siteid=arguments.event.getValue('mc_siteinfo.siteID'),
											memberid=local.strTemp.memberID,
											expire=0,
											resetMemberID=local.strTemp.memberID,
											email=''
											)>
									<cfset local.redirectToURL = "#arguments.event.getValue('mc_siteInfo.scheme')#://#arguments.event.getValue('mc_siteinfo.mainHostName')#/?pg=login&logact=resetPassword&id=#local.strRequest.encString#&returnURL=#urlencodedFormat(local.strTemp.returnURL)#">
									<cflocation url="#local.redirectToURL#" addtoken="false">
								<cfelse>
									<cflocation url="#variables.baseURL#" addtoken="false">
								</cfif>
							<cfelseif len(local.strTemp.returnURL)>
								<cflocation url="#local.strTemp.returnURL#" addtoken="false">
							<cfelse>
								<cflocation url="#variables.baseURL#" addtoken="false">
							</cfif>
						</cfcase>

						<cfcase value="SearchJobs">
							
							<cfswitch expression="#arguments.event.getValue('orderColumn','')#">
								<cfcase value="job,emp,st,city,date">
									<cfset local.orderColumn = arguments.event.getValue('orderColumn')>
								</cfcase>
								<cfdefaultcase>
									<cfset local.orderColumn = ''>
								</cfdefaultcase>
							</cfswitch>
							<cfswitch expression="#arguments.event.getValue('orderBy','')#">
								<cfcase value="asc,desc">
									<cfset local.orderBy = arguments.event.getValue('orderBy')>
								</cfcase>
								<cfdefaultcase>
									<cfset local.orderBy = ''>
								</cfdefaultcase>
							</cfswitch>
							
							<cfset local.baseURL = "#variables.baseURL#&action=#event.getValue('action')#" />
							<cfset local.resultsURL = "#local.baseURL#&do=results" />
							
							<cfset local.JBJ = application.mcCacheManager.sessionGetValue(keyname='JBJ', defaultValue={})>
							<cfswitch expression="#arguments.event.getValue('do','search')#">
								
								<cfcase value="search">
									<cfset application.mcCacheManager.sessionSetValue(keyname='JBJ', value=local.refresh.JBJ)>
									<cfinclude template="frm_searchJobs.cfm">
								</cfcase>
								<cfcase value="results">
									<cfscript>
										// PARAM SEARCH FIELDS: --------------------------------------------------------//
										arguments.event.paramValue('JobTitle','');
										arguments.event.paramValue('City','');
										arguments.event.paramValue('State','');
										arguments.event.paramValue('Employer','');
										arguments.event.paramValue('PositionDescription','');
										arguments.event.paramValue('employmentTypeID','');
										arguments.event.paramValue('CategoryID','');
										arguments.event.paramValue('Days','');
										// if for was Submitted LOAD SESSION: -------------------------------------------//
										if( event.getValue('isSubmit',0) ){
											local.JBJ.JobTitle 						= event.getValue('JobTitle');
											local.JBJ.City 								= event.getValue('City');
											local.JBJ.State 							= event.getValue('State');
											local.JBJ.Employer 						= event.getValue('Employer');
											local.JBJ.PositionDescription = event.getValue('PositionDescription');
											local.JBJ.employmentTypeID 		= event.getValue('EmploymentTypeID');
											local.JBJ.CategoryID 					= event.getValue('CategoryID');
											local.JBJ.Days 								= event.getValue('Days');
											local.JBJ.isSubmit 						= event.getValue('isSubmit');
										}
										application.mcCacheManager.sessionSetValue(keyname='JBJ', value=local.JBJ);
										// param pagination values: -----------------------------------------------------//
										event.paramValue('page',1);
										event.paramValue('nextPage',event.getValue('page',0) + 1);
										event.paramValue('prevPage',event.getValue('page',0) - 1);
										// RUN QUERY: -------------------------------------------------------------------//
										local.jobResults = getJobSearch(local.jbSettings.jobBankID,local.jbSettings.maxDaysForPostingJobs,local.orderColumn,local.orderBy,session.cfcuser.memberData.memberID);
									</cfscript>
									<cfinclude template="dsp_JobSearchResults.cfm">
								</cfcase>
								<cfcase value="details">
									<cfif NOT isNumeric(arguments.event.getValue('jobID'))>
										<cflocation url="#variables.baseURL#" addtoken="false">
									</cfif>
									<cfset local.qryJobDetails = getJobDetails(arguments.event.getValue('jobID'),local.jbSettings.jobBankID,local.jbSettings.maxDaysForPostingJobs) />
									<cfset local.currentmember = StructNew()>
									<cfloop list="#ArrayToList(local.qryJobDetails.getColumnNames())#" index="local.col" >
										<cfset local.currentmember["#local.col#"] = local.qryJobDetails[local.col]>
									</cfloop>
									
									<cfif len(local.currentmember.positionDescriptionContentID)>
										<cfset local.currentmember.positionDescription 		= variables.objJB.showContentEditor("jb_PositionDescription", local.currentmember.positionDescriptionContentID) />
									<cfelse>
										<cfset local.currentmember.positionDescription 		= variables.objJB.showContentEditor("jb_PositionDescription", 0) />
										<cfset local.currentmember.positionDescriptionContentID = 0>
									</cfif>
									<cfif len(local.currentmember.howToApplyContentID)>
										<cfset local.currentmember.howToApply 		= variables.objJB.showContentEditor("jb_HowToApply", local.currentmember.howToApplyContentID) />
									<cfelse>
										<cfset local.currentmember.howToApply 		= variables.objJB.showContentEditor("jb_HowToApply", 0) />
										<cfset local.currentmember.howToApplyContentID = 0>
									</cfif>
									<cfif len(local.currentmember.requiredExperienceContentID)>
										<cfset local.currentmember.requiredExperience 		= variables.objJB.showContentEditor("jb_RequiredExperience", local.currentmember.requiredExperienceContentID) />
									<cfelse>
										<cfset local.currentmember.requiredExperience 		= variables.objJB.showContentEditor("jb_RequiredExperience", 0) />
										<cfset local.currentmember.requiredExperienceContentID = 0>
									</cfif>
									<cfif len(local.currentmember.requiredSkillsContentID)>
										<cfset local.currentmember.requiredSkills 		= variables.objJB.showContentEditor("jb_RequiredSkills", local.currentmember.requiredSkillsContentID) />
									<cfelse>
										<cfset local.currentmember.requiredSkills 		= variables.objJB.showContentEditor("jb_RequiredSkills", 0) />
										<cfset local.currentmember.requiredSkillsContentID = 0>
									</cfif>
									
									<cfinclude template="dsp_jobDetails.cfm">
								</cfcase>
							</cfswitch>
						</cfcase>

						<cfcase value="SearchResumes">
							<cfif val(variables.myRights.jbBrowseResumes)>
								<cfswitch expression="#arguments.event.getValue('orderColumn','')#">
									<cfcase value="res,loc,dpos,dava,cat">
										<cfset local.orderColumn = arguments.event.getValue('orderColumn')>
									</cfcase>
									<cfdefaultcase>
										<cfset local.orderColumn = ''>
									</cfdefaultcase>
								</cfswitch>
								<cfswitch expression="#arguments.event.getValue('orderBy','')#">
									<cfcase value="asc,desc">
										<cfset local.orderBy = arguments.event.getValue('orderBy')>
									</cfcase>
									<cfdefaultcase>
										<cfset local.orderBy = ''>
									</cfdefaultcase>
								</cfswitch>
								
								<cfset local.baseURL = "#variables.baseURL#&action=#event.getValue('action')#" />
								<cfset local.resultsURL = "#local.baseURL#&do=results" />
								
								<cfset local.JBR = application.mcCacheManager.sessionGetValue(keyname='JBR', defaultValue={})>
								<cfswitch expression="#arguments.event.getValue('do','search')#">
									<cfcase value="search">
										<cfset local.inS = local.jbSettings />
										<cfset application.mcCacheManager.sessionSetValue(keyname='JBR', value=local.refresh.JBR)>
										<cfif (NOT val(local.inS.AllowSearchObjective)) AND 
											(NOT val(local.inS.AllowSearchCity)) AND 
											(NOT val(local.inS.AllowSearchState)) AND 
											(NOT val(local.inS.AllowSearchDatePosted)) AND 
											(NOT val(local.inS.AllowSearchWorkStatus))>
											<div class="tsAppBodyText">Searching is not available at this time.</div>
										<cfelse>
											<cfinclude template="frm_searchResumes.cfm">
										</cfif>
									</cfcase>
									<cfcase value="results">
										<cfscript>
											// PARAM SEARCH FIELDS: --------------------------------------------------------//
											arguments.event.paramValue('Objective','');
											arguments.event.paramValue('City','');
											arguments.event.paramValue('State','');
											arguments.event.paramValue('EmploymentTypeID','');
											arguments.event.paramValue('CategoryID','');
											arguments.event.paramValue('Days','');
											// if for was Submitted LOAD SESSION: -------------------------------------------//
											if( event.getValue('isSubmit',0) ){
												local.JBR.Objective 						= event.getValue('Objective');
												local.JBR.City 								= event.getValue('City');
												local.JBR.State 							= event.getValue('State');
												local.JBR.employmentTypeID 		= event.getValue('EmploymentTypeID');
												local.JBR.CategoryID 					= event.getValue('CategoryID');
												local.JBR.Days 								= event.getValue('Days');
												local.JBR.isSubmit 						= event.getValue('isSubmit');
											}
											application.mcCacheManager.sessionSetValue(keyname='JBR', value=local.JBR);
											// param pagination values: -----------------------------------------------------//
											event.paramValue('page',1);
											event.paramValue('nextPage',event.getValue('page',0) + 1);
											event.paramValue('prevPage',event.getValue('page',0) - 1);
											// RUN QUERY: -------------------------------------------------------------------//
											local.ResumeResults = getResumeSearch(local.jbSettings.jobBankID,local.jbSettings.maxDaysForPostingResumes,local.orderColumn,local.orderBy);
										</cfscript>
										<cfinclude template="dsp_ResumeSearchResults.cfm">
									</cfcase>
									<cfcase value="details">
										<cfif Not (variables.myRights.jbBrowseFull OR variables.myRights.jbEditAll)>
											<div >You are not authorized to view full resume listing.</div>
										<cfelse>
											<cfset local.currentmember = getResumeDetails(arguments.event.getValue('resumeID'),local.jbSettings.jobBankID,local.jbSettings.maxDaysForPostingResumes) />
											<cfinclude template="dsp_ResumeDetails.cfm">
										</cfif>
									</cfcase>
								</cfswitch>
							</cfif>
						</cfcase>

						<cfcase value="MyJobs,ManageJob">
							<cfif val(variables.myRights.jbPostJobs) OR val(variables.myRights.jbEditAll)>
								<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
									<cfset local.baseURL = "#variables.baseURL#&Action=#arguments.event.getValue('Action')#">
									<cfset local.payURL = "#variables.baseURL#&Action=paypending&mode=direct">
									<cfset local.qryStates = application.objCommon.getStates() />
									<cfquery name="local.qryStates" dbtype="query">
										select * from [local].qryStates where countryID in (1,2)
									</cfquery>							
									<cfswitch expression="#arguments.event.getValue('do','list')#">
										<cfcase value="list">
											<cfset local.qryJobs = getMembersJobs(session.cfcUser.memberData.memberID,local.jbSettings.jobBankID,local.jbSettings.maxDaysForPostingJobs) />
											<cfset local.verifyTaxInfoURL = "#variables.baseURL#&Action=verifyTaxInfo&mode=direct">
											<cfinclude template="MyJobs.cfm">
										</cfcase>
										<cfcase value="Add,Edit">
											<cfset local.formaction ="#local.baseURL#&do=Save" />
											<cfset local.formData = buildJobFormData(event.getValue('jobId',0),local.jbSettings.jobBankID,session.cfcUser.memberData.memberID,local.jbSettings.maxDaysForPostingJobs,variables.myRights.jbEditAll) />
											<cfinclude template="frm_Job.cfm">
										</cfcase>
										<cfcase value="Save">											
											<cfset local.formData = structNew() />
											<cfloop collection="#arguments.event.getCollection()#" item="local.key">
												<cfif findNoCase("jb_",local.key)>
													<cfset structInsert(local.formData,local.key,arguments.event.getValue(local.key)) />
												</cfif>
											</cfloop>
											<cfif val(arguments.event.getValue('jb_jobID',0))>
												<cfif val(variables.myRights.jbEditAll) OR doYouOwnThisJob(jobID=arguments.event.getValue('jb_jobID',0),memberID=session.cfcUser.memberData.memberID,jobBankID=local.jbSettings.jobBankId)>
													<cfset local.jobID = updateJob(local.formData,local.jbSettings.jobBankID,variables.instanceSettings) />
												</cfif>
											<cfelse>
												<cfset local.jobID = addJob(local.formData,local.jbSettings.jobBankID,session.cfcUser.memberData.memberID,local.jbSettings.maxDaysForPostingJobs,variables.instanceSettings) />
												<cfscript>
													local.notifyEmail = CreateObject('component','model.admin.jobBank.jobBank').getJobBankNotifyEmails(local.jbSettings.jobBankID);
													local.mailData.to = local.notifyEmail;
													local.mailData.subject = 'New Job Was Created by #session.cfcUser.memberData.FirstName# #session.cfcUser.memberData.LastName#';
													local.mailData.message = 'A new job has been created on #local.jbSettings.APPLICATIONINSTANCENAME# by #session.cfcUser.memberData.FirstName# #session.cfcUser.memberData.lastName#.';
													local.mailData.emailTitle = "#arguments.event.getValue('mc_siteinfo.sitename')# Job Bank";
													local.mailData.emailAttachments = [];
													local.mailData.resourceId = variables.siteResourceID;
													local.mailData.memberID = session.cfcUser.memberData.memberID;
												
													sendEmail(event=arguments.event, maildata=local.mailData);
												</cfscript>
												
											</cfif>
											<cfif arguments.event.getValue('action','') EQ 'MyJobs'>
												<cflocation url="#local.baseURL#" addtoken="false">
											<cfelse>
												<cflocation url="#variables.baseURL#&Action=Manage" addtoken="false">
											</cfif>
										</cfcase>
										<cfcase value="Delete">
											<cfscript>
												local.jobID = event.getValue('jobID');
												if( val(variables.myRights.jbEditAll) OR doYouOwnThisJob(jobID=local.jobID,memberID=session.cfcUser.memberData.memberID,jobBankID=local.jbSettings.jobBankID) ){
													deleteJobPosting(local.jobID,local.jbSettings.jobBankID);
													if( arguments.event.getValue('action','') EQ 'MyJobs' ){
														application.objCommon.redirect('#local.baseURL#');
													}
													else{
														application.objCommon.redirect('#variables.baseURL#&Action=Manage');
													}
												}
												else{
													application.objCommon.redirect('#local.NoRightsURL#');
												}
											</cfscript>
										</cfcase>
									</cfswitch>
								<cfelse>
									<cfset local.alternateGuestAccountCreationLink = arguments.event.getValue('mc_siteInfo.alternateGuestAccountCreationLink')>

 									<script>
									function createAccount() {
 										$.colorbox( {innerWidth:650, innerHeight:520, href:'/?pg=accountLocator&alAction=locator&bypna=1&autoClose=0&retFunc=redirectNewMember', iframe:true, overlayClose:false} );
 									}
 									function resizeBox(newW,newH) { $.colorbox.resize({innerWidth:newW,innerHeight:newH});}
 									function redirectNewMember(memObj) {
 										$('##divLoginButtons').html('Please wait while we redirect you.');
 										if (memObj.memberID)
 											self.location.href = '#variables.baseURL#&action=redirectNewMember&mid=' + memObj.memberID + '&retURL=' + escape(self.location.href);
 										$.colorbox.close();
 									}
 									</script>

 									<div id="divLoginButtons" class="tsAppBodyText">
 										To access this area of #local.jbSettings.ApplicationInstanceName#, you will need to login or create an account.
 										<br/><br/>
 										<button class="tsAppBodyButton" onClick="self.location.href='/?pg=login'">Login</button>
 										&nbsp; or &nbsp; 
 										<cfif len(local.alternateGuestAccountCreationLink)>
 											<button class="tsAppBodyButton" onClick="self.location.href='#local.alternateGuestAccountCreationLink#';">Create Account</button>
 										<cfelse>
 											<button class="tsAppBodyButton" onClick="createAccount();">Create Account</button>
 										</cfif>
 									</div>
								</cfif>
							<cfelse>
								<cflocation url="#local.NoRightsURL#" addtoken="false">
							</cfif>
						</cfcase>

						<cfcase value="MyResume,ManageResume">
							<cfif val(variables.myRights.jbPostResumes) OR val(variables.myRights.jbEditAll)>
								<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
									<cfset local.baseURL = "#variables.baseURL#&Action=#arguments.event.getValue('Action')#">
									<cfset local.qryStates = application.objCommon.getStates() />
									<cfquery name="local.qryStates" dbtype="query">
										select * from [local].qryStates where countryID in (1,2)
									</cfquery>								
									<cfswitch expression="#arguments.event.getValue('do','list')#">
										<cfcase value="list">
											<cfset local.qryResumes = getMembersResumes(session.cfcUser.memberData.memberID,local.jbSettings.jobBankId,local.jbSettings.maxDaysForPostingResumes) />
											<cfinclude template="MyResume.cfm">
										</cfcase>
										<cfcase value="Add,Edit">
											<cfset local.formaction ="#local.baseURL#&do=Save" />
											<cfset local.formData = buildResumeFormData(event.getValue('resumeID',0),local.jbSettings.jobBankId,session.cfcUser.memberData.memberID,local.jbSettings.maxDaysForPostingResumes,variables.myRights.jbEditAll) />
											<cfinclude template="frm_Resume.cfm">
										</cfcase>
										<cfcase value="Save">											
											<cfset local.formData = structNew() />
											<cfloop collection="#arguments.event.getCollection()#" item="local.key">
												<cfif findNoCase("jb_",local.key)>
													<cfset structInsert(local.formData,local.key,arguments.event.getValue(local.key)) />
												</cfif>
											</cfloop>
											<cfset local.returnStruct.fileUploaded 	= FALSE />
											<cfset local.returnStruct.reasonText 		= '' />
											<cfset local.returnStruct.documentID 		= 0 />
											<cfscript>
												/* if resumeID is 0 OR (resumeID NOT 0 and (you own the resume OR you have editAll rights)) */
												if( ( arguments.event.getValue('jb_ResumeID',0) EQ 0 ) OR	( arguments.event.getValue('jb_ResumeID',0) NEQ 0 AND ( doYouOwnThisResume(arguments.event.getValue('jb_ResumeID',0),local.jbSettings.jobBankId,session.cfcUser.memberData.memberID) OR val(variables.myRights.jbEditAll))) ){
													// LOAD DOCUMENT: ----------------------------------------------------------------- //
													local.jbSectionID = getJobBankSectionID(local.jbSettings.siteResourceID);
													local.formData.jb_documentID = event.getValue('jb_documentID',0);
													if( len( trim( event.getValue('newFile') ) ) ){
														event.setValue('sectionID',local.jbSectionID);
														event.setValue('docTitle',local.formData.jb_ResumeTitle);
														event.setValue('docDesc',local.formData.jb_ResumeTitle);
														local.uploadedDoc = actionAddDocument(event,local.jbSettings.siteResourceID);
														local.formData.jb_documentID = local.uploadedDoc.documentID;
													}
												}
												// UPDATE RESUME: ----------------------------------------------------------------- //
												if( val(arguments.event.getValue('jb_ResumeID',0)) ){
													if( variables.myRights.jbEditALL OR doYouOwnThisResume(arguments.event.getValue('jb_ResumeID',0),local.jbSettings.jobBankId,session.cfcUser.memberData.memberID) ){
														local.resumeID = updateResume(local.formData,local.jbSettings.jobBankID);
													}
												}
												// INSERT RESUME: ----------------------------------------------------------------- //
												else{
													// add resume
													local.resumeID = addResume(local.formData,local.jbSettings.jobBankID,session.cfcUser.memberData.memberID,local.jbSettings.maxDaysForPostingResumes);
													// build email struct
													local.notifyEmail = CreateObject('component','model.admin.jobBank.jobBank').getJobBankNotifyEmails(local.jbSettings.jobBankID);
													local.mailData.to = local.notifyEmail;
													local.mailData.subject = 'New Resume Was Created by #session.cfcUser.memberData.FirstName# #session.cfcUser.memberData.LastName#';
													local.mailData.message = 'A new resume has been created on #local.jbSettings.APPLICATIONINSTANCENAME# by #session.cfcUser.memberData.FirstName# #session.cfcUser.memberData.lastName#.';
													local.mailData.emailTitle = "#arguments.event.getValue('mc_siteinfo.sitename')# Job Bank";
													local.mailData.emailAttachments = [];
													local.mailData.resourceId = variables.siteResourceID;
													local.mailData.memberID = session.cfcUser.memberData.memberID;
													// send mail
													sendEmail(event=arguments.event, maildata=local.mailData);
												}
												// LOCATE USER BACK: ----------------------------------------------------------------- //
												if( arguments.event.getValue('action','') EQ 'MyResume' ){
													application.objCommon.redirect('#local.baseURL#');
												}
												else{ 
													application.objCommon.redirect('#variables.baseURL#&Action=Manage');
												}
											</cfscript>
										</cfcase>
										<cfcase value="Delete">
											<cfscript>
												// DELETE RESUME: ----------------------------------------------------------------- //
												local.resumeID = event.getValue('resumeID');
												if( variables.myRights.jbEditALL OR doYouOwnThisResume(local.resumeID,local.jbSettings.jobBankId,session.cfcUser.memberData.memberID) ){
													deleteResume(siteID=local.jbSettings.siteID, resumeID=local.resumeID, jobBankID=local.jbSettings.jobBankId);
													// LOCATE USER BACK: ----------------------------------------------------------------- //
													if( arguments.event.getValue('action','') EQ 'MyResume' ){
														application.objCommon.redirect('#local.baseURL#');
													}
													else{ 
														application.objCommon.redirect('#variables.baseURL#&Action=Manage');
													}
												}
												else{
													application.objCommon.redirect('#local.NoRightsURL#');
												}
											</cfscript>
										</cfcase>
										<cfcase value="DeleteDoc">
											<cfscript>
												// do you own the resume or do you have EditAll rights
												if( variables.myRights.jbEditALL OR doYouOwnThisResume(arguments.event.getValue('resumeID'),local.jbSettings.jobBankId,session.cfcUser.memberData.memberID) ){
													// DELETE DOCUMENT FROM RESUME ------------------------------------------------------- //
													removeDocFromResume(siteID=local.jbSettings.siteID, documentID=arguments.event.getValue('documentID'), jobBankID=local.jbSettings.jobBankID);
													// LOCATE USER BACK: ----------------------------------------------------------------- //
													application.objCommon.redirect("#local.baseURL#&do=edit&resumeID=#arguments.event.getValue('resumeID')#");
												}
												else{
													application.objCommon.redirect('#local.NoRightsURL#');
												}
											</cfscript>
										</cfcase>
									</cfswitch>
								<cfelse>
									<cfset local.alternateGuestAccountCreationLink = arguments.event.getValue('mc_siteInfo.alternateGuestAccountCreationLink')>

 									<script>
									function createAccount() {
 										$.colorbox( {innerWidth:650, innerHeight:520, href:'/?pg=accountLocator&alAction=locator&bypna=1&autoClose=0&retFunc=redirectNewMember', iframe:true, overlayClose:false} );
 									}
 									function resizeBox(newW,newH) { $.colorbox.resize({innerWidth:newW,innerHeight:newH});}
 									function redirectNewMember(memObj) {
 										$('##divLoginButtons').html('Please wait while we redirect you.');
 										if (memObj.memberID)
 											self.location.href = '#variables.baseURL#&action=redirectNewMember&mid=' + memObj.memberID + '&retURL=' + escape(self.location.href);
 										$.colorbox.close();
 									}
 									</script>

 									<div id="divLoginButtons" class="tsAppBodyText">
 										To access this area of #local.jbSettings.ApplicationInstanceName#, you will need to login or create an account.
 										<br/><br/>
 										<button class="tsAppBodyButton" onClick="self.location.href='/?pg=login'">Login</button>
 										&nbsp; or &nbsp; 
 										<cfif len(local.alternateGuestAccountCreationLink)>
 											<button class="tsAppBodyButton" onClick="self.location.href='#local.alternateGuestAccountCreationLink#';">Create Account</button>
 										<cfelse>
 											<button class="tsAppBodyButton" onClick="createAccount();">Create Account</button>
 										</cfif>
 									</div>
								</cfif>
							<cfelse>
								<cflocation url="#local.NoRightsURL#" addtoken="false">
							</cfif>
						</cfcase>

						<cfcase value="verifyTaxInfo">
							<cfset local.jobID = arguments.event.getValue('jobId',0)>
							<cfset local.jobData = getJobData(jobBankID=local.jbSettings.jobBankID, jobID=local.jobID, orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=session.cfcUser.memberData.memberID)>
							<cfif len(local.jobData.zipForTax) AND NOT application.objCommon.checkBillingZIP(billingZip=local.jobData.zipForTax, billingStateID=val(local.jobData.stateIDForTax)).isvalidzip>
								<cfset local.jobData.zipForTax = "">
							</cfif>
							<!--- if tax info is not present --->
							<cfif local.jobData.recordCount and not (local.jobData.stateIDForTax gt 0 and len(local.jobData.zipForTax))>
								<cfset local.qryStates = application.objCommon.getStates()>
								<cfset local.saveTaxInfoURL = "#variables.baseURL#&Action=saveTaxInfo&mode=stream">
								<cfinclude template="frm_TaxInfo.cfm">
							<cfelse>
								<cfset local.retData = getBuyNowRedirectResponseScript(jobBankID=variables.instanceSettings.jobBankID, jobID=local.jobID)>								
								<cfreturn returnAppStruct(local.retData,"echo")>
							</cfif>
						</cfcase>

						<cfcase value="saveTaxInfo">
							<cfset local.jobID = arguments.event.getValue('jobId',0)>
							<cfset local.stateIDForTax = val(arguments.event.getValue("stateIDforTax",0))>
							<cfset local.zipForTax = arguments.event.getValue("zipForTax",'')>
							<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=local.stateIDForTax)>
							<cfif local.strBillingZip.isvalidzip>
								<cfset local.zipForTax = local.strBillingZip.billingzip>
							<cfelse>
								<cfthrow message="Invalid State/Zip.">
							</cfif>
							<cfset local.strJobBankTax = { stateID=local.stateIDForTax, zip=local.zipForTax }>
							<cfset application.mcCacheManager.sessionSetValue(keyname='strJobBankTax', value=local.strJobBankTax)>
							<cfset local.retData = getBuyNowRedirectResponseScript(jobBankID=variables.instanceSettings.jobBankID, jobID=local.jobID)>								
							<cfreturn returnAppStruct(local.retData,"echo")>							
						</cfcase>

						<cfcase value="Manage">
							<cfif val(variables.myRights.jbEditAll)>
								<cfswitch expression="#arguments.event.getValue('do','list')#">
									<cfcase value="list">
										<div id="RESUMES">
											<!--- RESUMES: --->
											<cfset local.baseURL = "#variables.baseURL#&Action=ManageResume" />
											<cfset local.qryResumes = getJobBankResumes(local.jbSettings.jobBankID,local.jbSettings.maxDaysForPostingResumes)>
											<cfinclude template="MyResume.cfm">
										</div>
										<div id="SPACER" style="padding-bottom:25px;"></div>
										<div id="JOBS">
											<!--- JOBS: --->
											<cfset local.baseURL = "#variables.baseURL#&Action=ManageJob">
											<cfset local.payURL = "#variables.baseURL#&Action=paypending&mode=direct">
											<cfset local.qryJobs = getJobBankJobs(local.jbSettings.jobBankID,local.jbSettings.maxDaysForPostingJobs)>
											<cfset local.verifyTaxInfoURL = "#variables.baseURL#&Action=verifyTaxInfo&mode=direct">
											<cfinclude template="MyJobs.cfm">
										</div>
									</cfcase>
								</cfswitch>
							<cfelse>
								<cflocation url="#local.NoRightsURL#" addtoken="false">
							</cfif>
						</cfcase>
						
						<cfcase value="NoRights">
										
										<style type="text/css">
										<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) is 1>
											##noRightsLoggedOut{ display:none;}
										<cfelse>
											##noRightsLoggedIn{ display:none;}
										</cfif>
										</style>
										
										<cfset local.LanguageID = arguments.event.getValue('mc_siteinfo.defaultLanguageID')>
										<cfset local.noRightsContentID = arguments.event.getValue('mc_siteinfo.noRightsContentID')>
										<cfset local.tmpContent = application.objCMS.getStaticContent(local.noRightsContentID,local.LanguageID)>
										<cfif len(local.tmpContent.rawContent)>
											<cfset local.tmpStrRes.data = local.tmpContent.rawContent>
										</cfif>
										
										<div>
											#local.tmpStrRes.data#
										</div>
						</cfcase>
						
						<cfcase value="disabled">
							<div>
								This Job Bank is currently disabled.<br />
								Please contact the Association for further assistance.
							</div>
						</cfcase>
						
					</cfswitch>
				<cfif local.showHeaderFooter>#local.pageFooter#</cfif>
			</cfoutput>
		</cfsavecontent>
		<!--- return the app struct --->
		<cfreturn returnAppStruct(local.data,"echo")>
		<!--- <cfreturn returnAppStruct(local.returnStruct,local.viewToUse)> --->
	</cffunction>	
	<!--- JOBS:------------------------------------------------------------------- --->
	<cffunction name="doYouOwnThisJob" access="private" returntype="boolean">
		<cfargument name="jobID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="jobBankID" type="numeric" required="true">
		
		<cfset var local = structNew() />
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SELECT distinct jbj.jobID
			FROM dbo.JobBankJobs jbj
			inner join dbo.ams_members m on m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			inner join dbo.ams_members mMerged on mMerged.activeMemberID = m.activeMemberID
			WHERE jobID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobID#">
				AND jbj.memberID = mMerged.memberID
				AND jbj.IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="0">
				AND jbj.jobBankId = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobBankId#">
		</cfquery>
		
		<cfif local.data.recordCount>
			<cfreturn True />
		<cfelse>
			<cfreturn False />
		</cfif>
		
	</cffunction>

	<cffunction name="getJobStates" access="public" returntype="query">
		<cfargument name="jobBankId" type="numeric" required="yes">
		<cfargument name="maxDaysForPostingJobs" type="numeric" required="true">
		
		<cfset var local = structNew() />
		
		<cfquery name="local.states" datasource="#application.dsn.membercentral.dsn#">
			select distinct state 
			from dbo.JobBankJobs
			where jobBankId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobBankID#">
				AND IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="0">
				AND hasPaid = <cfqueryparam cfsqltype="CF_SQL_BIT" value="1">
				<cfif arguments.maxDaysForPostingJobs NEQ '' OR arguments.maxDaysForPostingJobs NEQ 0>
					AND GETDATE() <=  removeOnDate
				</cfif>
			order by state
		</cfquery>
		
		<cfreturn local.states />
	</cffunction>

	<cffunction name="getJobBankJobs" access="public" returntype="query" output="false">
		<cfargument name="jobBankId" type="numeric" required="true">
		<cfargument name="maxDaysForPostingJobs" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SELECT mActive.FirstName + ' ' + mActive.LastName AS PostedBy, jbj.*
			FROM dbo.ams_members m
				INNER JOIN dbo.JobBankJobs jbj ON m.memberID = jbj.memberID
				INNER JOIN dbo.ams_members mActive on mActive.memberID = m.activeMemberID
			WHERE jbj.IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="0"> 
				AND jbj.jobBankId = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobBankId#">
				<cfif arguments.maxDaysForPostingJobs NEQ '' OR arguments.maxDaysForPostingJobs NEQ 0>
					AND GETDATE() <=  jbj.removeOnDate
				</cfif>
			ORDER BY jbj.hasPaid, jbj.DatePosted DESC
		</cfquery>
		
		<cfreturn local.data />
	</cffunction>

	<cffunction name="getMembersJobs" access="public" returntype="query" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="jobBankId" type="numeric" required="true">
		<cfargument name="maxDaysForPostingJobs" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SELECT mActive.FirstName + ' ' + mActive.LastName AS PostedBy, jbj.*
			FROM dbo.ams_members m
				INNER JOIN dbo.ams_members mActive on mActive.memberID = m.activeMemberID
				INNER JOIN dbo.ams_members mMerged on mMerged.activeMemberID = m.activeMemberID
				INNER JOIN dbo.JobBankJobs jbj ON jbj.memberID = mMerged.memberID
			WHERE jbj.IsDeleted = 0
				AND jbj.jobBankId = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobBankId#">
				AND m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				<cfif arguments.maxDaysForPostingJobs NEQ '' OR arguments.maxDaysForPostingJobs NEQ 0>
					AND GETDATE() <=  jbj.removeOnDate
				</cfif>
			ORDER BY jbj.hasPaid, jbj.DatePosted DESC
		</cfquery>
		
		<cfreturn local.data />
	</cffunction>

	<cffunction name="getJobToEdit" access="public" returntype="query">
		<cfargument name="jobId" type="numeric" required="yes">
		<cfargument name="jobBankID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="maxDaysForPostingJobs" type="numeric" required="true">
		<cfargument name="editALLRight" type="numeric" required="true">
		
		<cfset var local = structNew() />
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SELECT 
				jbj.jobID, jbj.jobBankID, jbj.memberID, jbj.firstName, jbj.lastName, jbj.contactTitle, jbj.contactFirm, jbj.phone, 
				jbj.fax, jbj.emailAddress, jbj.jobTitle, jbj.employmentTypeID, jbj.employer, jbj.city, jbj.state
				,jbj.minSalary, jbj.maxSalary, jbj.salaryUnit, jbj.workExperienceID, jbj.educationLevelID, jbj.dateOpen, jbj.datePosted
				, jbj.showEmployerInfo
				, jbj.categoryID, jbj.hasPaid, 
				jbj.removeOnDate, jbj.isDeleted, jbj.purchasedPrice, jbj.status, jbj.howToApplyContentId, jbj.requiredExperienceContentId, jbj.requiredSkillsContentId, jbj.positionDescriptionContentId
			FROM 
				dbo.JobBankJobs jbj
				INNER JOIN dbo.JobBank jb ON jbj.jobBankID = jb.jobBankID 
					AND jbj.jobBankID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobBankID#">
					AND jbj.IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="0">
					AND jbj.jobID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobID#">
					<cfif NOT val(arguments.editALLRight)>
					AND jbj.memberID in (select mMerged.memberID 
										 from ams_members m 
											INNER JOIN dbo.ams_members mMerged on mMerged.activeMemberID = m.activeMemberID 
										 where m.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">)
					</cfif>
					<cfif arguments.maxDaysForPostingJobs NEQ '' OR arguments.maxDaysForPostingJobs NEQ 0>
						AND GETDATE() <=  jbj.removeOnDate
					</cfif>
					AND jbj.status <> 'X'
				INNER JOIN ams_states s ON
					s.code = jbj.state
					AND s.countryID in (1,2)					
		</cfquery>
		
		<cfif NOT local.data.recordCount>
			<cfset local.data = buildDefaultJobsQuery(local.data) />
		</cfif>
		
		<cfreturn local.data />
	</cffunction>

	<cffunction name="getJobSearch" access="private" returntype="query"  output="true">
		<cfargument name="jobBankID" type="numeric" required="true">
		<cfargument name="maxDaysForPostingJobs" type="numeric" required="true">
		<cfargument name="orderColumn" type="string" default="" required="false">
		<cfargument name="orderBy" type="string" default="" required="false">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structnew() />
		<cfset local.JBJ = application.mcCacheManager.sessionGetValue(keyname='JBJ', defaultValue={})>
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @exclusiveAccessCutoffDate datetime, @now datetime,  @exclusiveAccessDays int;

			set @now = getdate();
			set @exclusiveAccessDays = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.instanceSettings.exclusiveAccessDays#">;
			set @exclusiveAccessCutoffDate = DATEADD(ms, -3,DATEADD(day, DATEDIFF(day, 0, @now),-(@exclusiveAccessDays-1)));

			SELECT jbj.jobid, jbj.JobTitle, jbj.Employer, jbj.City, jbj.State, jbj.DatePosted,
				inExcusiveAccessPeriod = case when jbj.datePosted > @exclusiveAccessCutoffDate then cast(1 as bit) else cast(0 as bit) end
			FROM JobBankJobs jbj
			<cfif arguments.memberID neq 0>
				INNER JOIN dbo.ams_members m on m.memberID = jbj.memberID
				INNER JOIN dbo.ams_members mActive on mActive.memberID = m.activeMemberID
			</cfif>
			<cfif structKeyExists(local.JBJ,"PositionDescription") and len(trim(local.JBJ.PositionDescription))>
				CROSS APPLY dbo.fn_getContent(jbj.positionDescriptionContentID,1) positionDesc
			</cfif>
			WHERE 
				jbj.JobBankId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobBankID#">
				AND jbj.hasPaid = <cfqueryparam cfsqltype="cf_sql_bit" value="1">
				AND jbj.IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="0">
				<cfif arguments.maxDaysForPostingJobs NEQ '' OR arguments.maxDaysForPostingJobs NEQ 0>
					AND @now <= jbj.removeOnDate
				</cfif>
				AND jbj.status = 'A'
		 	<cfif local.JBJ.count()>
				<cfif structKeyExists(local.JBJ,"Days") and len(trim(local.JBJ.Days))>AND DATEDIFF(day, jbj.datePosted, GETDATE()) <= <cfqueryparam cfsqltype="cf_sql_integer" value="#local.JBJ.Days#"></cfif>
				<cfif structKeyExists(local.JBJ,"Employer") and len(trim(local.JBJ.Employer))> AND Lower(jbj.Employer) like <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.JBJ.Employer#%"> </cfif>
				<cfif structKeyExists(local.JBJ,"City") and len(trim(local.JBJ.City))> AND Lower(jbj.city) like <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.JBJ.City#%"> </cfif>
				<cfif structKeyExists(local.JBJ,"State") and len(trim(local.JBJ.State))> AND Lower(jbj.state) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.JBJ.State#"> </cfif>
				<cfif structKeyExists(local.JBJ,"employmentTypeID") and len(trim(local.JBJ.employmentTypeID))> AND jbj.employmentTypeId In (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.JBJ.employmentTypeID#" list="true">) </cfif>
				<cfif structKeyExists(local.JBJ,"JobTitle") and len(trim(local.JBJ.JobTitle))> AND Lower(jbj.JobTitle) like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#local.JBJ.JobTitle#%"> </cfif>
				<cfif structKeyExists(local.JBJ,"PositionDescription") and len(trim(local.JBJ.PositionDescription))> AND Lower(positionDesc.rawcontent) like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#local.JBJ.PositionDescription#%"> </cfif> 
				<cfif structKeyExists(local.JBJ,"CategoryID") and len(trim(local.JBJ.CategoryID))> AND jbj.CategoryId In (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.JBJ.CategoryID#" list="true">) </cfif> 
			</cfif>

			<cfswitch expression="#arguments.orderColumn#">
				<cfcase value="job">
					ORDER BY jbj.JobTitle #arguments.orderBy#
				</cfcase>
				<cfcase value="emp">
					ORDER BY jbj.Employer #arguments.orderBy#
				</cfcase>
				<cfcase value="city">
					ORDER BY jbj.City #arguments.orderBy#
				</cfcase>
				<cfcase value="st">
					ORDER BY jbj.State #arguments.orderBy#
				</cfcase>
				<cfcase value="date">
					ORDER BY jbj.datePosted #arguments.orderBy#
				</cfcase>
				<cfdefaultcase>
					ORDER BY jbj.datePosted DESC
				</cfdefaultcase>
			</cfswitch>

			set nocount off
		</cfquery>
		
		<cfreturn local.data />
	</cffunction>

	<cffunction name="getJobDetails" access="public" returntype="query">
		<cfargument name="jobID" type="numeric" required="yes">
		<cfargument name="jobBankID" type="numeric" required="true">
		<cfargument name="maxDaysForPostingJobs" type="numeric" required="true">
		
		<cfset var local = structNew() />
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @exclusiveAccessCutoffDate datetime, @now datetime,  @exclusiveAccessDays int;

			set @now = getdate();
			set @exclusiveAccessDays = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.instanceSettings.exclusiveAccessDays#">;
			set @exclusiveAccessCutoffDate = DATEADD(ms, -3,DATEADD(day, DATEDIFF(day, 0, @now),-(@exclusiveAccessDays-1)));

			SELECT *, 
				(select description from JobBankEmploymentTypes where employmentTypeId = j.employmentTypeId) as WorkStatus,
				(select description from JobBankWorkExperience where workExperienceId = j.workExperienceId) as WorkExperience,
				(select description from JobBankEducationLevel where educationLevelId = j.educationLevelId) as EducationLevel,
	   			(select description from JobBankCategories where categoryId = j.categoryId) as CategoryDescription,
   				inExcusiveAccessPeriod = case when datePosted > @exclusiveAccessCutoffDate then cast(1 as bit) else cast(0 as bit) end
			FROM JobBankJobs j
			WHERE j.jobID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobID#">
			AND j.IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="0">
			AND j.jobBankID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobBankID#">
			<cfif arguments.maxDaysForPostingJobs NEQ '' OR arguments.maxDaysForPostingJobs NEQ 0>
				AND @now <=  j.removeOnDate
			</cfif>
		</cfquery>		
		
		<cfreturn local.data />
	</cffunction>

	<cffunction name="addJob" access="public" output="false" returntype="numeric">
		<cfargument name="formData" type="struct" required="true">
		<cfargument name="jobBankID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="maxDaysForPostingJobs" type="numeric" required="true">
		<cfargument name="instanceSettings" type="struct" required="true">		
		
		<cfset var local = structnew()>
		<cfset local.price = 0>
		<cfset local.hasPaid = 1>
		
		<cfquery name="local.qryActiveMember" datasource="#application.dsn.membercentral.dsn#">
			select activeMemberID
			from dbo.ams_members
			where memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
		</cfquery>

		<cfset local.jbPostJobsRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="JobBank", functionName="jbPostJobs")>

		<cfquery name="local.qryPrice" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @FID int;
			select @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.jbPostJobsRFID#">;

			SELECT ISNULL(jbp.price, 0.00) AS price
			FROM dbo.jobBankFunctionPricing jbp
			INNER JOIN dbo.cms_siteResourceRights srr ON jbp.resourceRightsID = srr.resourceRightsID AND srr.functionID = @FID
			INNER JOIN dbo.cache_members_groups as vmg ON srr.groupID = vmg.groupID
				AND vmg.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryActiveMember.activeMemberID#">
			order by price asc;
		</cfquery>
		
		<cfif local.qryPrice.RecordCount GT 0 AND local.qryPrice.price GT 0>
			<cfset local.price = local.qryPrice.price>
			<cfset local.hasPaid = 0>
		</cfif>
		
		<cfset arguments.formData.jb_MinSalary = cleanNumber(arguments.formData.jb_MinSalary)>
		<cfset arguments.formData.jb_MaxSalary = cleanNumber(arguments.formData.jb_MaxSalary)>
		<cfif  structKeyExists(arguments.formData,"statusCopy")>
			<cfset local.statusToSave = arguments.formData.statusCopy>
		<cfelse>
			<cfset local.statusToSave = 'A'>
		</cfif>
	
		<cfquery name="local.insertNewJob" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;
			declare  
				@jobId int,
				@siteID int, 
				@positionDescriptionContentID int, 
				@requiredSkillsContentID int, 
				@requiredExperienceContentID int, 
				@howToApplyContentID int, 
				@contentSiteResourceID int,
				@appCreatedContentResourceTypeID int,
				@defaultLanguageID int,
				@count int,
				@rc int;
			
			SET @siteID = #arguments.instanceSettings.siteid#;
			select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');
			SELECT @defaultLanguageID = defaultLanguageID from dbo.sites where siteID = @siteID;
			
			
			insert into dbo.JobBankJobs(
				jobBankID, memberID, firstName, lastName, contactTitle, contactFirm, phone, fax, emailAddress, jobTitle, 
				employmentTypeID, employer, city, state, minSalary, maxSalary, salaryUnit, workExperienceID,
				educationLevelID, showEmployerInfo,  
				categoryID, hasPaid, purchasedPrice, dateOpen, removeOnDate, status)
			values	(
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobBankID#" />,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryActiveMember.activeMemberID#" />,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_firstName#" />,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_lastName#" />,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_contactTitle#" />,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(arguments.formData.jb_contactFirm,200)#" />,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_phone#" />,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_fax#" />,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_emailAddress#" />,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(arguments.formData.jb_jobTitle,200)#" />,
				<cfif val(arguments.formData.jb_employmentTypeID)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_employmentTypeID#" /><cfelse>NULL</cfif>,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(arguments.formData.jb_employer,200)#" />,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_city#" />,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_state#" />,
				<cfif arguments.formData.jb_minSalary GT 0><cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.formData.jb_minSalary#" /><cfelse>NULL</cfif>,
				<cfif arguments.formData.jb_maxSalary GT 0><cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.formData.jb_maxSalary#" /><cfelse>NULL</cfif>,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_salaryUnit#" />,
				<cfif val(arguments.formData.jb_workExperienceID)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_workExperienceID#" /><cfelse>NULL</cfif>,
				<cfif val(arguments.formData.jb_educationLevelID)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_educationLevelID#" /><cfelse>NULL</cfif>,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_showEmployerInfo#" />,
				<cfif val(arguments.formData.jb_categoryID)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_categoryID#" /><cfelse>NULL</cfif>,
				<cfqueryparam cfsqltype="cf_sql_bit" value="#local.hasPaid#" />,
				<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#cleanNumber(local.price)#" />,
				<cfqueryparam cfsqltype="cf_sql_date" value="#arguments.formData.jb_dateOpen#" />,
				<cfif val(arguments.maxDaysForPostingJobs)><cfqueryparam cfsqltype="cf_sql_date" value="#dateAdd('d',arguments.maxDaysForPostingJobs,now())#"><cfelse>NULL</cfif>,
				'#local.statusToSave#'
			);
			
			SELECT @jobID = @@IDENTITY;
			
			EXEC dbo.cms_createContentObject 
			@siteID=@siteID, 
			@resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1,
			@isHTML=1, 
			@languageID=@defaultLanguageID, 
			@isActive=1, 
			@contentTitle=null, 
			@contentDesc=null, 
			@rawContent='#arguments.formData.jb_PositionDescription#',
			@contentID=@positionDescriptionContentID OUTPUT, 
			@siteResourceID=@contentSiteResourceID OUTPUT;
			
			EXEC dbo.cms_createContentObject 
			@siteID=@siteID, 
			@resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1,
			@isHTML=1, 
			@languageID=@defaultLanguageID, 
			@isActive=1, 
			@contentTitle=null, 
			@contentDesc=null, 
			@rawContent='#arguments.formData.jb_requiredExperience#',
			@contentID=@requiredExperienceContentID OUTPUT, 
			@siteResourceID=@contentSiteResourceID OUTPUT;
			
			EXEC dbo.cms_createContentObject 
			@siteID=@siteID, 
			@resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1,
			@isHTML=1, 
			@languageID=@defaultLanguageID, 
			@isActive=1, 
			@contentTitle=null, 
			@contentDesc=null, 
			@rawContent='#arguments.formData.jb_requiredSkills#',
			@contentID=@requiredSkillsContentID OUTPUT, 
			@siteResourceID=@contentSiteResourceID OUTPUT;
			
			EXEC dbo.cms_createContentObject 
			@siteID=@siteID, 
			@resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1,
			@isHTML=1, 
			@languageID=@defaultLanguageID, 
			@isActive=1, 
			@contentTitle=null, 
			@contentDesc=null, 
			@rawContent='#arguments.formData.jb_howToApply#',
			@contentID=@howToApplyContentID OUTPUT, 
			@siteResourceID=@contentSiteResourceID OUTPUT;
			
			UPDATE jobBankJobs SET positionDescriptionContentID = @positionDescriptionContentID
			,requiredSkillsContentID=@requiredSkillsContentID
			,requiredExperienceContentID=@requiredExperienceContentID 
			,howToApplyContentID=@howToApplyContentID 
			WHERE jobId = @jobId;
			
			SELECT @jobId as jobId;
	
		</cfquery>
		<cfreturn local.insertNewJob.jobId>
	</cffunction>

	<cffunction name="updateJob" access="public" output="false" returntype="numeric">
		<cfargument name="formData" type="struct" required="true">
		<cfargument name="jobBankID" type="numeric" required="true">
		<cfargument name="instanceSettings" type="struct" required="true">
				
		<cfset var local = structnew() />
		<cfset arguments.formData.jb_MinSalary = cleanNumber(arguments.formData.jb_MinSalary) />
		<cfset arguments.formData.jb_MaxSalary = cleanNumber(arguments.formData.jb_MaxSalary) />
		<cfset local.instanceSettings = arguments.instanceSettings />
		
		<cfquery name="local.updateJob" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			DECLARE  
				@jobId int,
				@jobBankId int,
				@siteID int, 
				@positionDescriptionContentID int,
				@positionDescriptionContentIDNew int,
				@positionDescription varchar(max), 
				@requiredSkillsContentID int, 
				@requiredSkillsContentIDNew int, 
				@requiredSkills varchar(max), 
				@requiredExperienceContentID int, 
				@requiredExperienceContentIDNew int, 
				@requiredExperience varchar(max), 
				@howToApplyContentID int, 
				@howToApplyContentIDNew int, 
				@howToApply varchar(max), 
				@contentSiteResourceID int,
				@appCreatedContentResourceTypeID int,
				@defaultLanguageID int,
				@count int,
				@rc int;
			
			SET @siteID = #local.instanceSettings.siteid#;
			SET @jobId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_jobID#">;
			SET @jobBankId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobBankID#">;
			
			SELECT @positionDescriptionContentID = positionDescriptionContentID,
				@requiredSkillsContentID = requiredSkillsContentID,
				@requiredExperienceContentID = requiredExperienceContentID,
				@howToApplyContentID = howToApplyContentID FROM JobBankJobs 
			WHERE jobBankId = @jobBankId AND jobId = @jobId;
			
			SELECT @defaultLanguageID = defaultLanguageID from dbo.sites where siteID = @siteID;
			SELECT @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');
			
			SET @positionDescription = '#arguments.formData.jb_positionDescription#';
			SET @requiredSkills = '#arguments.formData.jb_requiredSkills#';
			SET @requiredExperience = '#arguments.formData.jb_requiredExperience#';
			SET @howToApply = '#arguments.formData.jb_howToApply#';
			
			UPDATE JobBankJobs set
				FirstName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_FirstName#">, 
				LastName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_LastName#">, 
				ContactFirm = <cfqueryparam cfsqltype="cf_sql_varchar" value="#left(arguments.formData.jb_ContactFirm,200)#">, 
				Phone = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_Phone#">,
				Fax = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_Fax#">,
				EmailAddress = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_EmailAddress#">,
				jobTitle = <cfqueryparam cfsqltype="cf_sql_varchar" value="#left(arguments.formData.jb_JobTitle,200)#">,
				contactTitle = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_ContactTitle#">,
				employer = <cfqueryparam cfsqltype="cf_sql_varchar" value="#left(arguments.formData.jb_Employer,200)#">,
				City = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_city#">,
				State = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_state#">,
				MinSalary = <cfif arguments.formData.jb_MinSalary GT 0><cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.formData.jb_MinSalary#"><cfelse>NULL</cfif>,
				MaxSalary = <cfif arguments.formData.jb_MaxSalary GT 0><cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.formData.jb_MaxSalary#"><cfelse>NULL</cfif>,
				SalaryUnit = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_SalaryUnit#">,
				ShowEmployerInfo = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.formData.jb_ShowEmployerInfo#">,
				WorkExperienceId = <cfif val(arguments.formData.jb_WorkExperienceId)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_WorkExperienceID#"><cfelse>NULL</cfif>,
				EducationLevelId = <cfif val(arguments.formData.jb_EducationLevelId)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_EducationLevelID#"><cfelse>NULL</cfif>,
				employmentTypeId = <cfif val(arguments.formData.jb_employmentTypeId)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_employmentTypeID#"><cfelse>NULL</cfif>,
				categoryId = <cfif arguments.formData.jb_CategoryId GT 0><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_CategoryId#"><cfelse>NULL</cfif>,
				<cfif arguments.formData.jb_Status neq 'D'>
				status = <cfqueryparam cfsqltype="cf_sql_char" value="#arguments.formData.jb_Status#">,
				isDeleted = 0,
				<cfelse>
					isDeleted = 1,
				</cfif>
				dateOpen = <cfif len(trim(arguments.formData.jb_dateOpen))><cfqueryparam cfsqltype="cf_sql_date" value="#createODBCDate(arguments.formData.jb_dateOpen)#"><cfelse>NULL</cfif>
			WHERE jobBankId = @jobBankId 
				AND jobId = @jobId;
			
			IF ISNULL(@positionDescriptionContentID,0) = 0 
				BEGIN
					EXEC dbo.cms_createContentObject 
						@siteID=@siteID, 
						@resourceTypeID=@appCreatedContentResourceTypeID, 
						@siteResourceStatusID=1,
						@isHTML=1, 
						@languageID=@defaultLanguageID, 
						@isActive=1, 
						@contentTitle=null, 
						@contentDesc=null, 
						@rawContent=@positionDescription,
						@contentID=@positionDescriptionContentIDNew OUTPUT, 
						@siteResourceID=@contentSiteResourceID OUTPUT;
						
					UPDATE JobBankJobs set positionDescriptionContentID = @positionDescriptionContentIDNew WHERE jobId = @jobId
				END
			ELSE
				BEGIN
					EXEC @rc = dbo.cms_updateContent @contentID=@positionDescriptionContentID, @languageID=1, @isHTML=1, @contentTitle='', @contentDesc='', 	@rawContent=@positionDescription;
				END
			
			IF (ISNULL(@requiredSkillsContentID,0)) = 0
				BEGIN
					EXEC dbo.cms_createContentObject 
						@siteID=@siteID, 
						@resourceTypeID=@appCreatedContentResourceTypeID, 
						@siteResourceStatusID=1,
						@isHTML=1, 
						@languageID=@defaultLanguageID, 
						@isActive=1, 
						@contentTitle=null, 
						@contentDesc=null, 
						@rawContent=@requiredSkills,
						@contentID=@requiredSkillsContentIDNew OUTPUT, 
						@siteResourceID=@contentSiteResourceID OUTPUT;
						
					UPDATE JobBankJobs set requiredSkillsContentID = @requiredSkillsContentIDNew WHERE jobId = @jobId
				END
			ELSE
				BEGIN
					EXEC @rc = dbo.cms_updateContent @contentID=@requiredSkillsContentID, @languageID=1, @isHTML=1, @contentTitle='', @contentDesc='', @rawContent=@requiredSkills;
				END
			IF (ISNULL(@requiredExperienceContentID,0)) = 0 
				BEGIN
					EXEC dbo.cms_createContentObject 
						@siteID=@siteID, 
						@resourceTypeID=@appCreatedContentResourceTypeID, 
						@siteResourceStatusID=1,
						@isHTML=1, 
						@languageID=@defaultLanguageID, 
						@isActive=1, 
						@contentTitle=null, 
						@contentDesc=null, 
						@rawContent=@requiredExperience,
						@contentID=@requiredExperienceContentIDNew OUTPUT, 
						@siteResourceID=@contentSiteResourceID OUTPUT;
						
					UPDATE JobBankJobs set requiredExperienceContentID = @requiredExperienceContentIDNew WHERE jobId = @jobId
				END
			ELSE
				BEGIN
					EXEC @rc = dbo.cms_updateContent @contentID=@requiredExperienceContentID, @languageID=1, @isHTML=1, @contentTitle='', @contentDesc='', @rawContent=@requiredExperience;
				END
				
			IF (ISNULL(@howToApplyContentID,0)) = 0
				BEGIN
					EXEC dbo.cms_createContentObject 
						@siteID=@siteID, 
						@resourceTypeID=@appCreatedContentResourceTypeID, 
						@siteResourceStatusID=1,
						@isHTML=1, 
						@languageID=@defaultLanguageID, 
						@isActive=1, 
						@contentTitle=null, 
						@contentDesc=null, 
						@rawContent=@howToApply,
						@contentID=@howToApplyContentIDNew OUTPUT, 
						@siteResourceID=@contentSiteResourceID OUTPUT;
						
					UPDATE JobBankJobs set howToApplyContentID = @howToApplyContentIDNew WHERE jobId = @jobId
				END
			ELSE
				BEGIN
					EXEC @rc = dbo.cms_updateContent @contentID=@howToApplyContentID, @languageID=1, @isHTML=1, @contentTitle='', @contentDesc='', @rawContent=@howToApply;
				END
		</cfquery>
		
		<cfreturn arguments.formData.jb_jobID />
	</cffunction>

	<cffunction name="deleteJobPosting" access="public" returntype="void" output="false">
		<cfargument name="jobId" type="numeric" required="yes">
		<cfargument name="jobBankID" type="numeric" required="true">
		
		<cfset var local = structnew()>

		<cfquery name="local.qry" datasource="#application.dsn.membercentral.dsn#">
			UPDATE JobBankJobs
			SET IsDeleted = <cfqueryparam cfsqltype="cf_sql_integer" value="1">
			WHERE jobId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobId#">
				AND jobBankId = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobBankId#">
		</cfquery>	
	</cffunction>

	<cffunction name="buildDefaultJobsQuery" access="private" returntype="query">
		<cfargument name="jobData" type="query" required="yes">
		<cfset var local = structNew() />
		
		<cfset local.memberAddress 		= getMemberAddress() />
		
		<cfset local.data = QueryNew( "" ) />
		<cfset local.default = arrayNew(1) />
		<cfset local.default[1] = '' />
		<cfloop index="local.strKey" list="#arguments.jobData.columnList#" delimiters=",">
			<!--- Add column to new query with default values. --->
			<cfset QueryAddColumn( local.data, local.strKey, local.default) />
		</cfloop>
		<cfset querySetCell(local.data,'jobID',0) />
		<cfset querySetCell(local.data,'firstname',session.cfcUser.memberData.firstName) />
		<cfset querySetCell(local.data,'lastName',session.cfcUser.memberData.lastName) />
		<cfset querySetCell(local.data,'contactFirm',session.cfcUser.memberData.company) />
		<cfset querySetCell(local.data,'employer',session.cfcUser.memberData.company) />
		<cfset querySetCell(local.data,'emailAddress',session.cfcUser.memberData.email) />
		<cfset querySetCell(local.data,'dateOpen',DateFormat(Now(),"mm/dd/yyyy")) />
		<cfset querySetCell(local.data,'showEmployerInfo',1) />
		<cfset querySetCell(local.data,'City',local.memberAddress.city) />
		<cfset querySetCell(local.data,'State',local.memberAddress.stateCode) />		
		
		<cfreturn local.data />
	</cffunction>

	<cffunction name="buildJobFormData" access="private" returntype="struct">
		<cfargument name="jobId" type="numeric" required="yes">
		<cfargument name="jobBankID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="maxDaysForPostingJobs" type="numeric" required="true">
		<cfargument name="editALLRight" type="numeric" required="true">
		
		<cfset var local = structNew() />
		
		<cfset local.qryThisJob = getJobToEdit(arguments.jobID,arguments.jobBankID,arguments.memberID,arguments.maxDaysForPostingJobs,arguments.editALLRight) />
		
		<cfset local.data 													= structNew() />
		<cfset local.data.jb_JobID		 							= local.qryThisJob.JobID />
		<cfset local.data.jb_Firstname 							= local.qryThisJob.FirstName />
		<cfset local.data.jb_Lastname 							= local.qryThisJob.LastName />
		<cfset local.data.jb_ContactFirm 						= local.qryThisJob.ContactFirm />
		<cfset local.data.jb_Employer 							= local.qryThisJob.Employer />
		<cfset local.data.jb_EmailAddress 					= local.qryThisJob.EmailAddress />
		<cfset local.data.jb_Phone 									= local.qryThisJob.Phone />
		<cfset local.data.jb_Fax 										= local.qryThisJob.Fax />
		<cfset local.data.jb_JobTitle 							= local.qryThisJob.JobTitle />
		<cfset local.data.jb_City 									= local.qryThisJob.City />
		<cfset local.data.jb_State 									= local.qryThisJob.State />
		<cfset local.data.jb_employmentTypeID				= local.qryThisJob.EmploymentTypeId />
		
		<cfif len(local.qryThisJob.positionDescriptionContentID)>
			<cfset local.data.jb_PositionDescription 		= variables.objJB.showContentEditor("jb_PositionDescription", local.qryThisJob.positionDescriptionContentID) />
			<cfset local.data.jb_positionDescriptionContentID = local.qryThisJob.positionDescriptionContentID>
		<cfelse>
			<cfset local.data.jb_PositionDescription 		= variables.objJB.showContentEditor("jb_PositionDescription", 0) />
			<cfset local.data.jb_positionDescriptionContentID = 0>
		</cfif>
		<cfset local.data.jb_MinSalary 							= local.qryThisJob.MinSalary />
		<cfset local.data.jb_MaxSalary 							= local.qryThisJob.MaxSalary />
		<cfset local.data.jb_SalaryUnit 						= local.qryThisJob.SalaryUnit />
		<cfset local.data.jb_WorkExperienceId 			= local.qryThisJob.WorkExperienceId />
		<cfset local.data.jb_EducationLevelId 			= local.qryThisJob.EducationLevelId />
		<cfset local.data.jb_ContactTitle 					= local.qryThisJob.ContactTitle />
		<cfset local.data.jb_ShowEmployerInfo 			= local.qryThisJob.ShowEmployerInfo />
		
		<cfif len(local.qryThisJob.requiredSkillsContentID)>
			<cfset local.data.jb_RequiredSkills 				= variables.objJB.showContentEditor("jb_RequiredSkills", local.qryThisJob.requiredSkillsContentID)>
			<cfset local.data.jb_requiredSkillsContentID = local.qryThisJob.requiredSkillsContentID>
		<cfelse>
			<cfset local.data.jb_RequiredSkills 				= variables.objJB.showContentEditor("jb_RequiredSkills", 0)>
			<cfset local.data.jb_requiredSkillsContentID = 0>
		</cfif>
		
		<cfif len(local.qryThisJob.requiredExperienceContentID)>
			<cfset local.data.jb_RequiredExperience 		= variables.objJB.showContentEditor("jb_RequiredExperience", local.qryThisJob.requiredExperienceContentID) />
			<cfset local.data.jb_RequiredExperienceContentID = local.qryThisJob.requiredExperienceContentID>
		<cfelse>
			<cfset local.data.jb_RequiredExperience 		= variables.objJB.showContentEditor("jb_RequiredExperience", 0) />
			<cfset local.data.jb_RequiredExperienceContentID = 0>
		</cfif>
		<cfset local.data.jb_CategoryId 						= local.qryThisJob.categoryId />
		<cfset local.data.jb_DateOpen 							= DateFormat(local.qryThisJob.DateOpen,'mm/dd/yyyy') />
		
		<cfif len(local.qryThisJob.howToApplyContentID)>
			<cfset local.data.jb_howToApply 						= variables.objJB.showContentEditor("jb_howToApply", local.qryThisJob.howToApplyContentID) />
			<cfset local.data.jb_howToApplyContentID = local.qryThisJob.howToApplyContentID>
		<cfelse>
			<cfset local.data.jb_howToApply 						= variables.objJB.showContentEditor("jb_howToApply", 0) />
			<cfset local.data.jb_howToApplyContentID = 0>
		</cfif>
		<cfset local.data.jb_Status 						= local.qryThisJob.status>
		
		<cfreturn local.data />
	</cffunction>
	<!--- RESUMES:------------------------------------------------------------------ --->
	<cffunction name="doYouOwnThisResume" access="private" returntype="boolean">
		<cfargument name="resumeID" type="numeric" required="true">
		<cfargument name="jobBankId" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		
		<cfset var local = structNew() />
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SELECT jbr.resumeID, jbr.*
			FROM dbo.JobBankResumes jbr
				inner join dbo.ams_members m on m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				inner join dbo.ams_members mMerged on mMerged.activeMemberID = m.activeMemberID
			WHERE jbr.resumeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.resumeID#">
				AND jbr.memberID = mMerged.memberID
				AND jbr.IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="0">
				AND jbr.jobBankId = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobBankId#">
		</cfquery>	
		
		<cfif local.data.recordCount>
			<cfreturn True />
		<cfelse>
			<cfreturn False />
		</cfif>
		
	</cffunction>

	<cffunction name="getResumeStates" access="public" returntype="query">
		<cfargument name="jobBankId" type="numeric" required="yes">
		<cfargument name="maxDaysForPostingResumes" type="numeric" required="true">
		
		<cfset var local = structNew() />
		
		<cfquery name="local.states" datasource="#application.dsn.membercentral.dsn#">
			select distinct state 
			from JobBankResumes
			where jobBankId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobBankID#">
				AND IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="0">
				<cfif arguments.maxDaysForPostingResumes NEQ '' OR arguments.maxDaysForPostingResumes NEQ 0>
					AND GETDATE() <=  removeOnDate
				</cfif>
			order by state
		</cfquery>	
		
		<cfreturn local.states />
	</cffunction>

	<cffunction name="getJobBankResumes" access="public" returntype="query">
		<cfargument name="jobBankId" type="numeric" required="yes">
		<cfargument name="maxDaysForPostingResumes" type="numeric" required="true">
		
		<cfset var local = structNew() />

		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SELECT jbr.resumeId, jbr.ResumeTitle, jbr.FirstName, jbr.LastName, jbr.dateAvailable,
				cat.Description AS JobCategoryDesc, 
				et.Description AS EmploymentTypeDesc
			FROM dbo.JobBankResumes jbr
				INNER JOIN dbo.JobBank jb ON jbr.jobBankId = jb.jobBankId 
				LEFT OUTER JOIN dbo.JobBankCategories cat ON jbr.categoryId = cat.CategoryID 
				LEFT OUTER JOIN dbo.JobBankEmploymentTypes et ON jbr.employmentTypeId = et.employmentTypeId
			WHERE jbr.jobBankId = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobBankId#"> 
				AND jbr.IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="0">
				<cfif arguments.maxDaysForPostingResumes NEQ '' OR arguments.maxDaysForPostingResumes NEQ 0>
					AND GETDATE() <=  jbr.removeOnDate
				</cfif>
		</cfquery>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMembersResumes" access="public" returntype="query">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="jobBankId" type="numeric" required="true">
		<cfargument name="maxDaysForPostingResumes" type="numeric" required="true">
		
		<cfset var local = structNew() />

		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SELECT jbr.resumeId, jbr.ResumeTitle, jbr.FirstName, jbr.LastName, jbr.dateAvailable,
				cat.Description AS JobCategoryDesc,
				et.Description AS EmploymentTypeDesc
			FROM dbo.JobBankResumes jbr
				INNER JOIN dbo.JobBank jb ON jbr.jobBankId = jb.jobBankId 
				inner join dbo.ams_members m on m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				inner join dbo.ams_members mMerged on mMerged.activeMemberID = m.activeMemberID
				LEFT OUTER JOIN dbo.JobBankCategories cat ON jbr.categoryId = cat.CategoryID 
				LEFT OUTER JOIN dbo.JobBankEmploymentTypes et ON jbr.employmentTypeId = et.employmentTypeId
			WHERE jbr.jobBankId = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobBankId#"> 
				AND jbr.memberID = mMerged.memberID 
				AND jbr.IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="0">
				<cfif arguments.maxDaysForPostingResumes NEQ '' OR arguments.maxDaysForPostingResumes NEQ 0>
					AND GETDATE() <=  jbr.removeOnDate
				</cfif>
		</cfquery>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getResumeToEdit" access="public" returntype="query">
		<cfargument name="resumeId" type="numeric" required="yes">
		<cfargument name="jobBankId" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="maxDaysForPostingResumes" type="numeric" required="true">
		<cfargument name="EditAllRight" type="numeric" required="true">
		
		<cfset var local = structNew() />
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SELECT jbr.resumeID, jbr.jobBankID, jbr.memberID, jbr.resumeTitle, jbr.firstName, jbr.lastName, jbr.address1, jbr.address2, jbr.city, 
				jbr.state, jbr.zipCode, jbr.phone, jbr.fax, jbr.emailAddress, jbr.objective, jbr.employmentTypeID, jbr.willRelocated, jbr.documentID, 
				jbr.resumeInfo, jbr.dateAvailable, jbr.howToContact, jbr.showContactInfo, jbr.categoryID, jbr.educationLevelID, jbr.datePosted, 
				jbr.removeOnDate, jbr.isDeleted
			FROM dbo.JobBankResumes jbr
				INNER JOIN dbo.JobBank jb on jbr.jobBankID = jb.jobBankID
					AND jbr.jobBankID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobBankID#">
					AND jbr.IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="0">
					AND jbr.resumeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.resumeID#">
					<cfif NOT val(arguments.EditAllRight)>
						AND jbr.memberID in (select mMerged.memberID 
											 from ams_members m 
												INNER JOIN dbo.ams_members mMerged on mMerged.activeMemberID = m.activeMemberID 
											 where m.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">)
					</cfif>
					<cfif arguments.maxDaysForPostingResumes NEQ '' OR arguments.maxDaysForPostingResumes NEQ 0>
						AND GETDATE() <=  jbr.removeOnDate
					</cfif>
		</cfquery>
		
		<cfif NOT local.data.recordCount>
			<cfset local.data = buildDefaultResumesQuery(local.data) />
		</cfif>
		
		<cfreturn local.data />
	</cffunction>

	<cffunction name="getResumeSearch" access="public" returntype="query">
		<cfargument name="jobBankID" type="numeric" required="true">
		<cfargument name="maxDaysForPostingResumes" type="numeric" required="true">
		<cfargument name="orderColumn" type="string" default="" required="false">
		<cfargument name="orderBy" type="string" default="" required="false">

		<cfset var local = structnew() />
		<cfset local.JBR = application.mcCacheManager.sessionGetValue(keyname='JBR', defaultValue={})>
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SELECT jbr.*, jbc.Description as CategoryDesc
			FROM dbo.JobBankResumes jbr 
			LEFT OUTER JOIN JobBankCategories jbc ON jbr.categoryId = jbc.CategoryID
			WHERE jbr.JobBankId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobBankID#">
			AND jbr.IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="0">
			<cfif arguments.maxDaysForPostingResumes NEQ '' OR arguments.maxDaysForPostingResumes NEQ 0>
				AND GETDATE() <=  jbr.removeOnDate
			</cfif>
			<cfif local.JBR.count()>
				<cfif structKeyExists(local.JBR,"Days") and len(trim(local.JBR.Days))>AND DATEDIFF(day, jbr.datePosted, GETDATE()) <= <cfqueryparam cfsqltype="cf_sql_integer" value="#local.JBR.Days#"></cfif>
				<cfif structKeyExists(local.JBR,"Objective") and local.JBR.Objective NEQ ""> AND Lower(jbr.Objective) like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#LCase(local.JBR.Objective)#%"> </cfif>
				<cfif structKeyExists(local.JBR,"City") and local.JBR.City NEQ ""> AND Lower(jbr.city) like <cfqueryparam cfsqltype="cf_sql_varchar" value="#LCase(local.JBR.City)#%"> </cfif>
				<cfif structKeyExists(local.JBR,"State") and local.JBR.State NEQ ""> AND Lower(jbr.state) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#LCase(local.JBR.State)#"> </cfif>
				<cfif structKeyExists(local.JBR,"employmentTypeId") and local.JBR.employmentTypeId NEQ ""> AND jbr.employmentTypeId In (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.JBR.employmentTypeId#" list="true">) </cfif>
				<cfif structKeyExists(local.JBR,"CategoryId") and local.JBR.CategoryId NEQ ""> AND jbr.CategoryId In (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.JBR.CategoryId#" list="true">) </cfif> 
			</cfif>
			
			<cfif len(arguments.orderColumn)>
				<cfswitch expression="#arguments.orderColumn#">
					<cfcase value="res">
						ORDER BY jbr.resumeTitle #arguments.orderBy#
					</cfcase>
					<cfcase value="loc">
						ORDER BY jbr.city #arguments.orderBy#, jbr.State #arguments.orderBy#
					</cfcase>
					<cfcase value="dpos">
						ORDER BY jbr.datePosted #arguments.orderBy#
					</cfcase>
					<cfcase value="dava">
						ORDER BY jbr.dateAvailable #arguments.orderBy#
					</cfcase>
					<cfcase value="cat">
						ORDER BY jbc.Description #arguments.orderBy#
					</cfcase>
					<cfdefaultcase>
						ORDER BY jbr.datePosted DESC, jbr.LastName, jbr.FirstName
					</cfdefaultcase>
				</cfswitch>
			</cfif>
			
		</cfquery>	

		<cfreturn local.data />
	</cffunction>

	<cffunction name="getResumeDetails" access="public" returntype="query">
		<cfargument name="resumeID" type="numeric" required="yes">
		<cfargument name="jobBankID" type="numeric" required="true">
		<cfargument name="maxDaysForPostingResumes" type="numeric" required="true">
		
		<cfset var local = structNew() />
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SELECT *, 
						(select description from JobBankEmploymentTypes where employmentTypeId = jbr.employmentTypeId) as WorkStatus,
					  (select description from JobBankEducationLevel where educationLevelId = jbr.educationLevelId) as EducationLevel,
					  (select description from JobBankCategories where categoryId = jbr.categoryId) as CategoryDescription
			FROM JobBankResumes jbr
			WHERE jbr.resumeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.resumeID#">
				AND jbr.IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="0">
				AND jbr.jobBankID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobBankID#">
				<cfif arguments.maxDaysForPostingResumes NEQ '' OR arguments.maxDaysForPostingResumes NEQ 0>
					AND GETDATE() <=  jbr.removeOnDate
				</cfif>
		</cfquery>	
		<cfreturn local.data />
	</cffunction>

	<cffunction name="addResume" access="public" output="false" returntype="numeric">
		<cfargument name="formData" type="struct" required="true">
		<cfargument name="jobBankID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="maxDaysForPostingResumes" type="numeric" required="true">
		
		<cfset var local = structnew()>
		
		<cfquery name="local.qryActiveMember" datasource="#application.dsn.membercentral.dsn#">
			select activeMemberID
			from dbo.ams_members
			where memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
		</cfquery>

		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON
			INSERT INTO dbo.JobBankResumes(
				jobBankID, memberID, resumeTitle, firstName, lastName, address1, address2, city, state, zipCode, phone, fax, 
				emailAddress, objective, employmentTypeID, willRelocated, resumeInfo, dateAvailable, howToContact, showContactInfo, 
				categoryID, educationLevelID, documentID, removeOnDate 
			)
			values	(
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobBankID#">,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryActiveMember.activeMemberID#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_resumeTitle#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_firstName#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_lastName#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_address1#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_address2#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_city#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_state#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_zipCode#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_phone#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_fax#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_emailAddress#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_objective#">,
				<cfif val(arguments.formData.jb_employmentTypeID)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_employmentTypeID#"><cfelse>NULL</cfif>,
				<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.formData.jb_willRelocated#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_resumeInfo#">,
				<cfif arguments.formData.jb_dateAvailable NEQ ''><cfqueryparam cfsqltype="cf_sql_date" value="#createODBCDate(arguments.formData.jb_dateAvailable)#"><cfelse>NULL</cfif>,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_howToContact#">,
				<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.formData.jb_showContactInfo#">,
				<cfif val(arguments.formData.jb_categoryID)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_categoryID#"><cfelse>NULL</cfif>,
				<cfif val(arguments.formData.jb_educationLevelID)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_educationLevelID#"><cfelse>NULL</cfif>,
				<cfif val(arguments.formData.jb_documentID)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_documentID#"><cfelse>NULL</cfif>,
				<cfif val(arguments.maxDaysForPostingResumes)><cfqueryparam cfsqltype="cf_sql_date" value="#createODBCDate(dateAdd('d',arguments.maxDaysForPostingResumes,now()))#"><cfelse>NULL</cfif>
				
			)
			
			SELECT @@IDENTITY AS resumeID
		</cfquery>
		<cfreturn local.data.resumeID>
	</cffunction>

	<cffunction name="updateResume" access="public" output="false" returntype="numeric">
		<cfargument name="formData" type="struct" required="true">
		<cfargument name="jobBankID" type="numeric" required="true">
		
		<cfset var local = structnew()>
		
		<cfquery name="local.updateResume" datasource="#application.dsn.membercentral.dsn#">
			update JobBankResumes 
			set 
				resumeTitle			= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_resumeTitle#">,
				firstName			= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_firstName#">,
				lastName			= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_lastName#">,
				address1			= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_address1#">,
				address2			= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_address2#">,
				city				= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_city#">,
				state				= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_state#">,
				zipCode				= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_zipCode#">,
				phone				= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_phone#">,
				fax					= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_fax#">,
				emailAddress		= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_emailAddress#">,
				objective			= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_objective#">,
				employmentTypeID	= <cfif val(arguments.formData.jb_employmentTypeID)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_employmentTypeID#"><cfelse>NULL</cfif>,
				willRelocated		= <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.formData.jb_willRelocated#">,
				resumeInfo			= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_resumeInfo#">,
				dateAvailable		= <cfif arguments.formData.jb_dateAvailable NEQ ''><cfqueryparam cfsqltype="cf_sql_date" value="#createODBCDate(arguments.formData.jb_dateAvailable)#"><cfelse>NULL</cfif>,
				howToContact		= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.formData.jb_howToContact#">,
				showContactInfo		= <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.formData.jb_showContactInfo#">,
				categoryID			= <cfif val(arguments.formData.jb_categoryID)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_categoryID#"><cfelse>NULL</cfif>,
				educationLevelID	= <cfif val(arguments.formData.jb_educationLevelID)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_educationLevelID#"><cfelse>NULL</cfif>,
				documentID			= <cfif val(arguments.formData.jb_documentID)><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_documentID#"><cfelse>NULL</cfif>
			WHERE 
				jobBankId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobBankID#">
				AND resumeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formData.jb_resumeID#">
		</cfquery>

		<cfreturn arguments.formData.jb_resumeID />
	</cffunction>

	<cffunction name="deleteResume" access="public" returntype="void" output="false">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="resumeID" type="numeric" required="true">
		<cfargument name="jobBankID" type="numeric" required="true">

		<cfset var local = structnew()>

		<cfquery name="local.qryDocument" datasource="#application.dsn.membercentral.dsn#">
			SELECT r.documentID, d.siteID
			FROM dbo.JobBankResumes AS r
			INNER JOIN dbo.cms_documents AS d ON d.documentID = r.documentID
			WHERE r.resumeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.resumeID#">
			AND r.jobBankId = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobBankID#">
			AND d.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
		</cfquery>

		<cfif local.qryDocument.recordCount>
			<cfset deleteDocument(siteID=local.qryDocument.siteID, documentID=local.qryDocument.documentID)>
		</cfif>

		<cfquery name="local.qryDelete" datasource="#application.dsn.membercentral.dsn#">
			UPDATE dbo.JobBankResumes
			SET IsDeleted = <cfqueryparam cfsqltype="cf_sql_bit" value="1">
			WHERE resumeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.resumeID#">
			AND jobBankId = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobBankID#">
		</cfquery>
	</cffunction>

	<cffunction name="removeDocFromResume" access="public" output="false" returntype="void" hint="Removes documentID from table">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="documentID" type="numeric" required="true">
		<cfargument name="jobBankID" type="numeric" required="true">

		<cfset var local = structNew()/>

		<cfset deleteDocument(siteID=arguments.siteID, documentID=arguments.documentID)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteDocument">
			UPDATE dbo.JobBankResumes
			SET documentID = <cfqueryparam cfsqltype="cf_sql_integer" null="true">
			WHERE documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">
			AND jobBankId = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobBankID#">
		</cfquery>
	</cffunction>

	<cffunction name="buildDefaultResumesQuery" access="private" returntype="query">
		<cfargument name="resData" type="query" required="yes">
		
		<cfset var local = structNew() />
		
		<cfset local.memberAddress = getMemberAddress() />
		
		<cfset local.data = QueryNew( "" ) />
		<cfset local.default = arrayNew(1) />
		<cfset local.default[1] = '' />
		<cfloop index="local.strKey" list="#arguments.resData.columnList#" delimiters=",">
			<!--- Add column to new query with default values. --->
			<cfset QueryAddColumn( local.data, local.strKey, local.default) />
		</cfloop>
		<cfset querySetCell(local.data,'ResumeID',0) />
		<cfset querySetCell(local.data,'firstname',session.cfcUser.memberData.firstName) />
		<cfset querySetCell(local.data,'lastName',session.cfcUser.memberData.lastName) />
		<cfset querySetCell(local.data,'emailAddress',session.cfcUser.memberData.email) />
		
		<cfset querySetCell(local.data,'Address1',local.memberAddress.address1) />
		<cfset querySetCell(local.data,'Address2',local.memberAddress.address2) />
		<cfset querySetCell(local.data,'City',local.memberAddress.city) />
		<cfset querySetCell(local.data,'State',local.memberAddress.stateCode) />
		<cfset querySetCell(local.data,'ZipCode',local.memberAddress.postalCode) />
		<cfset querySetCell(local.data,'DateAvailable',DateFormat(Now(),"mm/dd/yyyy")) />
		<cfset querySetCell(local.data,'DocumentID', 0) />
		<cfset querySetCell(local.data,'showContactInfo', 0) />
		<cfset querySetCell(local.data,'willRelocated', 0) />
		
		<cfreturn local.data />
	</cffunction>

	<cffunction name="buildResumeFormData" access="private" returntype="struct">
		<cfargument name="resumeId" type="numeric" required="yes">
		<cfargument name="jobBankId" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="maxDaysForPostingResumes" type="numeric" required="true">
		<cfargument name="EditAllRight" type="numeric" required="true">
		
		<cfset var local = structNew() />
		
		<cfset local.qryThisResume = getResumeToEdit(arguments.resumeID,arguments.jobBankId,arguments.memberID,arguments.maxDaysForPostingResumes,arguments.EditAllRight) />
		
		<cfset local.data 						= structNew() />
		<cfset local.data.jb_ResumeID 			= local.qryThisResume.ResumeID />
		<cfset local.data.jb_ResumeTitle 		= local.qryThisResume.ResumeTitle />
		<cfset local.data.jb_Firstname 			= local.qryThisResume.FirstName />
		<cfset local.data.jb_Lastname 			= local.qryThisResume.LastName />
		<cfset local.data.jb_Address1 			= local.qryThisResume.Address1 />
		<cfset local.data.jb_Address2 			= local.qryThisResume.Address2 />
		<cfset local.data.jb_City 				= local.qryThisResume.City />
		<cfset local.data.jb_State 				= local.qryThisResume.State />
		<cfset local.data.jb_ZipCode 			= local.qryThisResume.ZipCode />
		<cfset local.data.jb_Phone 				= local.qryThisResume.Phone />
		<cfset local.data.jb_Fax 				= local.qryThisResume.Fax />
		<cfset local.data.jb_EmailAddress 		= local.qryThisResume.EmailAddress />
		<cfset local.data.jb_Objective 			= local.qryThisResume.Objective />
		<cfset local.data.jb_EmploymentTypeId 	= local.qryThisResume.EmploymentTypeId />
		<cfset local.data.jb_WillRelocated 		= local.qryThisResume.WillRelocated />
		<cfset local.data.jb_DocumentID 		= local.qryThisResume.DocumentID />
		<cfset local.data.jb_ResumeInfo 		= local.qryThisResume.ResumeInfo />
		<cfset local.data.jb_DateAvailable 		= local.qryThisResume.DateAvailable />
		<cfset local.data.jb_HowToContact 		= local.qryThisResume.HowToContact />
		<cfset local.data.jb_ShowContactInfo 	= local.qryThisResume.ShowContactInfo />
		<cfset local.data.jb_CategoryId 		= local.qryThisResume.CategoryId />
		<cfset local.data.jb_EducationLevelId 	= local.qryThisResume.EducationLevelId />
		
		<cfreturn local.data />
	</cffunction>
	
	<!--- JOB BANK GLOBAL:--------------------------------------------------------- --->
	<cffunction name="buildCSS" access="private" returntype="string" output="no">
		
		<cfset var local = structNew() />
		<cfsavecontent variable="local.data">
			<cfoutput>
				<style type="text/css">
					.navButton{ height:20px; font-size:.65em; font-family:Trebuchet MS, Arial, Helvetica; }
					.r { text-align:right;}
					.l { text-align:left; }
					.t { vertical-align:top;}
					.padRight { padding-right:10px; }
					.padLeft { padding-left:10px; }
					
					a.navhop{font-size: 5px; color:##FFFFFF; text-decoration: none;}
					.extra-smallBox{width: 50px;}
					.smallBox{width: 100px;}
					.mediumBox{width: 170px;}
					.largeBox{width: 250px;}
					.extra-largeBox{width: 479px;}
					.extra-largeBoxJob{width: 445px;}
					
					.JobBankHeading { font-family:Verdana, Arial, Helvetica, sans-serif; color:##0E568D; font-weight:bold; font-size:13px; }
					.JobBankSectionHeader{font-weight:bold; vertical-align:middle; background-color:##DEDEDE; font-size:12px; line-height:1.5em; width: 100%; text-align:center;}	
					
					##messageBox{ padding-top:5px; }
					##message  { padding:5px 10px 5px 10px; border:1px solid ##999; background-color:##f4f6d9; }
					##subNavBox { line-height:20px;  margin:0; text-align:right; }
					##subNav { padding-right:10px;}
					.JobBankRecords { padding-right:25px; font-size: 10px; }
					
					
					##jbSection{ border:1px solid ##DEDEDE; }
					##jbSectionBody{ padding:5px 10px 5px 10px; }
					
					##jbdPage{ width:85%;padding: 2px 2px 2px 2px; margin:auto auto; }
					##jbdSection{ float:left; width:100%; border:1px solid ##DEDEDE; }
					##jbdSectionHead{ padding:5px 0 0 10px; font-weight:bold; font-size:13px; background-color:##CCC; height:25px; font-weight:bold; }
					##jbdSectionBody{ padding:5px 10px 5px 10px; }
					
					.jbH{ background-color:##CCC; height:25px; font-weight:bold; text-align:left; text-transform:uppercase;}
					.jbHBT{ border-top:1px solid ##333; }
					.jbHBB{ border-bottom:1px solid ##333; }
					.jbRBB{ border-bottom:1px solid ##AAA; }
					.jbRBT{ border-top:1px solid ##AAA; }
					.jbR { height:20px; }
					.row1{background-color:##fff;}
					.row2{background-color:##DEDEDE;}
					.pending{ background-color:##f5d0d0; color:##cd7474; }
					.expired{ background-color:##aaaaaa; color:##999999; }
					.filterShadow{filter: progid:DXImageTransform.Microsoft.Shadow(strength=1,color=gray,direction=135);}
					.expanded{ background-color: ##FFFFFF; overflow: auto; border : 1px solid ##7B9EBD; padding : 1px 1px; vertical-align : top;}
					.customHdr { width:48%; float:left; }
					
					##tabs { border-bottom:1px solid ##ccc; margin:0; padding-left:10px; overflow:hidden}
					##tabs ul, ##tabs li { display:inline; list-style-type:none; margin:0; padding:0; }	
					##tabs a:link, ##tabs a:visited { background:##E8EBF0; border:1px solid ##ccc; color:##666; float:left; margin-right:8px; padding:2px 10px; text-decoration:none; }	
					##tabs a:link.active, ##tabs a:visited.active { border-bottom:1px solid ##fff; color:##000; }
				
					##tabs li{ background-color:##0099FF} 
					##tabs a:hover { color:##f00; } 
					
					##tabset_searchjobs ##tabs li##nav_searchjobs a, 
					##tabset_searchresumes ##tabs li##nav_searchresumes a, 
					##tabset_myresume ##tabs li##nav_myresume a, 
					##tabset_myjobs ##tabs li##nav_myjobs a, 
					##tabset_manage ##tabs li##nav_manage a{ background:##fff; border-bottom:1px solid ##fff; color:##000; }
					
					##tabs ul a:hover { color:##f00 !important; }
					##listing { border:1px solid ##ccc; border-top:none; clear:both; margin:0px; padding:10px 0; }
				</style>
			</cfoutput>
		</cfsavecontent>
		<cfreturn application.objCommon.minText(local.data) />
	</cffunction>

	<cffunction name="buildJS" access="public" returntype="string">
		<cfset var local = structNew() />
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript" language="javascript">
					var infoBoxRelocationURL = '';
					function infoBox(cURL) {
						infoBoxRelocationURL = '';
						$.colorbox( {
							innerWidth:700, innerHeight:450, href:cURL, iframe:true, overlayClose:false,
							onClosed:function(){ 
								if(infoBoxRelocationURL != '')
									window.location.href = infoBoxRelocationURL;
								else window.location.reload(); 
							}
						} );
					}
					function resizeBox(newW,newH) { $.colorbox.resize({innerWidth:newW,innerHeight:newH});}
					function closeBox() { $.colorbox.close(); }
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn application.objCommon.minText(local.data) />
	</cffunction>

	<cffunction name="buildHeader" access="private" returntype="string" output="no">
		<cfargument name="Event" type="any">
		<cfargument name="applicationInstanceName" type="string" required="true">
		
		<cfset var local = structNew() />
		<cfswitch expression="#arguments.event.getValue('Action','SearchJobs')#">
			<cfcase value="ManageJob,ManageResume">
				<cfset local.currentTab='manage'>
			</cfcase>
			<cfdefaultcase>
				<cfset local.currentTab= lcase(arguments.event.getValue('Action',''))>
			</cfdefaultcase>
		</cfswitch>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif event.getValue('registrantAction','') neq "download">
					<cfset local.tabs = buildCSS() />
					<cfhtmlhead text="#local.tabs#">
					
					<cfset local.jScript = buildJS() />
					<cfhtmlhead text="#local.jScript#">					
				</cfif>
				<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
				<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/tip_balloon.js"></script>
				<table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-top:10px;">
					<tr>
						<td><div class="tsAppHeading">#arguments.ApplicationInstanceName#</div></td>
					</tr>
					<tr>
						<td>
							<div style="margin-top:10px;" class="tsAppBodyText" id="tabset_#lcase(local.currentTab)#">
								<ul id="tabs">
									<li id="nav_searchjobs"><a href="#variables.baseURL#&action=SearchJobs">Search Job Openings</a></li>
									<cfif val(variables.myRights.jbBrowseResumes)>
										<li id="nav_searchresumes"><a href="#variables.baseURL#&action=SearchResumes">Search for Candidates</a></li>
									</cfif>
									<cfif val(variables.myRights.jbPostResumes)>
										<li id="nav_myresume"><a href="#variables.baseURL#&Action=MyResume">My Resumes</a></li>
									</cfif>
									<cfif val(variables.myRights.jbPostJobs)>
										<li id="nav_myjobs"><a href="#variables.baseURL#&Action=MyJobs">My Jobs</a></li>
									</cfif>
									<cfif val(variables.myRights.jbEditAll)>
										<li id="nav_manage"><a href="#variables.baseURL#&action=Manage">Manage Listings</a></li>
									</cfif>
								</ul>
							</div>
						</td>
					</tr>
					<tr>
						<td>
							<div id="listing" class="tsAppBodyText" style="padding-left:15px; padding-right:10px;">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn application.objCommon.minText(local.data) />
	</cffunction>

	<cffunction name="buildFooter" access="private" returntype="string" output="no">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew() />
		<cfsavecontent variable="local.data">
			<cfoutput>
				</div></td></tr></table>				
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn application.objCommon.minText(local.data) />
	</cffunction>

	<cffunction name="getCategories" access="public" returntype="query">
		<cfargument name="jobBankID" type="numeric" required="true">

		<cfset var local = structNew() />
		
		<cfquery name="local.getCategoryIds" datasource="#application.dsn.membercentral.dsn#">
			select categoryId, description
			from JobBankCategories
			where JobBankID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.jobBankID#">
			order by description
		</cfquery>
		
		<cfreturn local.getCategoryIds />
	</cffunction>

	<cffunction name="getWorkStatus" access="public" returntype="query">
		
		<cfset var local = structNew() />
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			select employmentTypeId, Description
			from JobBankEmploymentTypes
		</cfquery>
		
		<cfreturn local.data />
	</cffunction>

	<cffunction name="getWorkExperience" access="public" returntype="query">
		
		<cfset var local = structNew() />
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			select workExperienceId, Description
			from JobBankWorkExperience
			order by sortOrder
		</cfquery>	
		
		<cfreturn local.data />
	</cffunction>

	<cffunction name="getEducationLevel" access="public" returntype="query">

		<cfset var local = structNew() />
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			select educationLevelId, Description
			from JobBankEducationLevel
		</cfquery>	
		
		<cfreturn local.data />
	</cffunction>

	<cffunction name="getMemberAddress" access="private" returntype="query">
		<cfset var local = structNew()>
		
		<cfset local.data = application.objMember.getMemberAddressByFirstAddressType(orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgid, memberid=session.cfcUser.memberData.memberID)>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getJobBankSectionID" access="public" returntype="numeric">
		<cfargument name="siteResourceID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @RTID int;
			select @RTID = dbo.fn_getResourceTypeID('ApplicationCreatedSection');

			SELECT ps.sectionID
			FROM dbo.cms_pageSections ps
			INNER JOIN dbo.cms_siteResources ssr ON ps.siteResourceID = ssr.siteResourceID
				AND ssr.resourceTypeID = @RTID
			INNER JOIN dbo.cms_siteResources srp ON ssr.parentSiteResourceID = srp.siteResourceID
				AND srp.siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceID#">
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = ssr.siteResourceStatusID
				AND srs.siteResourceStatusDesc = 'Active';
		</cfquery>	
		
		<cfreturn local.data.sectionID>
	</cffunction>

	<cffunction name="isValidURLParams" access="private" returntype="boolean">
		<cfargument name="event" type="any" required="yes">

		<cfscript>
		var local = structNew();

		local.redirectFlag = true;			
		local.allowedActionsValues = "SearchJobs,SearchResumes,MyJobs,ManageJob,MyResume,ManageResume,Manage,redirectNewMember,verifyTaxInfo,saveTaxInfo,NoRights,disabled,paypending";
		local.allowedActionsDoValues = {
			SearchJobs = "search,results,details",
			SearchResumes = "search,results,details",
			MyJobs = "list,Add,Edit,Save,Delete",
			ManageJob = "list,Add,Edit,Save,Delete",
			MyResume = "list,Add,Edit,Save,Delete,DeleteDoc",
			ManageResume = "list,Add,Edit,Save,Delete,DeleteDoc",
			Manage = "list"
		};
		local.allowedActionsOrderValues = {
			SearchResumes= {Columns = "res,loc,dpos,dava,cat", Types="asc,desc"},
			SearchJobs = {Columns = "job,emp,st,city,date", Types="asc,desc"}
		};

		if (arguments.event.valueExists('action') AND NOT listfindnocase(local.allowedActionsValues,arguments.event.getValue('action'))) {
			local.redirectFlag = false;
		} else if (arguments.event.valueExists('action') AND arguments.event.valueExists('do') AND NOT listfindnocase(local.allowedActionsDoValues[arguments.event.getValue('action')], arguments.event.getValue('do'))) {
			local.redirectFlag = false;
		} else if (arguments.event.valueExists('action') AND arguments.event.valueExists('page') AND 				
			(
				NOT isNumeric(arguments.event.getValue('page')) 
				OR find(".",arguments.event.getValue('page')) 
				OR (isNumeric(arguments.event.getValue('page')) AND arguments.event.getValue('page') < 0)
			)
		) {
			local.redirectFlag = false;
		} else if (arguments.event.valueExists('action') AND arguments.event.valueExists('orderColumn')
			AND len(trim(arguments.event.getValue('orderColumn'))) GT 0 
			AND NOT listfindnocase(local.allowedActionsOrderValues[arguments.event.getValue('action')].Columns, arguments.event.getValue('orderColumn')) 
		) {
			local.redirectFlag = false;
		} else if (arguments.event.valueExists('action') AND arguments.event.valueExists('orderby') 
			AND len(trim(arguments.event.getValue('orderby'))) GT 0 
			AND NOT listfindnocase(local.allowedActionsOrderValues[arguments.event.getValue('action')].Types, arguments.event.getValue('orderby'))				
		) {
			local.redirectFlag = false;
		} else if (arguments.event.getValue('action','') eq "searchResumes" AND arguments.event.getValue('do','') eq "details" 
			AND arguments.event.valueExists('resumeID') AND arguments.event.getValue('resumeID') neq int(val(arguments.event.getValue('resumeID'))) ) {
			local.redirectFlag = false;
		}
		
		return local.redirectFlag;
		</cfscript>
	</cffunction>

	<!--- APPLICATION FUNCTIONS:--------------------------------------------------------- --->
	<cffunction name="setInstanceSettings" access="private" returntype="void">
		<cfargument name="instanceSettings" type="struct" required="yes"/>
		<cfset variables.instanceSettings = arguments.instanceSettings>
	</cffunction>

	<cffunction name="getInstanceSettings" access="private" returntype="struct" output="no">
		<cfargument name="applicationInstanceID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.getInstanceSettings" datasource="#application.dsn.membercentral.dsn#">
			select ai.siteresourceID, ai.applicationInstanceID, ai.siteID, isNull(jb.title, ai.applicationInstanceName) as applicationInstanceName, ai.settingsXML, s.orgID,
				jb.jobBankID, jb.disabled, jb.maxsearchresults, jb.recordsperpage, jb.maxDaysForPostingJobs, jb.maxDaysForPostingResumes, 
				jb.allowSearchCity, jb.allowSearchState, jb.allowSearchDateOpen, jb.allowSearchDatePosted, jb.allowSearchWorkStatus, jb.allowSearchPositionDescription, 
				jb.allowSearchObjective, jb.allowSearchJobTitle, jb.allowSearchEmployer, jb.allowEmployerToHideIdentity, jb.allowCandidateToHideIdentity, 
				jb.allowDocumentsAttached, jb.allowSearchJobCategory, jb.agreementStatement, jb.notifyEmails, jb.exclusiveAccessDays
			from dbo.jobBank as jb 
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = jb.applicationInstanceID
				and ai.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.applicationInstanceID#">
			inner join dbo.sites s on s.siteID = ai.siteID				
		</cfquery>
				
		<cfset local.instanceSettings = structNew()>
		<cfloop index="local.thisField" list="#local.getInstanceSettings.columnList#">
			<cfset local.instanceSettings[local.thisField] = local.getInstanceSettings[local.thisField]>
		</cfloop>
		
		<cfreturn local.instanceSettings>
	</cffunction>

	<cffunction name="actionAddDocument" access="public" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.fileUploaded = FALSE>
		<cfset local.returnStruct.reasonText = ''>
		<cfset local.returnStruct.documentID = 0>
		
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		<cfscript>
			// check to see if there is a new file to upload --------------------------------------------
			if(arguments.event.getTrimValue('newFile') NEQ "" ){
				// if yes then set fileToUpload to the form variable newFile ------------------------------
				arguments.event.setValue('fileToUpload','newFile');
				// pre set the fileUploaded variable to TRUE ----------------------------------------------
				local.fileUploaded = TRUE;
				// try to upload the file to the proper destination ---------------------------------------
				try {
					local.newFile = local.objDocument.uploadFile("form.newFile");
					if (local.newFile.uploadComplete){
						arguments.event.setValue('fileName',local.newFile.clientFile);
						arguments.event.setValue('fileExt',local.newFile.clientFileExt);
					}
					else{ local.fileUploaded = FALSE; }
				}
				catch(any excpt) {
					local.fileUploaded = FALSE; // if if fails to upload then set the fileUploaded flag to FALSE ----
				}
			}
			local.returnStruct.fileUploaded = local.fileUploaded;
			
			if (local.fileUploaded and NOT listFindNoCase("doc,docx,rtf,pdf",local.newFile.clientFileExt)) {
				local.fileUploaded = false;
				local.newFile.ReasonText = "Only .DOC, .RTF, and .PDF are accepted.";
			}
			
			if( local.fileUploaded ){
				//if there is already a document then add version
				if(val(arguments.event.getValue('jb_documentID'))){
					local.docData = local.objDocument.getDocumentData(documentID=arguments.event.getValue('jb_documentID'),documentVersionID=0,activeOnly=1,lan=session.mcStruct.languageID );
					local.newVersionID = local.objDocument.insertVersion(
						orgcode=arguments.event.getValue('mc_siteInfo.orgcode'),
						siteCode=arguments.event.getValue('mc_siteInfo.siteCode'),
						fileData=local.newFile, 
						documentLanguageID=local.docData.documentLanguageID,
						contributorMemberID=session.cfcuser.memberdata.memberID,
						recordedByMemberID=session.cfcuser.memberdata.memberID,
						author=arguments.event.getValue('author',''),
						isActive=1);
					local.documentID 				= arguments.event.getValue('jb_documentID');
					local.documentVersionID = local.newVersionID;
				}
				// else just insert new document
				else{
					local.newDoc = local.objDocument.insertDocument(
						siteID=arguments.event.getValue('mc_siteinfo.siteid'),
						resourceType='ApplicationCreatedDocument',
						parentSiteResourceID=arguments.siteResourceID,
						sectionID=arguments.event.getValue('SectionID'),
						docTitle=arguments.event.getTrimValue('docTitle'),
						docDesc=arguments.event.getTrimValue('docDesc'),
						author=arguments.event.getTrimValue('author',''),
						fileData=local.newFile, 
						isActive=1,
						isVisible=true,
						contributorMemberID=session.cfcuser.memberdata.memberid,
						recordedByMemberID=session.cfcuser.memberdata.memberid);
					local.documentID = local.newDoc.documentID;
					local.documentVersionID = local.newDoc.documentVersionID;
					linkDocViewRightsToFileShare(local.documentID,arguments.siteResourceID);
				}
				local.returnStruct.documentID = local.documentID;
			}	
			
			else{
				// error in upload - locate to message page and apply message ---------------------------
				local.returnStruct.reasonText = local.newFile.ReasonText;
			}
		</cfscript>
		<cfreturn local.returnStruct />
	</cffunction>

	<cffunction name="deleteDocument" access="private" output="false" returntype="void" hint="Marks file in database as Deleted">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="documentID" type="numeric" required="true">

		<cfset CreateObject("component","model.system.platform.document").deleteDocument(siteID=arguments.siteID, documentID=arguments.documentID)>
	</cffunction>

	<cffunction name="linkDocViewRightsToFileShare" access="public" returntype="void" output="no">
		<cfargument name="documentID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryGetInfo" datasource="#application.dsn.membercentral.dsn#">
			select siteid, siteResourceID
			from dbo.cms_documents
			where documentid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">
		</cfquery>
	
		<cfstoredproc procedure="cms_createSiteResourceRight" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#local.qryGetInfo.siteid#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#local.qryGetInfo.siteResourceID#">
			<cfprocparam cfsqltype="cf_sql_bit" value="1">
			<cfprocparam cfsqltype="cf_sql_varchar" value="4">
			<cfprocparam cfsqltype="cf_sql_integer" null="yes">
			<cfprocparam cfsqltype="cf_sql_integer" null="yes">
			<cfprocparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceID#">
			<cfprocparam cfsqltype="cf_sql_integer" value="4">
		</cfstoredproc>
	</cffunction>

	<cffunction name="sendEmail" access="private" returntype="void" output="false">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="mailData" type="struct" required="true">
				
		<cfset var local = structNew()>
		<cfset local.mc_siteInfo = arguments.event.getValue('mc_siteinfo')>
		<cfscript>
			local.arrEmailTo = [];
			arguments.maildata.to = replace(arguments.maildata.to,",",";","all");
			local.toEmailArr = listToArray(arguments.maildata.to,';');
			for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
				local.arrEmailTo.append({ name:'', email:trim(local.toEmailArr[local.i]) });
			}
		</cfscript>
		
		<cfif arrayLen(local.arrEmailTo)>
			<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=local.mc_siteInfo.orgname, email=local.mc_siteInfo.networkEmailFrom },
				emailto=local.arrEmailTo,
				emailreplyto=local.mc_siteinfo.supportProviderEmail,
				emailsubject=arguments.maildata.subject,
				emailtitle=arguments.maildata.emailTitle,
				emailhtmlcontent=arguments.maildata.message,
				emailAttachments=arguments.maildata.emailAttachments,
				siteID=local.mc_siteInfo.siteid,
				memberID=arguments.maildata.memberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="JOBBANK"),
				sendingSiteResourceID=arguments.maildata.resourceId
			)>
		</cfif>
	</cffunction>	

	<!--- CUSTOM FUNCTIONS FOR BUYNOW INFO: ------------------------------------------------->
	<cffunction name="getJobData" access="public" returntype="query">
		<cfargument name="jobBankID" type="numeric" required="true">
		<cfargument name="jobID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		
		<cfset var local = structNew() />
		<cfset local.objJB	= CreateObject('component','model.admin.jobBank.jobBank')>
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">,
				@memberID int = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">;

			SELECT ai.siteresourceID, ai.applicationInstanceID, ai.applicationInstanceName, ai.siteID, s.orgID,
				jb.jobBankID, jb.disabled, jb.maxDaysForPostingJobs, jb.notifyEmails,
				jbj.jobID,jbj.firstName,jbj.lastName,jbj.contactFirm,jbj.contactTitle,jbj.phone,jbj.fax,jbj.emailAddress
				,jbj.JobTitle,jbj.employer
				,jbj.howToApplyContentId, jbj.requiredExperienceContentId, jbj.requiredSkillsContentId, jbj.positionDescriptionContentId
				,'' as howToApply, '' as requiredExperience, '' as requiredSkills, '' as positionDescription
				,jbj.categoryID,jbj.employmentTypeId,
				jbj.city,jbj.state,jbj.minSalary,jbj.maxSalary,jbj.SalaryUnit,jbj.workExperienceID,jbj.educationLevelID,jbj.dateOpen,
				jbj.purchasedPrice, cast(0 as decimal(18,2)) as purchasedPriceTax, jb.GLAccountID, isnull(ma.stateid,0) as stateIDForTax, 
				isnull(ma.postalCode,'') as zipForTax, 
				(select description from JobBankEmploymentTypes where employmentTypeId = jbj.employmentTypeId) as WorkStatus,
				(select description from JobBankWorkExperience where workExperienceId = jbj.workExperienceId) as WorkExperience,
				(select description from JobBankEducationLevel where educationLevelId = jbj.educationLevelId) as EducationLevel,
	   			(select description from JobBankCategories where categoryId = jbj.categoryId) as CategoryDescription
			FROM dbo.jobBank AS jb 
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = jb.applicationInstanceID
			INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = @memberID
			inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
			INNER JOIN dbo.jobBankJobs AS jbj ON jb.jobBankID = jbj.jobBankID and jbj.memberID = m.memberID
			left outer join dbo.ams_memberAddressTags as matag 
				inner join dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID and matagt.addressTagTypeID = matag.addressTagTypeID and matagt.addressTagType = 'Billing'
				inner join dbo.ams_memberAddresses as ma on ma.orgID = @orgID and ma.memberID = matag.memberID and ma.addressTypeID = matag.addressTypeID
				on matag.orgID = @orgID and matag.memberID = m2.memberID
			WHERE jbj.jobBankID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobBankID#">
			AND jbj.jobID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobID#">
			AND jbj.hasPaid = 0
			AND jbj.isDeleted = 0
			AND jbj.purchasedPrice > 0;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>		
		
		<cfif application.mcCacheManager.sessionValueExists('strJobBankTax')>
			<cfset local.strJobBankTax = application.mcCacheManager.sessionGetValue(keyname='strJobBankTax', defaultValue={})>
			<cfif structKeyExists(local.strJobBankTax,"stateID") and local.strJobBankTax.stateID gt 0>
				<cfset QuerySetCell(local.data,"stateIDForTax",local.strJobBankTax.stateID)>
			</cfif>
			<cfif structKeyExists(local.strJobBankTax,"zip") and len(local.strJobBankTax.zip)>
				<cfset QuerySetCell(local.data,"zipForTax",local.strJobBankTax.zip)>
			</cfif>
		</cfif>

		<!--- figure out sales tax --->
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
		<cfloop query="local.data">
			<cfif local.data.purchasedPrice gt 0>
				<cfset local.strTax = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.data.GLAccountID, saleAmount=local.data.purchasedPrice, 
											transactionDate=now(), stateIDForTax=local.data.stateIDForTax, zipForTax=local.data.zipForTax)>
				<cfset QuerySetCell(local.data,"purchasedPriceTax",local.strTax.totalTaxAmt,local.data.currentrow)>
			</cfif>
			
			<cfif len(local.data.positionDescriptionContentID)>
				<cfset local.positionDescription = local.objJB.showContentEditor("jb_PositionDescription", local.data.positionDescriptionContentID) />
			<cfelse>
				<cfset local.positionDescription = local.objJB.showContentEditor("jb_PositionDescription", 0) />
			</cfif>
			<cfif len(local.data.howToApplyContentID)>
				<cfset local.howToApply = local.objJB.showContentEditor("jb_HowToApply", local.data.howToApplyContentID) />
			<cfelse>
				<cfset local.howToApply = local.objJB.showContentEditor("jb_HowToApply", 0) />
			</cfif>
			<cfif len(local.data.requiredExperienceContentID)>
				<cfset local.requiredExperience = local.objJB.showContentEditor("jb_RequiredExperience", local.data.requiredExperienceContentID) />
			<cfelse>
				<cfset local.requiredExperience = local.objJB.showContentEditor("jb_RequiredExperience", 0) />
			</cfif>
			<cfif len(local.data.requiredSkillsContentID)>
				<cfset local.requiredSkills = local.objJB.showContentEditor("jb_RequiredSkills", local.data.requiredSkillsContentID) />
			<cfelse>
				<cfset local.requiredSkills = local.objJB.showContentEditor("jb_RequiredSkills", 0) />
			</cfif>
			<cfset QuerySetCell(local.data,"positionDescription",local.positionDescription.html,local.data.currentrow)>
			<cfset QuerySetCell(local.data,"howToApply",local.howToApply.html,local.data.currentrow)>
			<cfset QuerySetCell(local.data,"requiredExperience",local.requiredExperience.html,local.data.currentrow)>
			<cfset QuerySetCell(local.data,"requiredSkills",local.requiredSkills.html,local.data.currentrow)>
		</cfloop>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getJobBankMerchantProfiles" access="public" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="jobBankID" type="numeric" required="true">
		
		<cfset var qryProfiles = "">
		
		<cfquery name="qryProfiles" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
				@jobBankID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobBankID#">,
				@procFeeSupportedGatewayIDs varchar(10) = '10';	-- AuthorizeCCCIM
			DECLARE @tmpInvoiceProfileProcFeeOverrides TABLE (invoiceProfileID int, gatewayID int, enableProcessingFeeDonation bit, 
				processFeeDonationDefaultSelect bit, processFeeDonationFETitle varchar(100), processFeeDonationFEMsg varchar(800));

			INSERT INTO @tmpInvoiceProfileProcFeeOverrides (invoiceProfileID, gatewayID, enableProcessingFeeDonation, processFeeDonationDefaultSelect, processFeeDonationFETitle, processFeeDonationFEMsg)
			SELECT ip.profileID, psg.listitem, ip.enableProcessingFeeDonation, ip.processFeeDonationDefaultSelect, pfm.title, pfm.message
			FROM dbo.jobBank AS jb
			INNER JOIN dbo.tr_glAccounts AS gl ON gl.orgID = @orgID
				AND gl.GLAccountID = jb.GLAccountID
			INNER JOIN dbo.tr_invoiceProfiles AS ip ON ip.orgID = @orgID
				AND ip.profileID = gl.invoiceProfileID
			LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.messageID = ip.solicitationMessageID
			CROSS APPLY dbo.fn_intListToTableInline(@procFeeSupportedGatewayIDs,',') AS psg
			WHERE jb.jobBankID = @jobBankID;

			SELECT mp.profileID, mp.profileCode, g.gatewayClass, jb.notifyEmails, mp.tabTitle, mp.gatewayID, g.gatewayType,
				CASE WHEN mp.enableProcessingFeeDonation = 1 AND ISNULL(tmp.enableProcessingFeeDonation,1) = 1 THEN 1 ELSE 0 END AS enableProcessingFeeDonation,
				mp.processFeeDonationFeePercent, ISNULL(tmp.processFeeDonationDefaultSelect,mp.processFeeDonationDefaultSelect) as processFeeDonationDefaultSelect,
				mp.processFeeDonationRenevueGLAccountID, mp.processFeeDonationRevTransDesc, mp.processFeeOtherPaymentsFELabel, mp.processFeeOtherPaymentsFEDenyLabel,
				ISNULL(tmp.processFeeDonationFETitle,pfm.title) as processFeeDonationFETitle, ISNULL(tmp.processFeeDonationFEMsg,pfm.message) as processFeeDonationFEMsg, 
				mp.enableApplePay, mp.enableGooglePay, mp.enableSurcharge, mp.surchargePercent, mp.surchargeRevenueGLAccountID, mp.processingFeeLabel
			FROM dbo.jobBankMerchantProfiles as jbmp
			INNER JOIN dbo.mp_profiles as mp on mp.profileID = jbmp.merchantProfileID
			INNER JOIN dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
			INNER JOIN dbo.jobBank as jb on jb.jobBankID = jbmp.jobBankID
			LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.messageID = mp.solicitationMessageID
			LEFT OUTER JOIN @tmpInvoiceProfileProcFeeOverrides AS tmp ON tmp.gatewayID = g.gatewayID
			WHERE jbmp.jobBankID = @jobBankID
			AND mp.status = 'A'
			AND g.isActive = 1
			AND g.gatewayID not in (2,13,14)
			ORDER BY mp.frontEndOrderBy;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryProfiles>
	</cffunction>

	<!--- BUY NOW FUNCTIONS: ---------------------------------------------------------------->
	<cffunction name="buyNow_parseItem" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="strBuyNow" type="struct" required="yes">
		<cfargument name="strBuyNowReturn" type="struct" required="yes">

		<cfscript>
			var local = structNew();
			// custom template overrides: ----------------------------------------------------------------------------------------- //
			arguments.strBuyNowReturn.notIdentifiedTemplate = "NotLoggedIn_jobBank";
			arguments.strBuyNowReturn.notFoundTemplate 			= "ItemNotFound_jobBank";
			// setup item parsing: ------------------------------------------------------------------------------------------------ //
			arguments.strBuyNowReturn.ItemType 	= arguments.strBuyNow.itemType;				// jobBank
			arguments.strBuyNowReturn.ItemKey 	= arguments.strBuyNow.itemKey;				// jobbank-#jobBankID#|#jobID#|#memberID# <:: PASSED VIA URL ::
			arguments.strBuyNowReturn.ItemID 		= listRest(arguments.strBuyNow.itemKey,"-");	// #jobBankID#|#jobID#|#memberID#
			arguments.strBuyNowReturn.ItemFolder = "jobBank";
			arguments.strBuyNowReturn.thisCFC 	= this;
			// make sure we have JobBankID, JobID, and MemberID: ------------------------------------------------------------------ //
			if (listLen(arguments.strBuyNowReturn.ItemID,"|") is not 3) return arguments.strBuyNowReturn;
			// parse to get JobBankID, JobID, and MEMBERID: ----------------------------------------------------------------------- //
			arguments.strBuyNowReturn.JobBankID = GetToken(listRest(arguments.strBuyNow.itemKey,"-"),1,"|");
			arguments.strBuyNowReturn.JobID 		= GetToken(listRest(arguments.strBuyNow.itemKey,"-"),2,"|");
			arguments.strBuyNowReturn.MemberID	= GetToken(listRest(arguments.strBuyNow.itemKey,"-"),3,"|");
			// make sure there are items in the cart: ----------------------------------------------------------------------------- //
			arguments.strBuyNowReturn.qryItems = getJobData(JobBankID=arguments.strBuyNowReturn.JobBankID, JobID=arguments.strBuyNowReturn.JobID, orgID=arguments.event.getValue('mc_siteinfo.orgID'), MemberID=arguments.strBuyNowReturn.MemberID);
			if (NOT arguments.strBuyNowReturn.qryItems.recordcount) return arguments.strBuyNowReturn;

			// basics: ------------------------------------------------------------------------------------------------------------ //
			arguments.strBuyNowReturn.itemOK 							= true;
			arguments.strBuyNowReturn.buyNowPageTitle 		= "JobBank Checkout";
			arguments.strBuyNowReturn.receiptTitle 				= "Purchase Complete";
			// pricing defaults: -------------------------------------------------------------------------------------------------- //
			arguments.strBuyNowReturn.showPaymentArea 		= true;		// payment is required unless overridden below
			arguments.strBuyNowReturn.offerCoupon = false;
			arguments.strBuyNowReturn.noPaymentStatement 	= "No payment is due for this purchase.";
			arguments.strBuyNowReturn.onReceiptStatement 	= "You have purchased the following items:";
			arguments.strBuyNowReturn.purchaserTitle 			= "Purchaser";
			// merchant profiles allowed: ----------------------------------------------------------------------------------------- //
			arguments.strBuyNowReturn.paymentGateways 		= getJobBankMerchantProfiles(orgID=arguments.event.getValue('mc_siteinfo.orgID'), jobBankID=arguments.strBuyNowReturn.jobBankID);
			// pricing overrides: ------------------------------------------------------------------------------------------------- //
			if (arguments.strBuyNowReturn.qryItems.purchasedPrice lte 0) 
						arguments.strBuyNowReturn.showPaymentArea = false;		// dont show payment area if cart total is 0
			// shipping panel: ---------------------------------------------------------------------------------------------------- //
			arguments.strBuyNowReturn.showShippingArea = false;
		</cfscript>
		
		<cfreturn arguments.strBuyNowReturn>
	</cffunction>
	
	<cffunction name="buynow_buy" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="strBuyNow" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.strResponse = { success=false, response='' }>
		<cfset local.strBuynow = arguments.strBuyNow>

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0>
			<cfset local.useMID = session.cfcUser.memberData.IdentifiedAsMemberID>
		<cfelse>
			<cfset local.useMID = session.cfcUser.memberData.memberID>
		</cfif>

		<!--- determine payment profileID and profileCode: ----------------------------------------------------------------------------->
		<cfset arguments.event.paramValue('profileid',0)>
		<cfquery name="local.qryMerchantProfile" dbtype="query">
			select profileID, profileCode, notifyEmails, tabTitle, gatewayID, gatewayType, enableProcessingFeeDonation, processFeeDonationFeePercent,
				processFeeDonationRenevueGLAccountID, processFeeDonationRevTransDesc, enableSurcharge, surchargePercent, surchargeRevenueGLAccountID,
				processingFeeLabel
			from arguments.strBuyNow.paymentGateways
			where profileid = <cfqueryparam value="#arguments.event.getValue('profileid')#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<!--- amount to charge: -------------------------------------------------------------------------------------------------------->
		<cfset local.amountToCharge = local.strBuyNow.qryItems.purchasedPrice + local.strBuyNow.qryItems.purchasedPriceTax>
		
		<!--- payment and accounting --->
		<cfset local.strAccTemp = { totalPaymentAmount=local.amountToCharge, assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, rc=arguments.event.getCollection() } >
		<cfif local.strAccTemp.totalPaymentAmount gt 0>
			<cfset local.strAccTemp.payment = { detail='#arguments.event.getValue('mc_siteinfo.sitename')# JobBank-#local.strBuyNow.qryItems.applicationInstanceName#', 
												amount=local.strAccTemp.totalPaymentAmount, profileID=local.qryMerchantProfile.profileID, profileCode=local.qryMerchantProfile.profileCode, 
												supportPaymentFees=1, stopOnError=1, qryMerchantProfile=local.qryMerchantProfile,
												taxInfo: { stateIDForTax=local.strBuyNow.qryItems.stateIDForTax, zipForTax=local.strBuyNow.qryItems.zipForTax } }>
			
			<cfif listFindNoCase("AuthorizeCCCIM",local.qryMerchantProfile.gatewayType)>
				<cfset local.strAccTemp.payment["qryLevel3Data"] = QueryNew("name,desc,itemPriceExcDiscount,itemPriceIncDiscount,discount,qty,total","varchar,varchar,decimal,decimal,decimal,decimal,decimal")>
				<cfset QueryAddRow(local.strAccTemp.payment["qryLevel3Data"], {
					"name": "JobBank Posting",
					"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# JobBank-#local.strBuyNow.qryItems.applicationInstanceName#",
					"itemPriceExcDiscount": local.strAccTemp.payment.amount,
					"itemPriceIncDiscount": local.strAccTemp.payment.amount,
					"discount": 0,
					"qty": 1,
					"total": local.strAccTemp.payment.amount
				})>
			</cfif>
		</cfif>
		<cfset local.strAccTemp.revenue = [ { revenueGLAccountID=local.strBuyNow.qryItems.GLAccountID, detail='JobBank-#local.strBuyNow.qryItems.applicationInstanceName#', amount=local.strBuyNow.qryItems.purchasedPrice, stateIDForTax=local.strBuyNow.qryItems.stateIDForTax, zipForTax=local.strBuyNow.qryItems.zipForTax } ]>
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
		<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>

		<!--- if payment not successful --->
		<cfif local.strAccTemp.totalPaymentAmount gt 0 and NOT (local.strACCResponse.paymentResponse.responseCode is 1 and local.strACCResponse.paymentResponse.mc_transactionID gt 0)>
			<cfset local.strResponse.response = local.strACCResponse.paymentResponse.publicResponseReasonText>
			<cfreturn local.strResponse>
		</cfif>

		<!--- Build Email Data: -------------------------------------------------------------------------------------------------------->
		<cfset local.jobData = local.strBuyNow.qryItems>

		<cfquery name="local.qryPaymentTransaction" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

			select t.detail, t.transactionDate, t.amount, ph.gatewayID, ph.gatewayTransactionID, ph.gatewayApprovalCode
			from dbo.tr_transactions as t
			inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
			inner join dbo.tr_paymentHistory as ph on ph.orgID = @orgID and ph.historyID = tp.historyID
			where t.ownedByOrgID = @orgID
			and t.transactionID = <cfqueryparam value="#local.strACCResponse.paymentResponse.mc_transactionID#" cfsqltype="CF_SQL_INTEGER">
			and t.typeID = 2
			and t.statusID in (1,3);
		</cfquery>

		<cfquery name="local.qryPaymentGateway" datasource="#application.dsn.membercentral.dsn#">
			SELECT ga.gatewayID, mpContent.rawContent as paymentInstructions
			FROM dbo.mp_profiles as pr
			INNER JOIN dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
			OUTER APPLY dbo.fn_getContent(pr.paymentInstructionsContentID,1) as mpContent
			WHERE ga.gatewayID = <cfqueryparam value="#val(local.qryMerchantProfile.gatewayID)#" cfsqltype="cf_sql_integer">
			AND pr.profileID = <cfqueryparam value="#val(local.qryMerchantProfile.profileID)#" cfsqltype="cf_sql_integer">
		</cfquery>

		<!--- Get Purchaser Info --->
		<cfset local.qryPurchaser = application.objMember.getMemberInfo(local.useMID)>
		<cfset local.qryPurchaserAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberid=local.qryPurchaser.memberID)>
		<cfset local.PurchaserName = "#local.qryPurchaser.firstname# #local.qryPurchaser.lastname#">
		<cfset local.PurchaserCompany = local.qryPurchaser.company>

		<cfsavecontent variable="local.PurchaserAddress">
			<cfoutput>
			<cfif len(local.qryPurchaserAddr.address1)>#local.qryPurchaserAddr.address1#<br/></cfif>
			<cfif local.qryPurchaserAddr.hasAddress2 is 1 and len(local.qryPurchaserAddr.address2)>#local.qryPurchaserAddr.address2#<br/></cfif>
			<cfif local.qryPurchaserAddr.hasAddress3 is 1 and len(local.qryPurchaserAddr.address3)>#local.qryPurchaserAddr.address3#<br/></cfif>
			#local.qryPurchaserAddr.city# #local.qryPurchaserAddr.stateCode# #local.qryPurchaserAddr.postalCode#
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.receiptData">
			<cfoutput>
			<cfinclude template="jobBank_receipt.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfset local.mailData = structNew()>
		
		<cfset local.mailData.subject = "#arguments.event.getValue('mc_siteInfo.siteName')# - #local.jobData.applicationInstanceName# - Job Purchase Receipt">
		
		<!--- Generate download links for invoices instead of attachments --->
		<cfset local.mailData.emailAttachments = []>
		<cfset local.invoiceDownloadLinks = "">
		<cfset local.invoiceIDList = "">
		<cfif StructKeyExists(local.strACCResponse,"revenueResponse") and ArrayLen(local.strACCResponse.revenueResponse) and val(local.strACCResponse.revenueResponse[1].invoiceID) gt 0 >
			<cfset local.invoiceIDList = listAppend(local.invoiceIDList,local.strACCResponse.revenueResponse[1].invoiceID)>
		</cfif>
		<cfif StructKeyExists(local.strACCResponse,"recordAdditionalPmtFees") AND local.strACCResponse.recordAdditionalPmtFees.success>
			<cfset local.invoiceIDList = listAppend(local.invoiceIDList,local.strACCResponse.recordAdditionalPmtFees.invoiceID)>
		</cfif>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.objInvoice = CreateObject("component","model.admin.transactions.invoice")>
		<cfset local.objReportHelper = CreateObject("component","membercentral.model.reports.report")>
		<cfloop list="#local.invoiceIDList#" index="local.thisInvoiceID">
			<cfset local.strInvoice = local.objInvoice.generateInvoice(siteID=arguments.event.getValue('mc_siteinfo.siteid'), invoiceID=local.thisInvoiceID, tmpFolder=local.strFolder.folderPath, encryptFile=true, namedForBundle=false)>
			<cfset local.reportDocResult = local.objReportHelper.generateScheduledReportDocument(
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				siteCode=arguments.event.getValue('mc_siteInfo.sitecode'),
				orgCode=arguments.event.getValue('mc_siteInfo.orgcode'),
				reportFilePath=local.strFolder.folderPath & "/" & local.strInvoice.displayName,
				reportFileName=local.strInvoice.displayName,
				reportTitle="Job Purchase Invoice",
				reportDescription="PDF invoice for job purchase",
				memberID=local.useMID
			)>
			<cfif local.reportDocResult.success>
				<cfset local.invoiceDownloadLinks = local.invoiceDownloadLinks & local.reportDocResult.downloadHTML>
			</cfif>
		</cfloop>

		<cfset local.mailData.emailTitle = "#arguments.event.getValue('mc_siteInfo.siteName')# Job Purchase Receipt">

		<!--- send email to User: ------------------------------------------------------------------------------------------------------>
		<cfset local.emailSentToUser = TRUE>
		<cftry>
			<cfset local.mailData.to = session.cfcUser.memberData.email>
			<cfset local.mailData.message = local.receiptData>
			<cfset local.mailData.resourceId = local.jobData.siteresourceID>
			<cfset local.mailData.memberID = local.useMID>
			<cfset sendEmail(event=arguments.event, maildata=local.mailData)>
		<cfcatch type="any"><cfset local.emailSentToUser = FALSE></cfcatch>
		</cftry>
		
		<!--- send email to Association: ----------------------------------------------------------------------------------------------->
		<cfif len(local.jobData.notifyEmails)>
			<cfsavecontent variable="local.receiptDataAssn">
				<cfoutput>
				<cfif NOT local.emailSentToUser>
					<p>
						#local.jobData.firstName# #local.jobData.lastName# was not sent email confirmation due to bad Data.<br />
						Please contact, and let them know.
					</p>
				</cfif>
				#local.strACCResponse.accResponseMessage#
				#local.receiptData#
				</cfoutput>
			</cfsavecontent>

			<cfset local.mailData.to = local.jobData.notifyEmails>
			<cfset local.mailData.message = local.receiptDataAssn>	
			<cfset local.mailData.resourceId = local.jobData.siteresourceID>
			<cfset local.mailData.memberID = arguments.event.getValue('mc_siteInfo.sysmemberid')>
			<cfset sendEmail(event=arguments.event, maildata=local.mailData)>
		</cfif>
		
		<!--- update JobBankJobs Table to show that job has been paid for --->
		<cfquery name="local.qryUpdateOrder" datasource="#application.dsn.membercentral.dsn#">
			UPDATE dbo.jobBankJobs
			SET hasPaid = <cfqueryparam cfsqltype="CF_SQL_BIT" value="1">,
				removeOnDate = <cfqueryparam cfsqltype="cf_sql_date" value="#createODBCDate(dateAdd('d',local.strBuyNow.qryItems.maxDaysForPostingJobs,now()))#">,
				datePosted = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#createODBCDate(now())#">
			WHERE jobID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strBuyNow.qryItems.JobID#">
		</cfquery>
		
		<cfif application.mcCacheManager.sessionValueExists('strJobBankTax')>
			<cfset application.mcCacheManager.sessionDeleteValue('strJobBankTax')>			
		</cfif>
		
		<!--- set ProfileCode into strResponse --->
		<cfset local.strResponse.profileCode = local.qryMerchantProfile.profileCode>
		<!--- for receipt page display --->
		<cfset local.strResponse.receiptData = local.receiptData>

		<cfset local.strResponse.success = true>
		
		<cfreturn local.strResponse>
	</cffunction>
	
	<cffunction name="buyNow_receipt" access="public" output="no" returntype="struct">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="strBuyNow" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.strResponse = structNew()>
		<cfset local.strBuynow = arguments.strBuyNow />
		<cfset local.jobData = local.strBuyNow.qryItems>

		<cfset local.strResponse.appBaseLink = application.objApplications.getAppBaseLink(applicationInstanceID=local.jobData.applicationInstanceID, siteID=arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfreturn local.strResponse />
	</cffunction>

	<cffunction name="createAppInstance" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="baseLink" type="string">
		<cfargument name="appInfo" type="query">
		<cfscript>
			var local 											= structNew();		
			// SET EVENT SPECIFICATION ----------------------------------------------
			variables.isCommunityReady 			= FALSE;
			variables.isMultiInstanceReady 	= FALSE;			
			arguments.event.paramValue('appTypeID','0');
			// LOAD OBJECTS ---------------------------------------------------------
			local.objAppCreation 						= CreateObject("component","model.admin.pages.appCreationProcess");
			//queries ---------------------------------------------------------------
			local.appInfo										= arguments.appInfo;
			// call the contruct to do all the page validation and form params ------			
			contructAppInstanceForm(arguments.event,local.appInfo);			
		</cfscript>
		<cfif cgi.request_method eq "POST" AND NOT arguments.event.getValue('error.formErrors')>			
			<cftry>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.createApp">
					set nocount on
				      <!--- create event app instance for community --->
				      declare 
				      	@siteID	int,
				      	@languageID int,
				      	@sectionID int,
				      	@isVisible bit,
				      	@pageName varchar(50), 
				      	@pageTitle varchar(200),
				      	@pagedesc varchar(400),
				      	@zoneID int,
				      	@pageTemplateID int,
				      	@pageModeID int,
				      	@pgResourceTypeID int,
				      	@allowReturnAfterLogin bit,
				      	@applicationInstanceName varchar(100),
				      	@applicationInstanceDesc varchar(200),	      	
				      	@applicationInstanceID int, 
				      	@siteResourceID int, 
				      	@pageID int,
				      	@defaultGLAccountID	int
							
						SELECT @siteID 							=	<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
						SELECT @languageID						= 	<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('lid')#">
						SELECT @isVisible						= 	1
						SELECT @pageName						= 	<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageName')#">
						SELECT @pageTitle						= 	<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageTitle')#">
						SELECT @pagedesc						= 	<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageDesc')#">
						SELECT @zoneID							= 	dbo.fn_getZoneID('Main')
						SELECT @pageTemplateID					= 	NULL
						SELECT @pageModeID						= 	<cfif arguments.event.getValue('pageModeID','0') EQ 0>NULL<cfelse><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('pageModeID')#"></cfif>
						SELECT @allowReturnAfterLogin			= 	1
						SELECT @applicationInstanceName			= 	<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('appInstanceName')#">
						SELECT @applicationInstanceDesc			= 	<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('appInstanceDesc')#">
						SELECT @sectionID						= 	<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('sectionID')#">
					    SELECT @pgResourceTypeID				= 	dbo.fn_getResourceTypeId('ApplicationCreatedPage')	
						SELECT @defaultGLAccountID				=	<cfqueryparam value="#arguments.event.getValue('GLAccountID',0)#" cfsqltype="cf_sql_integer">										
				      
						EXEC dbo.cms_createApplicationInstanceJobBank
										@siteid									=	@siteid, 
										@languageID								=	@languageID,							
										@sectionID								=	@sectionID, 
										@isVisible								=	@isVisible,
										@pageName								=	@pageName,
										@pageTitle								=	@pageTitle,
										@pagedesc								=	@pagedesc,
										@zoneID									=	@zoneID,
										@pageTemplateID							=	@pageTemplateID,
										@pageModeID								=	@pageModeID,							
										@pgResourceTypeID						=	@pgResourceTypeID,							
										@allowReturnAfterLogin					=	@allowReturnAfterLogin,
										@applicationInstanceName				=	@applicationInstanceName,
										@applicationInstanceDesc				=	@applicationInstanceDesc,	
										@defaultGLAccountID						=	@defaultGLAccountID,						
										@applicationInstanceID					=	@applicationInstanceID OUTPUT,
										@siteResourceID							=	@siteResourceID OUTPUT,
										@pageID									=	@pageID OUTPUT						
										
						select 	@applicationInstanceID as applicationInstanceID,
								@siteResourceID as siteResourceID,
								@pageID as pageID					
					
					set nocount off
				</cfquery>				
				<cfset local.message = 1>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
					<cfset local.message = 2>
				</cfcatch>
			</cftry>
			<cfoutput>
				<script language="javascript">
					top.reloadPageTable();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>			
		<cfelse>
			<cfoutput>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.getMode">
					select dbo.fn_getmodeId('Full') as pageModeID
				</cfquery>
				<cfset arguments.event.setValue('pageModeID',local.getMode.pageModeID)>
				<cfset showAppInstanceForm(arguments.event,local.appInfo)>
			</cfoutput>
		</cfif>		
	</cffunction>
	
	<cffunction name="showAppInstanceForm" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="appInfo" type="query">

		<cfset var local = structNew()>

		<cfset local.appInfo = arguments.appInfo>
		<cfset local.allow = FALSE>
		<cfif local.appInfo.instancesCreated EQ 0>
			<cfset local.allow = TRUE>
		<cfelse>
			<cfif variables.isMultiInstanceReady AND (local.appInfo.maxInstancesPerSite - local.appInfo.instancesCreated) GT 0>
				<cfset local.allow = TRUE>
			</cfif>
		</cfif>

		<cfif local.allow>
			<cfscript>				
			local.objAppCreation = CreateObject("component","model.admin.pages.appCreationProcess");
			local.objSection = CreateObject("component","model.system.platform.section");
			local.objPageAdmin = CreateObject("component","model.admin.pages.pageAdmin");
			local.qryCommunities = local.objAppCreation.getCommunities(arguments.event.getValue('mc_siteInfo.siteID'));
			local.getSections = local.objSection.getRecursiveSections(siteID=arguments.event.getValue('mc_siteInfo.siteID'));
			local.qryModes = local.objPageAdmin.getAvailableModes();
			local.qryLanguages = local.objPageAdmin.getAvailableLanguages();
			if( local.appInfo.recordCount ) variables.allowPageNameChange = local.appInfo.allowPageNameChange;
			// Initialize GL Account Widget for Revenue GL Account
			local.objGLAcctWidget = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget");
			local.strGLAcctWidgetData = {
				label="Revenue Account",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="GLAccountID",
				idFldValue=val(arguments.event.getValue('GLAccountID',0)),
				pathFldValue=arguments.event.getValue('GLAccountPath',''),
				pathNoneTxt="(No account selected)"
			};
			local.strGLAcctWidget = local.objGLAcctWidget.renderWidget(strWidgetData=local.strGLAcctWidgetData);
			
			</cfscript>
			
			<cfsavecontent variable="local.js">
				<cfoutput>
				<!--- GL Account Widget JavaScript --->
				#local.strGLAcctWidget.js#
				<script language="javascript">
					function doDeployToComm() {
						var thisForm = document.forms["frmCreateApp"];
						var deployValue = 0;
						for (var i=0; i < thisForm.deployToComm.length; i++){
							if (thisForm.deployToComm[i].checked) deployValue = thisForm.deployToComm[i].value;
						}
						if( deployValue == 1){
							document.getElementById('showCommunities').style.display = '';
							document.getElementById('showSections').style.display = 'none';
						} else {
							document.getElementById('showCommunities').style.display = 'none';
							document.getElementById('showSections').style.display = '';
						}					
					}		
					function setDuplicateMessage(boxEl, messageEl, iconEl, success, message){
						iconEl.toggleClass('fa-circle-check', success).toggleClass('fa-circle-exclamation', !success);
						messageEl.html(message);
						boxEl.toggleClass('text-green', success).toggleClass('text-danger', !success).removeClass('d-none');
					}
					function doesPageExist(pageName) {
						var boxEl = $('##pageBox');
						var messageEl = $('##pageText');
						var iconEl = $('##pageImg');
						var re = /[^a-zA-Z0-9\-_]/;
						mca_hideAlert('err_createapp');

						var existsResult = function(r) {
							if (r.success && r.success.toLowerCase() == 'true'){
								setDuplicateMessage(boxEl, messageEl, iconEl, !r.pageexists, r.pageexists ? 'Page Name already used!' : 'Passed!');
							} else {
								boxEl.addClass('d-none');
								mca_showAlert('err_createapp', 'We were unable to check whether this page name exists.');
							}
						};

						if (pageName.length > 0) {
							if(re.test(pageName)){
								setDuplicateMessage(boxEl, messageEl, iconEl, false, 'Only letters, numbers, underscores, and dashses are allowed');
							}
							else {
								checkPageExists(pageName,existsResult);
							}
						}
						else {
							boxEl.addClass('d-none');
							return false;
						}
					}
					function checkPageExists(pageName,callback) {
						var objParams = { pageID:#val(arguments.event.getValue('pageID',0))#, pageName:pageName };
						TS_AJX('PAGE','pageExists',objParams,callback,callback,10000,callback);
					}
					function validatePageForm() {
						toggleFinishButton(false);
						mca_hideAlert('err_createapp');
						var thisForm = document.forms["frmCreateApp"];	
						var arrPromises = [];					
						var arrReq = new Array();

							if($('##pageName').length && $.trim($('##pageName').val()).length == 0 && $('##pageName').is(':visible')) {
								arrReq[arrReq.length]	= 'Enter a valid name for this page.';
							}
							if($('##pageTitle').length && $.trim($('##pageTitle').val()).length == 0) {
								arrReq[arrReq.length]	= 'Enter a page title.';
							}
							if($('##pageDesc').length && $.trim($('##pageDesc').val()).length == 0) {
								arrReq[arrReq.length]	= 'Enter a page description.';
							}
							if($('##appInstanceName').length && $.trim($('##appInstanceName').val()).length == 0) {
								arrReq[arrReq.length]	= 'Enter a name for this #local.appInfo.applicationTypeDesc#.';
							}
							if($('##appInstanceDesc').length && $.trim($('##appInstanceDesc').val()).length == 0) {
								arrReq[arrReq.length]	= 'Enter a description for this #local.appInfo.applicationTypeDesc#.';
							}
							<cfif variables.isCommunityReady>
								if($('input[name=deployToComm]').length && $.trim($('input[name=deployToComm]:checked').val()) == 1 && $('##commSRID').length  && $.trim($('##commSRID').val()) == 0) {
									arrReq[arrReq.length]	= 'You must select an e-Community.';
								}
							</cfif>

							var GLAccountRegEx = new RegExp("[0-9]*\.?[0-9]*[1-9]", "gi");

							if($('##GLAccountID').length && $.trim($('##GLAccountID').val()).length == 0) {
								arrReq[arrReq.length]	= 'Select a GL Account';
							}else if(!(GLAccountRegEx.test($.trim($('##GLAccountID').val())))){
								arrReq[arrReq.length]	= 'Select a GL Account';
							}
							
						if (arrReq.length > 0) {
							mca_showAlert('err_createapp', arrReq.join('<br/>'), true);
							toggleFinishButton(true);
							return false;
						}
						<cfif variables.allowPageNameChange>
							arrPromises.push(
								new Promise(function(resolve, reject) {
									var checkPageNameResult = function(r) {
										if (r.success && r.success.toLowerCase() == 'true'){
											if(r.pageexists == true) {
												setDuplicateMessage($('##pageBox'), $('##pageText'), $('##pageImg'), false, 'Page Name already used!');
												mca_showAlert('err_createapp', 'Page Name already used.', true);
												toggleFinishButton(true);
												reject();
											}
											else resolve();
										} else {
											mca_showAlert('err_createapp', 'We were unable to check whether this page name exists.');
											toggleFinishButton(true);
											reject();
										}
									};
									if($.trim($('##pageName').val()).length) {
										checkPageExists($.trim($('##pageName').val()),checkPageNameResult);
									}
									else resolve();
								})
							);	
						</cfif>						
						Promise.all(arrPromises).then(function(){
							thisForm.submit();
						}).catch((error) => {
							return false;
						});			
						return false;
					}						
				</script>			
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
			
			<cfoutput>
			<h4>Add #local.appInfo.applicationTypeDesc#</h4>
			<div class="mt-4">
				<div id="err_createapp" class="alert alert-danger mb-4 mt-2 d-none"></div>
				
				<cfform action="/?#cgi.QUERY_STRING#" method="POST" name="frmCreateApp" id="frmCreateApp" onsubmit="return validatePageForm();">
					<cfinput type="hidden" name="lid"  id="lid" value="#arguments.event.getValue('lid')#">
					<cfinput type="hidden" name="pageTemplateID"  id="pageTemplateID" value="#arguments.event.getValue('pageTemplateID')#">
					<cfinput type="hidden" name="allowReturnAfterLogin"  id="allowReturnAfterLogin" value="#arguments.event.getValue('allowReturnAfterLogin')#">

					<cfif len(trim(arguments.event.getValue('error.errorMessage')))>
						<div class="alert alert-danger mb-2">
							Correct the following errors:<br/>
							<cfloop list="#arguments.event.getValue('error.errorMessage')#" delimiters="|" index="local.currentMessage">
								- #local.currentMessage#<br />
							</cfloop>
						</div>
					</cfif>
					<div>
						<cfif variables.allowPageNameChange>
							<div class="form-row">
								<div class="col">
									<div class="form-label-group">
										<input type="text" name="pageName" id="pageName" class="form-control" autocomplete="off" value="#arguments.event.getValue('pageName')#"  onblur="doesPageExist(this.value);" maxlength="50">                      
										<label for="pageName">Page Name <span class="text-danger">*</span></label>
									</div>
									<div id="pageBox" class="form-text small mb-2 d-none">
										<i class="fa-solid" id="pageImg"></i> <span id="pageText"></span>
									</div>
								</div>
							</div>
						<cfelse>
							<div class="form-group row">
								<label for="pageName" class="col-sm-4 col-form-label-sm font-size-md">Page Name <span class="text-danger">*</span>:</label>
								<div class="col-sm-7">
									#local.appInfo.suggestedPageName# <cfinput type="hidden" name="pageName"  id="pageName" value="#local.appInfo.suggestedPageName#">
								</div>
							</div>
						</cfif>

						<div id="showSections">
							<div class="form-group" >
								<div class="form-label-group">
									<select name="sectionID" id="sectionID" class="form-control">
										<cfloop query="local.getSections">
											<option value="#local.getSections.sectionID#"<cfif arguments.event.getValue('sectionID') EQ local.getSections.sectionID> SELECTED</cfif>>#local.getSections.thePathExpanded#</option>
										</cfloop>
									</select>
									<label for="sectionID">Section</label>
								</div>
							</div>							
						</div>
						<div class="form-group" >
							<div class="form-label-group">
								<select name="pageModeID" id="pageModeID" class="form-control">
									<option value="0">No Override</option>
									<cfloop query="local.qryModes">
										<option value="#local.qryModes.modeID#"<cfif arguments.event.getValue('pageModeID') EQ local.qryModes.modeID> SELECTED</cfif>>#local.qryModes.modeName#</option>
									</cfloop>
								</select>
								<label for="pageModeID">Mode Override</label>
							</div>
						</div>
						<div class="form-row">
							<div class="col">
								<div class="form-label-group">
									<input type="text" name="pageTitle" id="pageTitle" class="form-control" autocomplete="off" value="#arguments.event.getValue('pageTitle')#">                      
									<label for="pageTitle">Page Title <span class="text-danger">*</span></label>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group">
								<textarea name="pageDesc" id="pageDesc" class="form-control" cols="60" rows="4" maxlength="400">#arguments.event.getValue('pageDesc')#</textarea>
								<label for="pageDesc">Page Description <span class="text-danger">*</span></label>
							</div>
						</div>
						<div class="form-row">
							<div class="col">
								<div class="form-label-group">
									<input type="text" name="appInstanceName" id="appInstanceName" class="form-control" autocomplete="off" value="#arguments.event.getValue('appInstanceName')#">                      
									<label for="appInstanceName">#local.appInfo.applicationTypeDesc# Name <span class="text-danger">*</span></label>
								</div>
							</div>
						</div>
						<div class="form-row">
							<div class="col">
								<div class="form-label-group">
									<textarea name="appInstanceDesc" id="appInstanceDesc" class="form-control" cols="60" rows="4" maxlength="400">#arguments.event.getValue('appInstanceDesc')#</textarea>
									<label for="appInstanceDesc">#local.appInfo.applicationTypeDesc# Description <span class="text-danger">*</span></label>
								</div>
							</div>
						</div>

						<cfif variables.isCommunityReady>
							<div class="form-group row p-1">
								<div class="col-sm-5">
									<span>Will this application be used within an e-community?</span>		
								</div>
								<div class="col-sm-7">
									<div class="form-check-inline">
										<input type="radio" name="deployToComm" id="deployToCommYes" value="1" class="form-check-input" onClick="doDeployToComm();" checked="#iif(arguments.event.getValue('deployToComm', 0) EQ 1,de('1'),de('0'))#">
										<label class="form-check-label" for="deployToCommYes">Yes</label>
									</div>
									<div class="form-check-inline">
										<input type="radio" name="deployToComm" id="deployToCommNo" value="0" class="form-check-input" onClick="doDeployToComm();" checked="#iif(arguments.event.getValue('deployToComm', 0) NEQ 1,de('1'),de('0'))#">
										<label class="form-check-label" for="deployToCommNo">No</label>
									</div>		
								</div>
							</div>
							<div class="form-group" id="showCommunities">
								<div class="form-label-group">
									<select name="commSRID" id="commSRID" class="form-control">
										<option value="0">Choose an e-Community</option>
										<cfloop query="local.qryCommunities">
											<option value="#local.qryCommunities.siteResourceID#"<cfif arguments.event.getValue('commSRID') EQ local.qryCommunities.siteResourceID> SELECTED</cfif>>#local.qryCommunities.communityName#</option>
										</cfloop>
									</select>
									<label for="commSRID">E-Community <span class="text-danger">*</span></label>
								</div>
							</div>			
						</cfif>
						<div class="form-group p-1">
							#local.strGLAcctWidget.html#
						</div>
						<div class="form-group text-right">
							<button type="submit" name="btnSaveEventDetails" id="btnSaveEventDetails" class="btn btn-sm btn-primary d-none">Save Information</button>
						</div>
					</div>
				</cfform>	
			</div>			
			
			<cfif variables.isCommunityReady>
				<script>doDeployToComm();</script>
			</cfif>
			</cfoutput>
		<cfelse>
			<cfoutput>
			<div class="alert alert-warning">
				<h4>Unable to add #local.appInfo.applicationTypeName#</h4>
				<p>You may not add this application to your website at this time.</p>
			</div>
			</cfoutput>
		</cfif>
	</cffunction>		
	
	<cffunction name="cleanNumber" access="public" returntype="string">
		<cfargument name="AmountToClean" type="string" required="true">
		<cfset var local = structNew() />
		<cfset local.cleanNumber = ReReplace(arguments.AmountToClean,'[^0-9\.]','','ALL') />
		<cfreturn local.cleanNumber />
	</cffunction>

	<cffunction name="getBuyNowRedirectResponseScript" access="private" returntype="string">
		<cfargument name="jobBankID" type="numeric" required="true">
		<cfargument name="jobID" type="numeric" required="true">

		<cfsavecontent variable="local.retData">
			<cfoutput>
				<script type="text/javascript">
					<cfset local.itemValue = "jobBank-#arguments.jobBankID#|#arguments.jobID#|#session.cfcUser.memberData.memberID#">
					<cfset local.itemValue = urlEncodedFormat(local.itemValue)>
					top.infoBoxRelocationURL = '/?pg=buyNow&item=#local.itemValue#'
					top.closeBox();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.retData>
	</cffunction>
</cfcomponent>