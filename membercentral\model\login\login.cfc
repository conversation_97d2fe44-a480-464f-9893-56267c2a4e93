<cfcomponent extends="model.AppLoader" output="no">

	<cfset defaultEvent = "controller">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset arguments.event.paramValue('logact','login')>

		<!--- store hostinfo if this is a superuser autologin request --->
		<cftry>
			<cfif arguments.event.valueExists('hostinfo')>
				<cfset local.hostInfo = arguments.event.getTrimValue('hostinfo','')>
				<cfset local.strHost = deserializeJSON(decrypt(local.hostInfo,"M3mberC3ntr@l", "CFMX_COMPAT", "Hex"))>
				<cfset session.mcstruct["MCStaffLoginHostInfo"] = local.strHost>
			</cfif>
		<cfcatch type="any">
			<cfset systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [logic.cfc] invalid hostinfo param received by #application.objPlatform.getCurrentHostname()#", addNewLine=true, doErrorStream=true)>
		</cfcatch>
		</cftry>

		<cfif arguments.event.valueExists('returnURL')>
			<cfset session.remoteReturnURL = urldecode(arguments.event.getValue('returnURL'))>
		</cfif>

		<cfif (application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true") or (isdefined("session.enableMobile") and session.enableMobile)>
			<cfset arguments.event.setValue('viewDirectory', 'responsive')>
		<cfelse>
			<cfset arguments.event.setValue('viewDirectory', 'default')>
		</cfif>

		<cfswitch expression="#arguments.event.getTrimValue('logact')#">

			<!--- normal login form/process --->
			<cfcase value="login">
				<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and cgi.request_method eq "POST">
					<cfset local.data = doLogin(arguments.event)>
				</cfif>
				<cfif arguments.event.getValue('showErrMessage',0) NEQ 3>
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<cfset local.data = getMainLoginForm(arguments.event)>
					<cfelse>
						<cfset sendToDestination()>
					</cfif>
				</cfif>
			</cfcase>

			<!--- security code verification --->
			<cfcase value="verifySecurityCode">
				<cfset local.data = verifySecurityCode(arguments.event)>
			</cfcase>
			<cfcase value="chooseMFAMethod">
				<cfset local.data = chooseMFAMethod(arguments.event)>
			</cfcase>

			<!--- Guest Account Creation --->
			<cfcase value="createAccount">
				<cfif arguments.event.getValue('mc_siteinfo.allowGuestAccounts') is 1>
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and cgi.request_method eq "POST">
						<cfset local.objLocator = CreateObject("component","model.system.user.accountLocater")>
						<cfset local.memberID = local.objLocator.createAccount(arguments.event)>
	
						<cfquery name="local.newmemberlogin" datasource="#application.dsn.membercentral.dsn#">
							select top 1 defaultUsername, defaultpassword 
							from dbo.ams_memberSiteDefaults 
							where siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteID')#"> 
							and memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.memberID#"> 
							and status = 'A'
						</cfquery>
						
						<!--- ensure MCSecurityKey is set in session --->
						<cfset application.objUser.renderSecurityKeyElement()>

						<cfset arguments.event.setValue('username',local.newmemberlogin.defaultUsername)>
						<cfset arguments.event.setValue('password',local.newmemberlogin.defaultPassword)>
						<cfset arguments.event.setValue('mcSecKey',session.MCSecurityKey)>
						<cfset local.data = doLogin(arguments.event)>
					<cfelse>
						<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
							<cfset local.data = getGuestAccountForm(arguments.event)>
						<cfelse>
							<cfset sendToDestination()>
						</cfif>
					</cfif>
				<cfelse>
					<cfset sendToDestination()>
				</cfif>
			</cfcase>

			<!--- 1st time login/affiliation process --->
			<cfcase value="affiliate">
				<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and val(session.cfcUser.memberData.IdentifiedAsMemberID) gt 0>
					<!--- make sure user isnt already affiliated --->
					<cfquery name="local.checklogin" datasource="#application.dsn.membercentral.dsn#">
						set nocount on;

						declare @loginNetworkID int;
						set @loginNetworkID = dbo.fn_getLoginNetworkFromSiteCode('#arguments.event.getValue('mc_siteinfo.siteCode')#');

						SELECT TOP 1 
							CASE WHEN np.networkID = 1 then (select min(memberid) from ams_memberNetworkProfiles where profileID = np.profileID) else m.memberID end as memberID, 
							np.profileID, 
							np.status AS networkProfileStatus, 
							m.status AS memberstatus, 
							CASE WHEN np.networkID = 1 then 1 else 0 end as SuperAdmin
						FROM dbo.ams_networkProfiles as np
						INNER JOIN dbo.ams_memberNetworkProfiles AS mnp ON mnp.profileID = np.profileID 
							AND mnp.status = 'A'
							AND np.status = 'A'
							AND np.networkID = @loginNetworkID
						INNER JOIN dbo.ams_members AS m on m.memberID = mnp.memberID 
							and m.memberID = m.activeMemberID 
							and m.status = 'A'
							and m.memberID = #val(session.cfcUser.memberData.IdentifiedAsMemberID)#
						INNER JOIN dbo.sites AS s ON s.orgID = m.orgID 
							AND s.siteid = mnp.siteid
							AND s.siteid = #arguments.event.getValue('mc_siteinfo.siteID')#;
					</cfquery>
					<cfif local.checklogin.recordcount>
						<cflocation url="/?pg=login" addtoken="no">
					<cfelse>
						<cfset local.data = doAffiliation(arguments.event)>
					</cfif>
				<cfelse>
					<cfset sendToDestination()>
				</cfif>
			</cfcase>
			<cfcase value="affiliateWelcome">
				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) and isDefined("session.mcstruct.showAffiliationWelcome")>
					<cfset local.data = doAffiliationWelcome(arguments.event)>
				<cfelse>
					<cfset sendToDestination()>
				</cfif>
			</cfcase>
						
			<!--- comes from user agreement --->
			<cfcase value="agreement">
				<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and val(session.cfcUser.memberData.IdentifiedAsMemberID) gt 0>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.insAgreement">
						UPDATE dbo.ams_memberNetworkProfiles
						SET dateSiteAgreement = GetDate()
						WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
						AND memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcUser.memberData.IdentifiedAsMemberID#">
						AND status = 'A'
						AND dateSiteAgreement is null
					</cfquery>
					
					<cfset session.cfcuser.loggedin = 1>
					<cfset session.cfcuser.memberdata.memberid = session.cfcUser.memberData.IdentifiedAsMemberID>
					<cfset session.cfcuser.memberData.IdentifiedAsMemberID = 0>
					<cfset StructAppend(session.cfcuser,application.objUser.refresh(cfcuser=session.cfcuser, siteID=arguments.event.getValue('mc_siteinfo.siteid'), orgcode=arguments.event.getValue('mc_siteinfo.orgCode')),true)>
					<cfset setGAEventUserLoginFlag()>
					<cfset session.mcstruct['loginPolicyComplianceStatus'] = getLoginPolicyComplianceStatus().complianceStatus>
					<cfset application.objPlatformStats.updateStatsSessionID()>
					<cfset local.data = processLoginResponse(event=arguments.event, loginResponse="true")>
				<cfelse>
					<cfset sendToDestination()>
				</cfif>
			</cfcase>
			
			<cfcase value="requestReset">
				<cfif arguments.event.getValue('mc_siteinfo.useRemoteLogin') is not 1>
					<!--- 4/12/14 - this was changed to show the form even if you were logged in because Kent wanted to send reset instructions
						and have the same instructions apply even if you were logged in. he didn't like redirecting to updatemember instead. --->
					<cfif cgi.request_method eq "POST">					
						<cfset local.data = doResetRequest(arguments.event)>
						<cfset application.objCommon.redirect('/?pg=login&logact=message&msg=#local.data#')>
					<cfelse>
						<cfset local.data = getResetRequestForm()>
					</cfif>				
				<cfelse>
					<cfset sendToDestination()>
				</cfif>
			</cfcase>
			<cfcase value="resetPassword">
				<cfif arguments.event.getValue('mc_siteinfo.useRemoteLogin') is not 1>
					<!--- 4/12/14 - this was changed to allow resetting even if you were logged in because Kent wanted to send reset instructions
						and have the same instructions apply even if you were logged in. he didn't like redirecting to updatemember instead. --->
					<cfif cgi.request_method eq "POST">					
						<cfset local.data = doPasswordReset(arguments.event)>
					<cfelse>
						<cfset local.data = getResetPasswordForm(arguments.event)>
					</cfif>		
				<cfelse>
					<cflocation url="/?pg=login" addtoken="no">
				</cfif>
			</cfcase>

			<cfcase value="loginAsMember">
				<cfset local.data = doLoginAsMember(event=arguments.event)>
				<cfif arguments.event.getValue('showErrMessage',0) is 1>
					<cflocation url="/?pg=login&showErrMessage=1" addtoken="no">
				</cfif>
			</cfcase>

			<!--- Is MC SuperUser --->
			<cfcase value="MCStaffLogin">
				<cfset local.data = MCStaffLogin(arguments.event)>
			</cfcase>

			<cfcase value="manageSettings">
				<cfset local.data = manageSettings(arguments.event)>
			</cfcase>

			<cfcase value="message">
				<cfset local.strLoginOrgIdentityInfo = application.objOrgInfo.getOrgIdentity(orgIdentityID=arguments.event.getValue('mc_siteInfo.loginOrgIdentityID'))>
				<cfif listFind("1,2,3,4,5",arguments.event.getValue('msg','0'))>
					<cfsavecontent variable="local.data">
						<cfoutput>
						<div id="mc_loginApp">
						<table border="0" cellspacing="0" cellpadding="8" align="center" bgcolor="##cccccc">
						<tr>
							<td class="tsAppHeading" style="background-color:##014d7e;color:##fff;font-weight:bold;">Password Request</td>
						</tr>
						<tr>
							<td class="tsAppBodyText">
	
							<cfswitch expression="#arguments.event.getValue('msg','0')#">
								<cfcase value="1">
									Instructions for resetting your password have just been sent to the e-mail address on file.<br/><br/>
									Please check your spam folder if you have not received the e-mail within a few minutes.
								</cfcase>
								<cfcase value="2">
									<b>Your e-mail address was not found in our records.</b><br/><br/>
									Please use the e-mail address registered with #arguments.event.getValue('mc_siteInfo.OrgName')#.<br/>
									For assistance, call #local.strLoginOrgIdentityInfo.phone#.
								</cfcase>
								<cfcase value="3">
									Your account does not have a valid e-mail address on file.<br/><br/>
									For assistance, call #local.strLoginOrgIdentityInfo.phone#.
								</cfcase>
								<cfcase value="4">
									Your membership account is listed as being inactive.<br/><br/>
									Please contact #local.strLoginOrgIdentityInfo.organizationName# to resolve this matter.
								</cfcase>
								<cfcase value="5">
									Your password has been reset.<br /><br />
									An e-mail confirmation has been sent to the e-mail address on file.<br />
								</cfcase>
								</cfswitch>
	
							</td>
						</tr>							
						</table>
						</div>
						</cfoutput>
					</cfsavecontent>
				<cfelse>
					<cflocation url="/?pg=login" addtoken="no">
				</cfif>
			</cfcase>
			
			<cfdefaultcase>
				<cflocation url="/?pg=login" addtoken="no">
			</cfdefaultcase>
			
		</cfswitch>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>	

	
	<!--- -------------------------------------- --->
	<!--- for setting/getting credentials cookie --->
	<!--- -------------------------------------- --->
	<cffunction name="getCredentialsDelim" access="private" output="false" returntype="string" hint="Returns Delimiter">
		<cfreturn "~|~">
	</cffunction>
	<cffunction name="getCredentialsKey" access="private" output="false" returntype="string" hint="Returns AES Encryption Key">
		<cfreturn "xjNMPBGWL83p6bqwbF3BJA==">	<!--- used getCredentialsKey('AES') to generate --->
	</cffunction>
	<cffunction name="setCredentialsCookie" access="public" output="false" returntype="void">
		<cfargument name="username" type="string" required="yes">

		<cfset var local = structNew()>
		<cftry>
			<cfset local.PermDelim = getCredentialsDelim()>
			<cfset local.PermAESEncKey = getCredentialsKey()>
			<cfset local.stringToEncrypt = getTickCount() & local.PermDelim & arguments.username>
			<cfset local.cookievalue = Encrypt(local.stringToEncrypt,local.PermAESEncKey,"AES","Hex")>
			<cfset local.cookieExpires = dateadd("d",730,now())>
			<cfheader name="Set-Cookie" value="CRD=#local.cookievalue#; expires=#getHTTPTimeString(local.cookieExpires)#; HttpOnly;"/>
		<cfcatch type="any"></cfcatch>
		</cftry>
	</cffunction>
	<cffunction name="getCredentialsCookie" access="public" output="false" returntype="string">
		<cfargument name="credential" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.retValue = "">
		<cfif isDefined("cookie.crd")>
			<cftry>
				<cfset local.PermDelim = getCredentialsDelim()>
				<cfset local.PermAESEncKey = getCredentialsKey()>
				<cfset local.origString = Decrypt(cookie.crd,local.PermAESEncKey,"AES","Hex")>
				<cfswitch expression="#arguments.credential#">
					<cfcase value="username">
						<cfset local.idx = 2>
					</cfcase>
					<cfdefaultcase>
						<cfthrow>
					</cfdefaultcase>
				</cfswitch>
				<cfset local.retValue = GetToken(local.origString,local.idx,local.PermDelim)>
			<cfcatch type="any"></cfcatch>
			</cftry>
		</cfif>

		<cfreturn local.retValue>
	</cffunction>
		
	<cffunction name="logout" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<!--- Use custom logout if defined --->
		<cftry>
			<cfif fileExists("#application.paths.localSiteComponentsRoot.path##arguments.event.getValue('mc_siteinfo.orgcode')#/#arguments.event.getValue('mc_siteinfo.sitecode')#/custom/Login.cfc")>
				<cfset local.objCustomLogin = CreateObject("component","sitecomponents.#arguments.event.getValue('mc_siteinfo.orgcode')#.#arguments.event.getValue('mc_siteinfo.sitecode')#.custom.Login")>
				<cfif isDefined("local.objCustomLogin.logout")>
					<cfset local.objCustomLogin.logout(arguments.event)>
				<cfelse>
					<cfset doLogout(arguments.event)>
				</cfif>
			<cfelse>
				<cfset doLogout(arguments.event)>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset doLogout(arguments.event)>
		</cfcatch>
		</cftry>		
	</cffunction>

	<cffunction name="doLogout" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfset application.objPlatformStats.updateDateLoggedOut(Session)>
		<cfset application.objPlatform.resetSession(doRedirect=true, customRedirectURL="")>
	</cffunction>

	<cffunction name="doLogin" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
				
		<cfif arguments.event.valueExists('un') and len(trim(arguments.event.getValue('un')))>
			<cfset arguments.event.setValue('username',trim(arguments.event.getValue('un')))>
		<cfelseif arguments.event.valueExists('un') and not arguments.event.valueExists('username')>
			<cfset arguments.event.setValue('username','')>
		</cfif>
		
		<cfif arguments.event.valueExists('pw') and len(trim(arguments.event.getValue('pw')))>
			<cfset arguments.event.setValue('password',trim(arguments.event.getValue('pw')))>
		<cfelseif arguments.event.valueExists('pw') and not arguments.event.valueExists('password')>
			<cfset arguments.event.setValue('password','')>
		</cfif>
		
		<cfif arguments.event.valueExists('username') and arguments.event.valueExists('password')>
			<cfif application.objUser.verifySecurityKey(mcSecKey=arguments.event.getValue('mcSecKey',''))>
				<cflock name="login-#session.sessionID#" type="exclusive" timeout="10">
					<cftry>
						<!--- support for custom loginuser function --->
						<cfif fileExists("#application.paths.localSiteComponentsRoot.path##arguments.event.getValue('mc_siteinfo.orgcode')#/#arguments.event.getValue('mc_siteinfo.sitecode')#/custom/Login.cfc")>
							<cfset local.objCustomLogin = CreateObject("component","sitecomponents.#arguments.event.getValue('mc_siteinfo.orgcode')#.#arguments.event.getValue('mc_siteinfo.sitecode')#.custom.Login")>
							<cfif isDefined("local.objCustomLogin.loginuser")>
								<cfset local.loginResponse = local.objCustomLogin.loginuser(username=arguments.event.getValue('username'), password=arguments.event.getValue('password'), sitecode=arguments.event.getValue('mc_siteinfo.sitecode'), event=arguments.event)>
							<cfelse>
								<cfset local.loginResponse = loginuser(username=arguments.event.getValue('username'), password=arguments.event.getValue('password'), sitecode=arguments.event.getValue('mc_siteinfo.sitecode'))>
							</cfif>
						<cfelse>
							<cfset local.loginResponse = loginuser(username=arguments.event.getValue('username'), password=arguments.event.getValue('password'), sitecode=arguments.event.getValue('mc_siteinfo.sitecode'))>
						</cfif>

						<!--- support for custom preProcessLoginResponse function --->
						<cfif fileExists("#application.paths.localSiteComponentsRoot.path##arguments.event.getValue('mc_siteinfo.orgcode')#/#arguments.event.getValue('mc_siteinfo.sitecode')#/custom/Login.cfc")>
							<cfset local.objCustomLogin = CreateObject("component","sitecomponents.#arguments.event.getValue('mc_siteinfo.orgcode')#.#arguments.event.getValue('mc_siteinfo.sitecode')#.custom.Login")>
							<cfif isDefined("local.objCustomLogin.preProcessLoginResponse")>
								<cfset local.loginResponse = local.objCustomLogin.preProcessLoginResponse(loginResponse=local.loginResponse)>
							</cfif>
						</cfif>

						<cfset local.data = processLoginResponse(event=arguments.event, loginResponse=local.loginResponse)>
					<cfcatch type="any">
						<cfset application.objError.sendError(cfcatch=cfcatch)>
						<cfset local.data = showMainLoginForm(arguments.event)>
					</cfcatch>
					</cftry>
				</cflock>
			<cfelse>
				<cfset arguments.event.setValue('showErrMessage',1)>
				<cfset local.data = showMainLoginForm(arguments.event)>
			</cfif>
		<cfelse>
			<cfset local.data = showMainLoginForm(arguments.event)>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="generateNotyMessages" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>

		<!--- bad COF tied to overdue invoices --->
		<cfif NOT application.objUser.isSuperUser(cfcuser=session.cfcuser) and arguments.event.getValue('mc_siteinfo.notifyBadCOF') is 1>
			<cfquery name="local.qryBadCOFOverDueInvoices" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select top 1 mpp.payProfileID
				from dbo.ams_memberPaymentProfiles as mpp
				inner join dbo.tr_invoices as i on i.payProfileID = mpp.payprofileID
				inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
				where mpp.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">
				and mpp.status = 'A'
				and ins.status in ('Closed','Delinquent')
				and i.dateDue < getdate()
				and mpp.failedLastDate is not null;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfif local.qryBadCOFOverDueInvoices.recordcount>
				<cfparam name="session.mcStruct.arrNoty" type="array" default="#arrayNew(1)#">
				<cfset local.notyUID = createUUID()>
				<cfset local.tmpNoty = {
						notyUID=local.notyUID,
						params= {
							icon="icon-credit-card",
							text="#arguments.event.getValue('mc_siteinfo.notifyBadCOFMessage')#<br/><a href=""/?pg=invoices&va=listPayProfiles"">Update Payment Information</a>", 
							type="error",
							callbacks="afterShow: function() { mc_clearNoty('#local.notyUID#'); }"
						}
						}>
				<cfset arrayAppend(session.mcStruct.arrNoty, local.tmpNoty)>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="processLoginResponse" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="loginResponse" type="string" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.data = "">
		
		<!--- if loginResponse is true, check for siteagreement enforcement --->
		<cfif arguments.loginResponse eq "true" and arguments.event.getValue('mc_siteinfo.enforceSiteAgreement') is 1>
			<cfquery name="local.checklogin" datasource="#application.dsn.membercentral.dsn#">
				SELECT top 1 CASE WHEN mnp.dateSiteAgreement is null then 0 else 1 end as agreedToSiteAgreement
				FROM dbo.ams_memberNetworkProfiles AS mnp
				WHERE mnp.memberID = <cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">
				AND siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
				AND status = 'A'
			</cfquery>
			<cfif local.checklogin.agreedToSiteAgreement is 0>
				<cfset local.tmpMemberID = duplicate(session.cfcuser.memberdata.IdentifiedAsMemberID)>
				<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>
				<cfset session.cfcuser.memberdata.IdentifiedAsMemberID = local.tmpMemberID>
				<cfset arguments.loginResponse = "siteagreement">
			</cfif>
		</cfif>
	
		<cfswitch expression="#arguments.loginResponse#">
			<cfcase value="true">
				<cfif arguments.event.valueExists('username') and arguments.event.getValue('mc_siteinfo.useRemoteLogin') is not 1>
					<cfset setCredentialsCookie(username=arguments.event.getValue('username'))>
				</cfif>
				
				<cfif NOT application.objUser.isSuperUser(cfcuser=session.cfcuser)>
					<!--- run userLoggedIn hook with memberID and siteID --->
					<cfset local.strHookData = { memberID=session.cfcuser.memberdata.memberid, siteID=arguments.event.getValue('mc_siteinfo.siteid') }>
					<cfset application.objCMS.runHook(event='userLoggedIn', siteResourceID=this.siteResourceID, data=local.strHookData)>

					<!--- set MCIDME cookie --->
					<cfset application.objPlatform.setSignedCookie(cookiename="mcidme", value="#session.cfcuser.memberdata.memberid#|#arguments.event.getValue('mc_siteinfo.orgID')#|#GetTickCount()#", expires="90")>
				</cfif>

				<cfif isDefined("session.mcstruct.showAffiliationWelcome")>
					<cflocation url="/?pg=login&logact=affiliateWelcome" addtoken="No">
				<cfelse>
					<cfset generateNotyMessages(event=arguments.event)>
					<cfset sendToDestination()>
				</cfif>
			</cfcase>

			<!--- MFA --->
			<cfcase value="mfa_totp,mfa_sms,mfa_email">
				<cflocation url="/?pg=login&logact=verifySecurityCode" addtoken="No">
			</cfcase>
	
			<cfcase value="inactive">
				<cfset local.temp = getStaticContent(arguments.event.getValue('mc_siteinfo.inactiveUserContentID'),session.mcstruct.languageID)>
				<cfsavecontent variable="local.data">
					<cfoutput>
					<div>#local.temp.rawcontent#</div>
					</cfoutput>
				</cfsavecontent>
				<cfset arguments.event.setValue('showErrMessage',3)>
			</cfcase>
	
			<cfcase value="siteagreement">
				<cfset local.temp = getStaticContent(arguments.event.getValue('mc_siteinfo.siteAgreementContentID'),session.mcstruct.languageID)>
				<cfsavecontent variable="local.data">
					<cfoutput>
					<div id="mc_loginApp">
					<form name="loginprocess" method="POST" action="/?pg=login&logact=agreement">
					<div class="tsAppBodyText" style="text-align:left;margin:10px;width:90%;">
						<div class="tsAppHeading">Site Agreement and Privacy Statement</div>
						<p class="tsAppBodyText">You must accept this agreement by clicking "I Accept" in order to access this website.</p>
						<div>#local.temp.rawcontent#</div>
						<br/><br/>
						<input type="submit" name="btnSubmit" value="I Accept" class="tsAppBodyText">
					</div>
					</form>
					</div>
				</cfoutput>
				</cfsavecontent>
				<cfset arguments.event.setValue('showErrMessage',3)>
			</cfcase>

			<cfcase value="affiliate,loginprocess">	<!--- loginprocess was old name. should not be used anymore --->
				<cflocation url="/?pg=login&logact=affiliate" addtoken="No">
			</cfcase>

			<cfdefaultcase>
				<cfset arguments.event.setValue('showErrMessage',1)>
			</cfdefaultcase>
		</cfswitch>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="doAffiliationWelcome" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.temp = getStaticContent(arguments.event.getValue('mc_siteinfo.firstTimeLoginContentID'),session.mcstruct.languageID)>
		<cfset local.destination = getDestination()>

		<cfif isDefined("session.mcstruct.showAffiliationWelcome")>
			<cfset structDelete(session.mcstruct,"showAffiliationWelcome")>
		</cfif>

		<!--- if no content, just send to destination --->
		<cfif len(local.temp.rawContent) is 0>
			<cfset sendToDestination()>
		<cfelse>
		
			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					function gotoDestination() {
						var cbox = document.forms["frmAffilWel"].updateInfo;
						if (cbox.checked) self.location.href = '/?pg=updateMember&returl=#urlencodedformat(local.destination)#';
						else self.location.href = '#JSStringFormat(local.destination)#';
						
					}
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">

			<cfsavecontent variable="local.data">
				<cfoutput>
				<div id="mc_loginApp">
				<div class="tsAppHeading">You have Successfully Changed your Login Information</div>
				<br/>
				<div class="tsAppBodyText">
					Your login information was changed.
					<br/><br/>
					
					<form name="frmAffilWel">
					<div style="margin-left:40px;">
						<input type="checkbox" value="1" name="updateInfo" class="tsAppBodyText"> I'd like to review/update my information
						<br/><br/>
						<button type="button" onClick="gotoDestination();" class="tsAppBodyButton">Continue</button>
					</div>
					</form>
				</div>
	
				<cfif len(local.temp.rawContent)>
					<br/>
					<div class="tsAppBodyText">#local.temp.rawContent#</div>
				</cfif>

				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="doAffiliation" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>

		<cfset local.baselink = "/?pg=login&logact=affiliate">
		<cfset local.data = "">
		<cfset arguments.event.setValue('panel','chooselogin')>

		<!--- remote login doesnt prompt to select new username/password. just skip to doLogin --->
		<cfif arguments.event.getValue('mc_siteinfo.useRemoteLogin') is 1>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getDefaultUsername">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_VARCHAR" variable="local.defaultUsername">
			</cfstoredproc>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getDefaultPassword">
				<cfprocparam type="Out" cfsqltype="CF_SQL_VARCHAR" variable="local.defaultPassword">
			</cfstoredproc>
			<cfset arguments.event.setValue('mc_username',local.defaultUsername)>
			<cfset arguments.event.setValue('mc_password',local.defaultPassword)>
			<cfset arguments.event.setValue('panel','savelogin')>
			
		<cfelseif cgi.request_method eq "POST" 
			and arguments.event.valueExists('doUP') 
			and arguments.event.valueExists('mc_username') 
			and arguments.event.valueExists('mc_password') 
			and arguments.event.valueExists('mc_confirmPassword') 
			and len(arguments.event.getTrimValue('mc_username'))
			and len(arguments.event.getTrimValue('mc_password'))
			and arguments.event.getTrimValue('mc_password') eq arguments.event.getTrimValue('mc_confirmPassword')>
			<cfset arguments.event.setValue('panel','savelogin')>
		</cfif>

		<cfswitch expression="#arguments.event.getValue('panel')#">
			<cfcase value="chooselogin">
				<cfquery name="local.qryName" datasource="#application.dsn.membercentral.dsn#">
					select firstname
					from dbo.ams_members
					where memberID = <cfqueryparam value="#val(session.cfcUser.memberData.IdentifiedAsMemberID)#" cfsqltype="CF_SQL_INTEGER">
				</cfquery>
				
				<cfsavecontent variable="local.LoginJS">
					<cfoutput>
					<style type="text/css">
						.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
					</style>
					<script language="javascript">
						function validateChooseForm(){
							var runSubmit = true;
							if( !MCcheckPasswordAffiliation() ) runSubmit = false;
							if( runSubmit ){ return true; }
							else{ alert("Passwords do not match. Try again."); return false; }						
						}
						function MCcheckPasswordAffiliation(){
							var p = document.forms["frmChooseLogin"].mc_password.value;
							var pc	= document.forms["frmChooseLogin"].mc_confirmPassword.value;
							if( p == pc ) return true;
							else return false;
						}
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfhtmlhead text="#application.objCommon.minText(local.LoginJS)#">
				
				<cfsavecontent variable="local.data">
					<cfoutput>
					<cfform method="post" action="#local.baselink#" name="frmChooseLogin"  id="frmChooseLogin" onsubmit="return validateChooseForm()">
					<cfinput type="hidden" name="doUP"  id="doUP" value="1">	<!--- loop prevention when processing form --->
					<div id="mc_loginApp">
					<table border="0" cellspacing="0" cellpadding="8" align="center" bgcolor="##cccccc">
					<tr>
						<td class="tsAppHeading" style="background-color:##014d7e;color:##fff;font-weight:bold;">Choose Your New Username and Password</td>
					</tr>
					<tr>
						<td class="tsAppBodyText">
							<b>Hello #local.qryName.firstname#!</b><br/><br/>
							Welcome to #arguments.event.getValue('mc_siteinfo.siteName')#.<br/>
							Choose your new username and password for this site.
							
							<cfif arguments.event.getValue('showErrMessage',0) is 3>
								<div class="alert" style="margin-top:8px;">
									That username is already in use.
								</div>
							<cfelseif arguments.event.getValue('showErrMessage',0) is 4>
								<div class="alert" style="margin-top:8px;">
									There was an error creating your account.<br/>
									Contact Support for assistance.
								</div>
							</cfif>
						</td>
					</tr>
					<tr>
						<td>
							<table border="0" cellspacing="0" cellpadding="8">
							<tr>
								<td class="tsAppBodyText"><b>New Username:</b></td>
								<td><cfinput class="tsAppBodyText" type="text" name="mc_username"  id="mc_username" autocomplete="off" required="yes" message="Enter a new username" size="25" maxlength="75" value=""></td>
							</tr>
							<tr>
								<td class="tsAppBodyText"><b>New Password:</b></td>
								<td><cfinput class="tsAppBodyText" type="password" name="mc_password"  id="mc_password" autocomplete="off" required="yes" message="Enter a new password" size="25" maxlength="100" value=""></td>
							</tr>
							<tr>
								<td class="tsAppBodyText"><b>Confirm Password:</b></td>
								<td><cfinput class="tsAppBodyText" type="password" name="mc_confirmPassword"  id="mc_confirmPassword" autocomplete="off" required="yes" message="Confirm your new password" size="25" maxlength="100" value=""></td>
							</tr>
							<tr>
								<td></td>
								<td><button class="tsAppBodyButton" type="submit" name="btnSubmit">Continue</button></td>
							</tr>										
							</table>
						</td>
					</tr>							
					<tr>
						<td class="tsAppBodyText">Important: Make note of your new username and password.</td>
					</tr>
					</table>
					</div>
					</cfform>
					</cfoutput>
				</cfsavecontent>
			</cfcase>
			<cfcase value="savelogin">
				<cfquery name="local.qryLoginNetwork" datasource="#application.dsn.membercentral.dsn#">
					select dbo.fn_getLoginNetworkFromSiteCode('#arguments.event.getValue('mc_siteinfo.sitecode')#') as networkID
				</cfquery>

				<cfset local.doAction = "">

				<cfset local.username = application.objUser.login_getUsername(memberID=session.cfcuser.memberdata.IdentifiedAsMemberID, siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
				
				<!--- if we are changing username, check to see if it is a valid username before continuing. --->
				<cfif compareNoCase(arguments.event.getTrimValue('mc_username',''),local.username)>
					<cfset local.userNameAvailable = application.objUser.login_isUsernameAvailable(siteID=arguments.event.getValue('mc_siteinfo.siteID'), username=arguments.event.getTrimValue('mc_username',''))>
					<cfif NOT local.userNameAvailable>
						<cfset arguments.event.setValue('showErrMessage',3)>
						<cfset arguments.event.setValue('panel','chooselogin')>
						<cfset arguments.event.removeValue('doUP')>
						<cfset local.data = doAffiliation(arguments.event)>
						<cfreturn local.data>
					</cfif>
				</cfif>

				<cftry>
					<cfset local.npUsername = arguments.event.getValue('mc_username')>
					<cfset local.npPassword = arguments.event.getValue('mc_password')>
					<!--- when site uses site-specific passwords, we still check to see if username is available at the network level & if not, generate a random one for the network profile --->
					<cfif arguments.event.getValue('mc_siteinfo.sf_sitePasswords') is 1>
						<cfstoredproc procedure="ams_up_isUsernameAvailableWithinNetwork" datasource="#application.dsn.membercentral.dsn#">
							<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
							<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_username')#">
							<cfprocparam type="out" cfsqltype="CF_SQL_BIT" variable="local.isAvailable">
						</cfstoredproc>

						<cfif not local.isAvailable>
							<cfset local.npUsername = CreateUUID()>
							<cfset local.npPassword = CreateUUID()>
						</cfif>
					</cfif>
				
					<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createNetworkProfile">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryLoginNetwork.networkID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.npUsername#">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.npPassword#">
						<cfprocparam type="In" cfsqltype="CF_SQL_CHAR" value="A">
						<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.profileID">
					</cfstoredproc>
				<cfcatch>
					<cfset local.profileID = 0>
				</cfcatch>
				</cftry>

				<!--- if username is already in use show err 3 --->
				<cfif val(local.profileID) is 0>
					<cfset arguments.event.setValue('showErrMessage',3)>
					<cfset local.doAction = "affil">
				<cfelse>
					<cftry>
						<!--- create depoaccount on tlasites platform --->
						<!--- if there is an account in waiting, we will use that instead of creating a new one --->
						<cfset local.TrialSmithAllowedRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Site", functionName="TrialSmithAllowed")>
						<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createDepoTLASITESAccount">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.IdentifiedAsMemberID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.TrialSmithAllowedRFID#">
							<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.depomemberdataid">
							<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.TrialSmithMemberID">
						</cfstoredproc>
					<cfcatch>
						<cfquery name="local.qryUpdateNP" datasource="#application.dsn.membercentral.dsn#">
							update dbo.ams_networkProfiles
							set status = 'D'
							where profileID = <cfqueryparam value="#local.profileID#" cfsqltype="CF_SQL_INTEGER">
						</cfquery>
						<cfset local.depomemberdataid = 0>
						<cfset local.TrialSmithMemberID = 0>
						<cfset local.tmpErr = { local=local, event=arguments.event.getCollection() }>
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.tmpErr)>
						<cfset logout(arguments.event)>
					</cfcatch>
					</cftry>
						
					<cfif local.depomemberdataid gt 0>
						<cftry>
							<cfquery name="local.qryUpdateDepoID" datasource="#application.dsn.membercentral.dsn#">
								SET NOCOUNT ON;

								DECLARE @siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="CF_SQL_INTEGER">;
								DECLARE @profileID int = <cfqueryparam value="#local.profileID#" cfsqltype="CF_SQL_INTEGER">;
								DECLARE @memberID int = <cfqueryparam value="#session.cfcuser.memberdata.IdentifiedAsMemberID#" cfsqltype="CF_SQL_INTEGER">;
								DECLARE @TrialSmithMemberID int = <cfqueryparam value="#val(local.TrialSmithMemberID)#" cfsqltype="CF_SQL_INTEGER">;
								DECLARE @depomemberdataid int = <cfqueryparam value="#local.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;
								DECLARE @TSSiteID int, @TSSitePasswordsEnabled bit, @isUsernameAvailable bit = 0;
								DECLARE @username varchar(75) = <cfqueryparam value="#arguments.event.getValue('mc_username')#" cfsqltype="CF_SQL_VARCHAR">,
									@password varchar(100) = <cfqueryparam value="#arguments.event.getValue('mc_password')#" cfsqltype="CF_SQL_VARCHAR">;

								UPDATE dbo.ams_networkProfiles 
								SET depomemberdataid = @depomemberdataid
								WHERE profileID = @profileID 
								AND depomemberdataid IS NULL;

								<cfif arguments.event.getValue('mc_siteinfo.sf_sitePasswords') IS 1>
									EXEC dbo.ams_createMemberNetworkProfileWithCredentials @memberID=@memberID, @profileID=@profileID, 
										@siteID=@siteID, @username=@username, @password=@password, @status='A';
								<cfelse>
									EXEC dbo.ams_createMemberNetworkProfile @memberID=@memberID, @profileID=@profileID, @siteID=@siteID, @status='A';
								</cfif>

								<!--- affiliate with TS --->
								IF @TrialSmithMemberID > 0 BEGIN
									SELECT @TSSiteID = s.siteID, @TSSitePasswordsEnabled = sf.sitePasswords
									FROM dbo.sites as s
									INNER JOIN dbo.siteFeatures as sf on sf.siteID = s.siteID
									WHERE s.sitecode = 'TS';

									IF @TSSitePasswordsEnabled = 1 BEGIN
										<!--- check availability of the chosen username for TS Site --->
										EXEC dbo.ams_up_isUsernameAvailable @siteID=@TSSiteID, @username=@username, @isAvailable=@isUsernameAvailable OUTPUT;
										IF @isUsernameAvailable = 0 BEGIN
											SET @username = <cfqueryparam value="#CreateUUID()#" cfsqltype="CF_SQL_VARCHAR">;
											SET @password = <cfqueryparam value="#CreateUUID()#" cfsqltype="CF_SQL_VARCHAR">;
										END

										EXEC dbo.ams_createMemberNetworkProfileWithCredentials @memberID=@TrialSmithMemberID, @profileID=@profileID,
											@siteID=@TSSiteID, @username=@username, @password=@password, @status='A';
									END
									ELSE BEGIN
										EXEC dbo.ams_createMemberNetworkProfile @memberID=@TrialSmithMemberID, @profileID=@profileID, @siteID=@TSSiteID, @status='A';
									END
								END
							</cfquery>
							
							<cfset local.credentialsSaved = true>
						<cfcatch type="Any">
							<cfset local.credentialsSaved = false>
							<cfif structKeyExists(cfcatch,"detail") and findNoCase("Username is not available", cfcatch.detail)>
								<cfset arguments.event.setValue('showErrMessage',3)>
								<cfset local.doAction = "affil">
							<cfelse>
								<cfset local.tmpErr = { local=local, event=arguments.event.getCollection() }>
								<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.tmpErr)>
								<cfset arguments.event.setValue('showErrMessage',4)>
								<cfset local.doAction = "affil">
							</cfif>
						</cfcatch>
						</cftry>

						<cfif local.credentialsSaved>
							<!--- clear any reset links --->
							<cfif StructKeyExists(session.mcStruct,"resetPwdRPRID")>
								<cfset updateHasBeenUsed(session.mcStruct.resetPwdRPRID)>
								<cfset structDelete(session.mcStruct,"resetPwdRPRID")>
							</cfif>
							
							<cfset session.cfcuser.loggedin = 1>
							<cfset session.cfcuser.memberdata.memberid = session.cfcuser.memberdata.IdentifiedAsMemberID>
							<cfset session.cfcuser.memberdata.IdentifiedAsMemberID = 0>
							<cfset StructAppend(session.cfcuser,application.objUser.refresh(cfcuser=session.cfcuser, siteID=arguments.event.getValue('mc_siteinfo.siteid'), orgcode=arguments.event.getValue('mc_siteinfo.orgCode')),true)>
							<cfset setGAEventUserLoginFlag()>
							<cfset session.mcstruct['loginPolicyComplianceStatus'] = getLoginPolicyComplianceStatus().complianceStatus>
							<cfset recordLogin(sitecode=arguments.event.getValue('mc_siteinfo.siteCode'))>
							<cfset application.objPlatformStats.updateStatsSessionID()>
							<cfset arguments.event.setValue('username',arguments.event.getValue('mc_username'))>
							<cfset session.mcstruct.showAffiliationWelcome = true>
							<cfset local.data = processLoginResponse(event=arguments.event, loginResponse="true")>
						</cfif>
					<cfelse>
						<cfset arguments.event.setValue('showErrMessage',4)>
						<cfset local.doAction = "affil">
					</cfif>
				</cfif>

				<cfif local.doAction eq "affil">
					<cfset arguments.event.setValue('panel','chooselogin')>
					<cfset arguments.event.removeValue('doUP')>
					<cfset local.data = doAffiliation(arguments.event)>
				</cfif>
			</cfcase>
			<cfdefaultcase>
				<cfset local.data = "">
			</cfdefaultcase>
		</cfswitch>
		<cfreturn local.data>	
	</cffunction>
	
	<cffunction name="loginUser" access="public" returntype="string" output="false">
		<cfargument name="username" type="string" required="true">
		<cfargument name="password" type="string" required="true">
		<cfargument name="sitecode" type="string" required="true">
		<cfargument name="loginAsMemberID" type="numeric" required="false" default="0">
		<cfargument name="loginAsRequestByMemberID" type="numeric" required="false" default="0">

		<cfset var local = StructNew()>
		<cfset local.success = "">

		<cfscript>
		// trim incoming vars
		arguments.username = left(trim(arguments.username),75);
		arguments.password = left(trim(arguments.password),100);

		// if no username/password, reject
		if (arguments.loginAsMemberID is 0 and (len(arguments.username) is 0 or len(arguments.password) is 0))
			return "false";
		</cfscript>

		<cfset local.siteID = application.objSiteInfo.getSiteInfo(arguments.siteCode).siteid>

		<!--- test for superusers --->
		<cfset local.strLoginAttemptSU = application.objUser.login_attemptSuperUser(sessionID=session.cfcuser.statsSessionID, siteID=local.siteID, username=arguments.username, password=arguments.password, loginAsMemberID=arguments.loginAsMemberID)>
		<!--- if superuser and success --->
		<cfif local.strLoginAttemptSU.result eq "pass">
			<!--- skip login policy checks --->
			<cfif arguments.loginAsMemberID>
				<cfset local.success = doLoginUser(result=local.strLoginAttemptSU.result, memberID=local.strLoginAttemptSU.memberID, memberStatus='A', loginAsRequestByMemberID=arguments.loginAsRequestByMemberID)>
			<cfelse>
				<!--- any qualified login policy for this member --->
				<cfscript>
					local.qryLoginPolicyMethod = getQualifiedLoginPolicyMethod(siteID=local.siteID, memberID=local.strLoginAttemptSU.memberID);
					var arrConfiguredMethods = getConfiguredLoginPolicyMethod(siteID=local.siteID, memberID=local.strLoginAttemptSU.memberID);
					local.qryLoginPolicyMethodToApply = local.qryLoginPolicyMethod.filter((row) => arrConfiguredMethods.contains(row.methodCode));
				</cfscript>

				<!--- validate login policy method --->
				<cfif local.qryLoginPolicyMethodToApply.recordCount>
					<cfset local.success = processLoginPolicyMethod(sitecode=arguments.sitecode, userPassCode=local.strLoginAttemptSU.result, memberID=local.strLoginAttemptSU.memberID, 
											memberStatus='A', verifyMethodCode=local.qryLoginPolicyMethodToApply.methodCode)>

				<!--- doLoginSuperUser --->
				<cfelse>
					<cfscript>
						if (arguments.sitecode neq "mc") {
							cfparam(name="session.mcStruct.arrNoty" type="array" default="#[]#")

							local.notyUID = createUUID();
							local.tmpNoty = {
								notyUID=local.notyUID,
								params= {
									icon="icon-lock",
									text="<div style='font-size:larger;font-weight:bold;'>MC Staff Login Process Change</div>You directly logged-in on this site using your Superuser credentials. This ability will soon be blocked. <br><br> Instead, please visit Control Panel and click the MemberCentral Logo below the Login form.  Alternatively, use the new platformwide /super Quick URL.", 
									type="warning",
									callbacks="afterClose: function() { mc_clearNoty('#local.notyUID#'); }"
								}
							};
							arrayAppend(session.mcStruct.arrNoty, local.tmpNoty);
						}
						try {
							local.browserIdentifier = resetExistingLoginCookie();
						} catch(e) {
							application.objError.sendError(cfcatch=e,objectToDump=local);
							local.browserIdentifier = '';
						};
					</cfscript>
					<cfset local.success = doLoginUser(result=local.strLoginAttemptSU.result, memberID=local.strLoginAttemptSU.memberID, memberStatus='A',browserIdentifier=local.browserIdentifier, loginAsRequestByMemberID=arguments.loginAsRequestByMemberID)>
				</cfif>
			</cfif>
		<!--- if superuser but bad password --->
		<cfelseif local.strLoginAttemptSU.result eq "fail" and local.strLoginAttemptSU.memberID gt 0>
			<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>
			<cfset local.success = "false">

		<!--- if remote authentication and login as member, bypass username checking --->
		<cfelseif arguments.loginAsMemberID gt 0 and application.objSiteInfo.getSiteInfo(arguments.siteCode).useRemoteLogin is 1>
			<cfquery name="local.qryLogin" datasource="#application.dsn.membercentral.dsn#">
				SELECT np.profileID, mnp.siteid, m.memberID
				FROM dbo.ams_networkProfiles AS np
				INNER JOIN dbo.ams_memberNetworkProfiles AS mnp ON np.profileID = mnp.profileID
					and np.status <> 'D'
					and mnp.status <> 'D'
					and mnp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
				INNER JOIN dbo.ams_members AS m ON mnp.memberID = m.memberID
					and m.orgID = #application.objSiteInfo.getSiteInfo(arguments.siteCode).orgID#
					and m.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.loginAsMemberID#">
			</cfquery>
			<cfif local.qryLogin.profileID gt 0>
				<cfset session.cfcuser.loggedin = 1>
				<cfset session.cfcuser.memberdata.memberid = val(local.qryLogin.memberid)>
				<cfset session.cfcuser.memberdata.identifiedAsMemberID = 0>
				<cfset StructAppend(session.cfcuser,application.objUser.refresh(cfcuser=session.cfcuser, siteID=local.siteID, orgcode=application.objSiteInfo.getSiteInfo(arguments.siteCode).orgcode),true)>
				<cfset setGAEventUserLoginFlag()>
				<cfset session.mcstruct['loginPolicyComplianceStatus'] = getLoginPolicyComplianceStatus().complianceStatus>
				<cfset local.success = true>
				<cfset recordLogin(sitecode=arguments.sitecode, requestByMemberID=arguments.loginAsRequestByMemberID)>
				<cfset application.objPlatformStats.updateStatsSessionID()>
			<cfelse>
				<!--- Lookup member data for first time user process. --->
				<cfquery name="local.checkMemberDefault" datasource="#application.dsn.membercentral.dsn#">
					select top 1 m.memberid, s.siteid, msd.status as defaultStatus, m.status as memberStatus
					from dbo.ams_memberSiteDefaults as msd
					inner join dbo.sites as s on s.siteID = <cfqueryparam value="#local.siteID#" cfsqltype="CF_SQL_INTEGER"> and s.siteid = msd.siteid
					inner join dbo.ams_members as m on m.memberid = msd.memberID and m.status IN ('A','I')
					where m.memberid = <cfqueryparam value="#arguments.loginAsMemberID#" cfsqltype="CF_SQL_INTEGER">
					and msd.status IN ('A','I')
				</cfquery>
				
				<!--- Has a default username/password. --->
				<cfif local.checkMemberDefault.RecordCount is 1>

					<!--- inactive user --->
					<cfif local.checkMemberDefault.defaultStatus neq "A" or local.checkMemberDefault.memberStatus neq "A">
						<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>
						<cfset local.success = "inactive">

					<!--- active user, so send to affiliate ---> 
					<cfelse>
						<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>
						<cfset session.cfcuser.memberdata.identifiedAsMemberID = val(local.checkMemberDefault.memberid)>
						<cfset local.success = "affiliate">
					</cfif>

				<!--- Does not have a default username/password --->
				<cfelse>
					<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>
					<cfset local.success = "false">
				</cfif>
			</cfif>
			<cfreturn local.success>
		<!--- test for non-superusers --->
		<cfelse>
			<cfset local.strLoginAttemptU = application.objUser.login_attemptUser(sessionID=session.cfcuser.statsSessionID, siteID=local.siteID, username=arguments.username, password=arguments.password, loginAsMemberID=val(arguments.loginAsMemberID))>

			<!--- good login --->			
			<cfif listFindNoCase("pass.mnp,pass.np,pass.msd,",local.strLoginAttemptU.result) AND listFindNoCase("A,I",local.strLoginAttemptU.memberStatus)>

				<!--- skip login policy checks --->
				<cfif arguments.loginAsMemberID>
					<cfset local.success = doLoginUser(result=local.strLoginAttemptU.result, memberID=local.strLoginAttemptU.memberID, memberStatus=local.strLoginAttemptU.memberStatus,
											loginAsRequestByMemberID=arguments.loginAsRequestByMemberID)>

				<cfelse>
					<!--- any qualified login policy for this member --->
					<cfscript>
						local.qryLoginPolicyMethod = getQualifiedLoginPolicyMethod(siteID=local.siteID, memberID=local.strLoginAttemptU.memberID);
						var arrConfiguredMethods = getConfiguredLoginPolicyMethod(siteID=local.siteID, memberID=local.strLoginAttemptU.memberID);
						local.qryLoginPolicyMethodToApply = local.qryLoginPolicyMethod.filter((row) => arrConfiguredMethods.contains(row.methodCode));
					</cfscript>
					<!--- validate login policy method --->
					<cfif local.qryLoginPolicyMethodToApply.recordCount>
						<cfset local.success = processLoginPolicyMethod(sitecode=arguments.sitecode, userPassCode=local.strLoginAttemptU.result, memberID=local.strLoginAttemptU.memberID, 
												memberStatus=local.strLoginAttemptU.memberStatus, verifyMethodCode=local.qryLoginPolicyMethodToApply.methodCode)>

					<!--- doLoginUser --->
					<cfelse>
						<cfset local.success = doLoginUser(result=local.strLoginAttemptU.result, memberID=local.strLoginAttemptU.memberID, memberStatus=local.strLoginAttemptU.memberStatus,
												loginAsRequestByMemberID=arguments.loginAsRequestByMemberID)>
					</cfif>
				</cfif>

			<!--- failed login --->
			<cfelse>
				<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>
				<cfset local.success = "false">
			</cfif>
		</cfif>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="doLoginUser" access="private" output="false" returntype="string">
		<cfargument name="result" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="memberStatus" type="string" required="true">
		<cfargument name="loginAsRequestByMemberID" type="numeric" required="true">
		<cfargument name="browserIdentifier" type="string" required="false" default="">
		<cfargument name="verificationMethodCode" type="string" required="false" default="">

		<cfset var local = {}>

		<!--- if active user using np/mnp, good --->
		<cfif listFindNoCase("pass,pass.mnp,pass.np",arguments.result) AND arguments.memberStatus eq "A">
			<cfset session.cfcuser.loggedin = 1>
			<cfset session.cfcuser.memberdata.memberid = arguments.memberID>
			<cfset session.cfcuser.memberdata.identifiedAsMemberID = 0>
			<cfset structAppend(session.cfcuser,application.objUser.refresh(cfcuser=session.cfcuser, siteID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID, orgcode=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgcode),true)>
			<cfset setGAEventUserLoginFlag()>
			<cfif arguments.loginAsRequestByMemberID GT 0>
				<cfset session.mcstruct['loginPolicyComplianceStatus'] = 'bypassed'>
			<cfelse>
				<cfset session.mcstruct['loginPolicyComplianceStatus'] = getLoginPolicyComplianceStatus().complianceStatus>
			</cfif>		
			<cfset local.success = "true">
			<cfset recordLogin(sitecode=session.mcStruct.siteCode, requestByMemberID=arguments.loginAsRequestByMemberID, browserIdentifier=arguments.browserIdentifier, verificationMethodCode=arguments.verificationMethodCode)>
			<cfset application.objPlatformStats.updateStatsSessionID()>

		<!--- if active user but using default, affiliate --->
		<cfelseif arguments.result eq "pass.msd" AND arguments.memberStatus eq "A">
			<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>
			<cfset session.cfcuser.memberdata.identifiedAsMemberID = arguments.memberID>
			<cfset local.success = "affiliate">

		<!--- if inactive user --->
		<cfelseif listFindNoCase("pass.mnp,pass.np,pass.msd",arguments.result) AND arguments.memberStatus eq "I">
			<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>
			<cfset local.success = "inactive">

		<!--- else failed login --->
		<cfelse>
			<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>
			<cfset local.success = "false">
		</cfif>

		<cfreturn local.success>
	</cffunction>
	
	<cffunction name="recordLogin" access="public" returntype="void" output="false" hint="public so it can be called from custom login cfcs">
		<cfargument name="sitecode" type="string" required="true">
		<cfargument name="requestByMemberID" type="numeric" required="false" default="0">
		<cfargument name="browserIdentifier" type="string" required="false" default="">
		<cfargument name="verificationMethodCode" type="string" required="false" default="">
		
		<cfset var qryLoginHistory = "">
		<cfset var local = {}>
		<cfset local.locationInfo = {countryCode:"",regionCode:"",cityName:""}>

		<cfif structKeyExists(application.objPlatform,"getClientIPLocationInfo")>
			<cfset local.locationInfo = application.objPlatform.getClientIPLocationInfo()>
		</cfif>

		<cfquery name="qryLoginHistory" datasource="#application.dsn.platformstatsMC.dsn#">
			set nocount on;

			declare @siteID int, @orgID int, @memberID int, @requestByMemberID int, @statsSessionID int, @nowDate datetime, 
				@loginID int, @activeLoginID int, @CFID varchar(50), @browserIdentifier uniqueidentifier, @verificationMethodID int,
				@PrevdateLastRecPrompted DATETIME;
			
			declare 
				@ISOCountryCode char(2) = NULLIF(<cfqueryparam value="#local.locationInfo.countryCode#" cfsqltype="CF_SQL_VARCHAR">,''),
				@regionCode varchar(10) = NULLIF(<cfqueryparam value="#local.locationInfo.regionCode#" cfsqltype="CF_SQL_VARCHAR">,''),
				@city varchar(100) = NULLIF(<cfqueryparam value="#local.locationInfo.cityName#" cfsqltype="CF_SQL_VARCHAR">,'');

			set @nowDate = getdate();
			set @siteID = membercentral.dbo.fn_getSiteIDFromSiteCode(<cfqueryparam value="#arguments.sitecode#" cfsqltype="CF_SQL_VARCHAR">);
			set @orgID = membercentral.dbo.fn_getOrgIDFromSiteID(@siteID);
			<cfif session.cfcuser.memberdata.identifiedAsMemberID gt 0>
				set @memberID = <cfqueryparam value="#session.cfcuser.memberdata.identifiedAsMemberID#" cfsqltype="CF_SQL_INTEGER">;
			<cfelse>
				set @memberID = <cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">;
			</cfif>
			<cfif arguments.requestByMemberID gt 0>
				set @requestByMemberID = <cfqueryparam value="#arguments.requestByMemberID#" cfsqltype="CF_SQL_INTEGER">;
			</cfif>
			set @statsSessionID = <cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">;
			set @CFID = <cfqueryparam value="#session.cfid#" cfsqltype="CF_SQL_VARCHAR">;
			<cfif len(arguments.browserIdentifier)>
				set @browserIdentifier = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.browserIdentifier#">;
			</cfif>
			<cfif len(arguments.verificationMethodCode)>
				select @verificationMethodID = verificationMethodID
				from memberCentral.dbo.platform_verificationMethods
				where methodCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.verificationMethodCode#">;
			</cfif>
			SET  @PrevdateLastRecPrompted = ( SELECT top 1 dateLastRecPrompted from dbo.ams_memberLogins where memberID = @memberID and siteid = @siteID  order by dateentered desc)

			INSERT INTO dbo.ams_memberLogins (memberID, siteID, statsSessionID, dateEntered, requestByMemberID, ISOCountryCode, regionCode, city, browserIdentifier, verificationMethodID, dateLastRecPrompted)
			VALUES (@memberID, @siteID, @statsSessionID, @nowDate, @requestByMemberID, @ISOCountryCode, @regionCode, @city, @browserIdentifier, @verificationMethodID, @PrevdateLastRecPrompted);

			SET @loginID = SCOPE_IDENTITY();

			INSERT INTO dbo.ams_memberLoginsActive (loginID, siteID, memberID, dateUpdated, CFID, loginRequestedByMemberID, ISOCountryCode, regionCode, city)
			VALUES (@loginID, @siteID, @memberID, @nowDate, @CFID, <cfif arguments.requestByMemberID gt 0>1<cfelse>0</cfif>, @ISOCountryCode, @regionCode, @city);

			set @activeLoginID = SCOPE_IDENTITY();

			<!--- update last login date. need to run any conditions based on dateLastLogin --->
			update memberCentral.dbo.ams_memberNetworkProfiles
			set dateLastLogin = @nowDate
			where memberID = @memberID
			and siteID = @siteID
			and [status] = 'A';

			IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
				DROP TABLE ##tblMCQRun;
			CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

			INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
			SELECT @orgID, @memberID, conditionID
			FROM memberCentral.dbo.ams_virtualGroupConditions
			WHERE orgID = @orgID
			and left(fieldCode,16) = 'm_datelastlogin_';

			EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

			IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
				DROP TABLE ##tblMCQRun;

			select @loginID as loginID,  @activeLoginID as activeLoginID, @nowDate as activeLoginDateUpdated;
		</cfquery>

		<cfset session.cfcuser["lastLogin"] = now()>
		<cfset session.cfcuser["loginID"] = qryLoginHistory.loginID>
		<cfset session.cfcuser["activeLoginID"] = qryLoginHistory.activeLoginID>
		<cfset session.cfcuser["activeLoginDateUpdated"] = qryLoginHistory.activeLoginDateUpdated>
		<cfset session.cfcuser["activeLoginRegionCode"] = local.locationInfo.regionCode>
		<cfset session.cfcuser["activeLoginCity"] = local.locationInfo.cityName>

		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser) and NOT application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfset local.loginLimitModeCode = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).loginLimitModeCode>
			<cfset local.loginLimitModeInfo = application.objSiteInfo.mc_loginLimitModes[local.loginLimitModeCode]>

			<cfif local.loginLimitModeInfo.concurrentLoginLimit and not arguments.requestByMemberID>
				<!--- Find other active sessions --->
				<cfquery name="local.qryActiveSessions" datasource="#application.dsn.platformstatsMC.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					declare @siteID int, @memberID int, @requestByMemberID int, @statsSessionID int, @nowDate datetime, @activeLoginID int, @CFID varchar(50);
					set @nowDate = getdate();
					set @siteID = <cfqueryparam value="#application.objSiteInfo.getSiteInfo(arguments.siteCode).siteID#" cfsqltype="CF_SQL_INTEGER">;
					set @memberID = <cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">;
					set @activeLoginID = <cfqueryparam value="#qryLoginHistory.activeLoginID#" cfsqltype="CF_SQL_INTEGER">;

					select mla.activeLoginID, mla.CFID
					from dbo.ams_memberLoginsActive mla
					inner join dbo.ams_memberLogins ml on mla.loginID = ml.loginID
						and ml.siteID = @siteID
						and ml.killReasonID is null
					where mla.siteID = @siteID
					and mla.memberID = @memberID 
					and mla.activeLoginID <= @activeLoginID
					and mla.loginRequestedByMemberID = 0
					order by mla.activeLoginID desc;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfif local.qryActiveSessions.recordcount gt local.loginLimitModeInfo.concurrentLoginLimit>
					<cfloop query="local.qryActiveSessions" startrow="#local.loginLimitModeInfo.concurrentLoginLimit+1#" endrow="#local.qryActiveSessions.recordcount#">
						<cfset application.objPlatform.addSessionToKillList(cfid=local.qryActiveSessions.cfid, killReasonCode='TooManyLogins', isTestMode=(local.loginLimitModeInfo.isTestMode gt 0))>
					</cfloop>
				</cfif>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="getQualifiedLoginPolicyMethod" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="LoginPolicy", functionName="Qualify")>
		
		<cfquery name="local.qryQualifiedLoginPolicyMethod" datasource="#application.dsn.membercentral.dsn#">
			EXEC dbo.ams_getQualifiedLoginPolicy 
				@siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">,
				@qualifyFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.QualifyRFID#">;
		</cfquery>

		<cfreturn local.qryQualifiedLoginPolicyMethod>
	</cffunction>

	<cffunction name="getConfiguredLoginPolicyMethod" access="public" output="false" returntype="array">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var arrQualifiedLoginPolicyMethod = "">
		
		<cfquery name="arrQualifiedLoginPolicyMethod" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">,
				@orgID int, @emailTagTypeID int; 

			SELECT @orgID = s.orgID, @emailTagTypeID = metagt.emailTagTypeID
			FROM dbo.sites as s
			INNER JOIN dbo.ams_memberEmailTagTypes as metagt ON metagt.orgID = s.orgID 
				AND metagt.emailTagType = 'Primary'
			WHERE s.siteID = @siteID;
				
			select 'Email' as methodCode
			from dbo.ams_members m 
			inner join dbo.ams_memberEmails AS me on me.orgID = @orgID
				and m.memberID = me.memberID
				and NULLIF(me.email,'') is not null
			inner join dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID
				and metag.memberID = me.memberID 
				and metag.emailTypeID = me.emailTypeID
				AND metag.emailTagTypeID = @emailTagTypeID
			WHERE m.orgID = @orgID
			and m.memberID = @memberID
			and m.[status] = 'A'

				UNION

			select 'MFASMS' as methodCode
			from dbo.ams_members m
			inner join dbo.ams_memberNetworkProfiles mnp 
				on mnp.siteID = @siteID 
				and m.orgID = @orgID
				and m.memberID = @memberID 
				and m.memberID=mnp.memberID
				and mnp.status = 'A'
				and mnp.MFAPhoneNumber is not null
			inner join dbo.ams_networkProfiles np
				on np.profileID = mnp.profileID
				and np.status = 'A'

			UNION

			select 'MFATOTP' as methodCode
			from dbo.ams_members m
			inner join dbo.ams_memberNetworkProfiles mnp 
				on mnp.siteID = @siteID 
				and m.orgID = @orgID
				and m.memberID = @memberID 
				and m.memberID=mnp.memberID
				and mnp.status = 'A'
				and mnp.MFATOTPIdentity is not null
				and mnp.MFATOTPSid is not null
			inner join dbo.ams_networkProfiles np
				on np.profileID = mnp.profileID
				and np.status = 'A';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn valueArray(arrQualifiedLoginPolicyMethod.methodCode)>
	</cffunction>

	<cffunction name="getLoginPolicyComplianceStatus" access="private" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="LoginPolicy", functionName="Qualify")>

		<cfquery name="local.qryLoginPolicyComplianceStatus" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @complianceStatus varchar(12), @noncompliantMethods varchar(100), @nonmetRequiredMethods varchar(100), @ismetRequired bit;
			
			EXEC dbo.ams_getLoginPolicyComplianceStatus 
				@siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID#">,
				@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">,
				@qualifyFID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.QualifyRFID#">,
				@complianceStatus=@complianceStatus OUTPUT, @noncompliantMethods=@noncompliantMethods OUTPUT, 
				@nonmetRequiredMethods=@nonmetRequiredMethods OUTPUT, @ismetRequired=@ismetRequired OUTPUT;

			SELECT @complianceStatus AS complianceStatus, @noncompliantMethods AS noncompliantMethods, 
				@nonmetRequiredMethods as nonmetRequiredMethods, @ismetRequired as ismetRequired;
		</cfquery>

		<!--- skip password checks for non-compliant users --->
		<cfif local.qryLoginPolicyComplianceStatus.complianceStatus EQ 'noncompliant'>
			<cfset session.mcstruct["ManageLoginSettings"] = true>
		</cfif>

		<cfreturn { "complianceStatus":local.qryLoginPolicyComplianceStatus.complianceStatus, "noncompliantMethods":local.qryLoginPolicyComplianceStatus.noncompliantMethods, "nonmetRequiredMethods":local.qryLoginPolicyComplianceStatus.nonmetRequiredMethods, "ismetRequired":local.qryLoginPolicyComplianceStatus.ismetRequired }>
	</cffunction>

	<cffunction name="processLoginPolicyMethod" access="private" output="false" returntype="string">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="userPassCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="memberStatus" type="string" required="true">
		<cfargument name="verifyMethodCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.retCode = "false">

		<cfset local.siteInfo = application.objSiteInfo.getSiteInfo(siteCode=arguments.siteCode)>

		<cfscript>
			// saved browser?
			try {
				local.strBrowserIdentifierAndAuthCode = getValidBrowserIdentifierAndAuthCode(siteID=local.siteInfo.siteID, memberID=arguments.memberID);
				local.browserIdentifier = local.strBrowserIdentifierAndAuthCode.browserIdentifier;
				local.memberAuthCode = local.strBrowserIdentifierAndAuthCode.memberAuthCode;
				local.arrValidAuthCodes = local.strBrowserIdentifierAndAuthCode.arrValidAuthCodes;
				local.savedBrowser = local.strBrowserIdentifierAndAuthCode.savedBrowser;
				
				// generate new authCode
				if (local.savedBrowser) {
					local.strSaveBrowser = saveMemberNetworkProfileValidBrowser(siteID=local.siteInfo.siteID, siteCode=local.siteInfo.siteCode, 
												memberID=arguments.memberID, browserIdentifier=local.browserIdentifier, existingAuthCode=local.memberAuthCode, 
												verificationMethodCode="Cookie", arrValidAuthCodes=local.arrValidAuthCodes);
					
					// failed to generate new authCode
					if (NOT isValid("guid",local.strSaveBrowser.newAuthCode)) {
						local.savedBrowser = false;
					}
				}
			
			} catch(e) {
				local.savedBrowser = false;
				local.browserIdentifier = "";
				application.objError.sendError(cfcatch=e);
			};
		</cfscript>

		<!--- skip verification for saved browser --->
		<cfif local.savedBrowser>
			<cfset local.retCode = doLoginUser(result=arguments.userPassCode, memberID=arguments.memberID, memberStatus=arguments.memberStatus, 
				loginAsRequestByMemberID=0, browserIdentifier=local.browserIdentifier, verificationMethodCode="Cookie")>
			<cfreturn local.retCode>
		</cfif>

		<cfset local.lastUsedMethodCode = getLastUsedMethodCode(siteID=local.siteInfo.siteID, memberID=arguments.memberID)>
		<cfif local.lastUsedMethodCode IS "">
			<cfset local.lastUsedMethodCode = arguments.verifyMethodCode>
		</cfif>
		<cfset local.retCode = sendVerificationCode(siteCode=arguments.siteCode, userPassCode=arguments.userPassCode, memberID=arguments.memberID, 
			memberStatus=arguments.memberStatus, verifyMethodCode=local.lastUsedMethodCode, browserIdentifier=local.browserIdentifier)>

		<cfreturn local.retCode>
	</cffunction>

	<cffunction name="getValidBrowserIdentifierAndAuthCode" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		
		<cfscript>
			var local = {};
			local.returnStruct = { "browserIdentifier":"", "memberAuthCode":"", "arrValidAuthCodes":[], "savedBrowser":false };
			
			try {
				// validated login cookie
				local.cookieVal = application.objPlatform.readSignedCookie(cookiename="mc_vb");
				
				if (local.cookieVal.isValid) {
					local.strCookie = deserializeJSON(local.cookieVal.verifiedValue);
					local.cookie_browserIdentifier = local.strCookie.keyList();
					
					if (isValid("guid",local.cookie_browserIdentifier)) {		
						//location information of the based on IP of the current Request
						local.locationInfo = { "countryCode":"", "regionCode":"" };

						if (structKeyExists(application.objPlatform,"getClientIPLocationInfo"))
							local.locationInfo = application.objPlatform.getClientIPLocationInfo();

						local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="LoginPolicy", functionName="Qualify");

						var qryValidBrowserIdentifierAndAuthCode = queryExecute("
							SET NOCOUNT ON;
							SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

							DECLARE @validBrowserIdentifier uniqueidentifier, @validAuthCode uniqueidentifier,
								@ISOCountryCode char(2) = NULLIF(:countryCode,''),
								@regionCode varchar(10) = NULLIF(:regionCode,'');

							EXEC dbo.ams_getValidBrowserIdentifierAndAuthCode
								@siteID = :siteID,
								@memberID = :memberID,
								@browserIdentifier = :browserIdentifier,
								@ISOCountryCode = @ISOCountryCode,
								@regionCode = @regionCode,
								@qualifyFID = :qualifyRFID,
								@validBrowserIdentifier = @validBrowserIdentifier OUTPUT,
								@validAuthCode = @validAuthCode OUTPUT;

							SELECT @validBrowserIdentifier AS browserIdentifier, @validAuthCode AS authCode;

							SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
							", 
							{ 
								siteID = { value=arguments.siteID, cfsqltype="CF_SQL_INTEGER" },
								memberID = { value=arguments.memberID, cfsqltype="CF_SQL_INTEGER" },
								browserIdentifier = { value=local.cookie_browserIdentifier, cfsqltype="CF_SQL_IDSTAMP" },
								countryCode = { value=local.locationInfo.countryCode, cfsqltype="CF_SQL_VARCHAR" },
								regionCode = { value=local.locationInfo.regionCode, cfsqltype="CF_SQL_VARCHAR" },
								qualifyRFID = { value=local.QualifyRFID, cfsqltype="CF_SQL_INTEGER" }
							},
							{ datasource="#application.dsn.membercentral.dsn#" }
						);
						
						local.returnStruct.browserIdentifier = qryValidBrowserIdentifierAndAuthCode.browserIdentifier;
						local.returnStruct.memberAuthCode = qryValidBrowserIdentifierAndAuthCode.authCode;

						// valid browserIdentifier
						if (local.returnStruct.browserIdentifier.len()) {
							// get valid auth codes under this browserIdentifier
							local.returnStruct.arrValidAuthCodes = isArray(local.strCookie[local.returnStruct.browserIdentifier]) ? duplicate(local.strCookie[local.returnStruct.browserIdentifier]) : [];
							
							if (arrayLen(local.returnStruct.arrValidAuthCodes) 
								AND (arrayLen(local.returnStruct.arrValidAuthCodes) GT 1 OR local.returnStruct.arrValidAuthCodes[1] NEQ local.returnStruct.memberAuthCode)
							) {
								local.returnStruct.arrValidAuthCodes = getValidAuthCodesForBrowserIdentifier(siteID=arguments.siteID, browserIdentifier=local.returnStruct.browserIdentifier, 
																		arrAuthCodes=local.returnStruct.arrValidAuthCodes);
							}
						}
					}
				}

				// savedBrowser
				if (local.returnStruct.browserIdentifier.len() 
					AND local.returnStruct.memberAuthCode.len() 
					AND arrayFind(local.returnStruct.arrValidAuthCodes,local.returnStruct.memberAuthCode)) {
					local.returnStruct.savedBrowser = true;
				}

			} catch(e) {
				local.returnStruct = { "browserIdentifier":"", "memberAuthCode":"", "arrValidAuthCodes":[], "savedBrowser":false };
				application.objError.sendError(cfcatch=e);
			}

			return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="getValidAuthCodesForBrowserIdentifier" access="private" output="false" returntype="array">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="browserIdentifier" type="string" required="true">
		<cfargument name="arrAuthCodes" type="array" required="false">

		<cfset var local = structNew()>
		<cfset local.arrAuthCodes = []>
		<cfset local.arrValidAuthCodes = []>

		<!--- authCodes passed --->
		<cfif arguments.keyExists("arrAuthCodes")>
			<cfset local.arrAuthCodes = duplicate(arguments.arrAuthCodes)>
		
		<!--- get authCodes from cookie --->
		<cfelse>
			<cfscript>
				try {
					local.cookieVal = application.objPlatform.readSignedCookie(cookiename="mc_vb");
					if (local.cookieVal.isValid) {
						local.strCookie = deserializeJSON(local.cookieVal.verifiedValue);
						local.cookie_browserIdentifier = local.strCookie.keyList();
						
						// valid browserIdentifier
						if (isValid("guid",local.cookie_browserIdentifier) AND local.cookie_browserIdentifier EQ arguments.browserIdentifier AND isArray(local.strCookie[arguments.browserIdentifier])) {
							local.arrAuthCodes = duplicate(local.strCookie[arguments.browserIdentifier]);
						}
					}
				} catch(e) {
					local.arrAuthCodes = [];
					application.objError.sendError(cfcatch=e);
				}
			</cfscript>
		</cfif>

		<!--- return if no auth codes found --->
		<cfif NOT arrayLen(local.arrAuthCodes)>
			<cfreturn local.arrAuthCodes>
		</cfif>

		<cftry>
			<!--- support upto 50 authCodes --->
			<cfif arrayLen(local.arrAuthCodes) GT 50>
				<cfset local.arrAuthCodes = local.arrAuthCodes.slice(1,50)>
			</cfif>

			<cfquery name="local.qryValidAuthCodesForBrowserIdentifier" datasource="#application.dsn.membercentral.dsn#">
				EXEC dbo.ams_getValidAuthCodesForBrowserIdentifier
					@siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					@browserIdentifier = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.browserIdentifier#">,
					@authCodeList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arrayToList(local.arrAuthCodes)#">;
			</cfquery>
			<cfset local.arrValidAuthCodes = QueryColumnData(local.qryValidAuthCodesForBrowserIdentifier,"authCode")>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.arrValidAuthCodes = []>
		</cfcatch>
		</cftry>

		<cfreturn local.arrValidAuthCodes>
	</cffunction>

	<cffunction name="setGAEventUserLoginFlag" access="private" output="false" returntype="void">
		<cfif NOT application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfset session.cfcuser["TriggerGAEventUserLogin"] = 1>
		</cfif>
	</cffunction>

	<cffunction name="sendVerificationCode" access="private" output="false" returntype="string">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="userPassCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="memberStatus" type="string" required="true">
		<cfargument name="verifyMethodCode" type="string" required="true">
		<cfargument name="browserIdentifier" type="string" required="true">
		<cfargument name="mfasms_channel" type="string" required="false" default="sms">

		<cfset var local = structNew()>
		<cfset local.retCode = "false">

		<cfset local.siteInfo = application.objSiteInfo.getSiteInfo(siteCode=arguments.siteCode)>

		<cfswitch expression="#arguments.verifyMethodCode#">
			<cfcase value="MFATOTP">
				<cfscript>
					local.qryMFATOTPSettings = getMFATOTPSettings(siteID=local.siteInfo.siteID, memberID=arguments.memberID);
					local.strVerifyTOTP = {
						"identity": local.qryMFATOTPSettings.MFATOTPIdentity,
						"sid": local.qryMFATOTPSettings.MFATOTPSid,
						"memberID": arguments.memberID,
						"userPassCode": arguments.userPassCode,
						"memberStatus": arguments.memberStatus,
						"browserIdentifier": arguments.browserIdentifier,
						"verificationMethod":arguments.verifyMethodCode
					};

					session.mcstruct["mc_vb_code"] = encrypt(serializeJSON(local.strVerifyTOTP),"M3mberC3ntr@l^_L051N", "CFMX_COMPAT", "Hex");
					local.retCode = "mfa_totp";
				</cfscript>
			</cfcase>
			<cfcase value="MFASMS">
				<cfscript>
					local.MFAPhoneNumber = getMFAPhoneNumber(siteID=local.siteInfo.siteID, memberID=arguments.memberID);
					if (len(local.MFAPhoneNumber)) {
						local.strVerifySMSCode = {
							"phoneNum": local.MFAPhoneNumber,
							"expiresAt": DateAdd("n",10,now()),
							"memberID": arguments.memberID,
							"userPassCode": arguments.userPassCode,
							"memberStatus": arguments.memberStatus,
							"browserIdentifier": arguments.browserIdentifier,
							"verificationMethod":arguments.verifyMethodCode
						};

						local.strSendCode = sendVerificationCode_MFASMS(phoneNumber=local.MFAPhoneNumber, channel=arguments.mfasms_channel);
						local.strVerifySMSCode['secondsBeforeAllowedReattempt'] = local.strSendCode.secondsBeforeAllowedReattempt;
						local.strVerifySMSCode['channel'] = local.strSendCode.channel;
						local.strVerifySMSCode['codeattempts'] = local.strSendCode.codeattempts;
						session.mcstruct["mc_vb_code"] = encrypt(serializeJSON(local.strVerifySMSCode),"M3mberC3ntr@l^_L051N", "CFMX_COMPAT", "Hex");
						local.retCode = "mfa_sms";
					}
				</cfscript>
			</cfcase>
			<cfcase value="Email">
				<cfset local.recipientEmail = application.objMember.getMainEmail(memberID=arguments.memberID).email>
				<cfif len(local.recipientEmail)>
					<cfset local.loginSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Login',siteID=local.siteInfo.siteID)>

					<cfset local.strEmailVerifyCode = {
						"email": local.recipientEmail,
						"emailCode": randRange(100000,999999),
						"expiresAt": DateAdd("n",15,now()),
						"memberID": arguments.memberID,
						"userPassCode": arguments.userPassCode,
						"memberStatus": arguments.memberStatus,
						"browserIdentifier": arguments.browserIdentifier,
						"verificationMethod":arguments.verifyMethodCode
					}>
					
					<cfset local.qryMember = application.objMember.getMemberInfo(memberID=arguments.memberID, orgID=local.siteInfo.orgID)>

					<cfsavecontent variable="local.emailContent">
						<cfoutput>
							Hi #local.qryMember.firstName# #local.qryMember.lastName#! Your verification code is #local.strEmailVerifyCode.emailCode#. <br/><br/><br/>Enter this code on the login screen to verify control of this account. 
						</cfoutput>
					</cfsavecontent>
					
					<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.siteInfo.orgname, email=local.siteInfo.networkEmailFrom },
						emailto=[{ name:'', email:local.recipientEmail }],
						emailreplyto=local.siteInfo.supportProviderEmail,
						emailsubject="#local.qryMember.firstName#,  your #application.objPlatform.getCurrentHostname()# security code is: #local.strEmailVerifyCode.emailCode#",
						emailtitle="Email Verification Code",
						emailhtmlcontent=local.emailContent,
						emailAttachments=[],
						siteID=local.siteInfo.siteID,
						memberID=arguments.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="LOGINVERIFY"),
						sendingSiteResourceID=local.loginSiteResourceID
					)>

					<cfif local.strEmailResult.success>
						<cfset session.mcstruct["mc_vb_code"] = encrypt(serializeJSON(local.strEmailVerifyCode),"M3mberC3ntr@l^_L051N", "CFMX_COMPAT", "Hex")>
						<cfset local.retCode = "mfa_email">
						<!--- send message immediately --->
						<cfif local.strEmailResult.messageID gt 0>
							<cftry>
								<cfhttp method="get" url="#application.paths.backendPlatform.internalUrl#?event=integrations.sendgrid.processmessage&messageID=#local.strEmailResult.messageID#">
							<cfcatch type="Any">
							</cfcatch>
							</cftry>
						</cfif>
					</cfif>
				</cfif>
			</cfcase>
		</cfswitch>

		<cfreturn local.retCode>
	</cffunction>

	<cffunction name="sendVerificationCode_MFASMS" access="private" output="false" returntype="struct">
		<cfargument name="phoneNumber" type="string" required="true">
		<cfargument name="channel" type="string" required="true">
	
		<cfscript>
			var local = structNew();
			local.objTwilioAPI = new model.system.utility.twilioAPI();
			local.SMSPhoneKey = replace('ph#arguments.phoneNumber#','+','','all');

			// twilio rate limits at 5 attempts per 10 mins, we want to additionally add more delay time between attempts
			local.progressiveReattemptDelayByAttempt = [ 30, 40, 60, 90, 120];

			initMFASMSCounter(SMSPhoneKey=local.SMSPhoneKey);

			local.sendCode = false;
			local.strMFASMSInfo = duplicate(session.mcstruct["MFASMSInfo"][local.SMSPhoneKey]);
			local.nextAttemptInFuture = local.strMFASMSInfo.nextAttempt gt now();
			
			// deny if already sent on the same channel
			if (local.nextAttemptInFuture AND local.strMFASMSInfo.prevChannel EQ arguments.channel) {
				return { 
					"secondsBeforeAllowedReattempt": dateDiff("s",now(),session.mcstruct["MFASMSInfo"][local.SMSPhoneKey]['nextAttempt']), 
					"channel": arguments.channel,
					"codeattempts": local.strMFASMSInfo.codeattempts
				};
			}
			
			// switch channel immediately?
			if (local.nextAttemptInFuture AND local.strMFASMSInfo.prevChannel NEQ arguments.channel AND local.strMFASMSInfo.codeattempts EQ 1) {
				// increase the next attempt delay
				local.progressiveReattemptDelayByAttempt[2] = local.progressiveReattemptDelayByAttempt[2] + dateDiff("s",now(),session.mcstruct["MFASMSInfo"][local.SMSPhoneKey]['nextAttempt']);
				local.sendCode = true;
			} 
			// allow
			else if (NOT local.nextAttemptInFuture) {
				local.sendCode = true;
			} 
			// deny
			else {
				local.sendCode = false;
			}
			
			// already sent
			if (NOT local.sendCode) {
				return { 
					"secondsBeforeAllowedReattempt": dateDiff("s",now(),session.mcstruct["MFASMSInfo"][local.SMSPhoneKey]['nextAttempt']), 
					"channel": local.strMFASMSInfo.prevChannel,
					"codeattempts": local.strMFASMSInfo.codeattempts
				};
			}

			// send verification code
			local.strSMSVerification = local.objTwilioAPI.callAPI(method="POST", endpoint="Verifications", 
											payload=[ 
												{ "type":"formfield", "name":"To", "value":arguments.phoneNumber },
												{ "type":"formfield", "name":"Channel", "value":arguments.channel }
											]
										);

			if (isDefined("local.strSMSVerification.strResult.send_code_attempts") and isArray(local.strSMSVerification.strResult.send_code_attempts))
				local.attemptNumber = arrayLen(local.strSMSVerification.strResult.send_code_attempts);
			else
				local.attemptNumber = 1;

			if (local.attemptNumber lte local.progressiveReattemptDelayByAttempt.len())
				// add defined delay for this attempt number
				local.secondsBeforeAllowedReattempt = local.progressiveReattemptDelayByAttempt[local.attemptNumber];
			else 
				// add 10 minutes
				local.secondsBeforeAllowedReattempt = 10 * 60;
			
			session.mcstruct["MFASMSInfo"][local.SMSPhoneKey] = {
				"nextAttempt": dateAdd("s",local.secondsBeforeAllowedReattempt,now()), 
				"codeattempts": local.attemptNumber, 
				"prevChannel": arguments.channel
			};
			
			return { 
				"secondsBeforeAllowedReattempt": local.secondsBeforeAllowedReattempt, 
				"channel": arguments.channel,
				"codeattempts": local.attemptNumber
			}
		</cfscript>
	</cffunction>

	<cffunction name="initMFASMSCounter" access="private" output="false" returntype="void">
		<cfargument name="SMSPhoneKey" type="string" required="true">
		<cfscript>
			if (NOT session.mcstruct.keyExists("MFASMSInfo"))
				session.mcstruct["MFASMSInfo"] = {};
			if (NOT session.mcstruct['MFASMSInfo'].keyExists(arguments.SMSPhoneKey))
				session.mcstruct["MFASMSInfo"][arguments.SMSPhoneKey] = { "nextAttempt":now(), "codeattempts":0, "prevChannel":"" };
		</cfscript>
	</cffunction>

	<cffunction name="getMFASMSNextAttemptInfo" access="private" output="false" returntype="struct">
		<cfargument name="phoneNumber" type="string" required="true">
	
		<cfscript>
			var secondsBeforeAllowedReattempt = 0;
			var SMSPhoneKey = replace('ph#arguments.phoneNumber#','+','','all');

			initMFASMSCounter(SMSPhoneKey=SMSPhoneKey);

			if (session.mcstruct["MFASMSInfo"][SMSPhoneKey].nextAttempt gt now()) {
				secondsBeforeAllowedReattempt = dateDiff("s",now(),session.mcstruct["MFASMSInfo"][SMSPhoneKey].nextAttempt);
			}

			return { "secondsBeforeAllowedReattempt":secondsBeforeAllowedReattempt, "codeattempts":session.mcstruct["MFASMSInfo"][SMSPhoneKey].codeattempts };
		</cfscript>
	</cffunction>

	<cffunction name="resetMFASMSCounter" access="private" output="false" returntype="void">
		<cfargument name="phoneNumber" type="string" required="true">
	
		<cfscript>
			var SMSPhoneKey = replace('ph#arguments.phoneNumber#','+','','all');
			initMFASMSCounter(SMSPhoneKey=SMSPhoneKey);

			if (structKeyExists(session.mcstruct["MFASMSInfo"],SMSPhoneKey))
				session.mcstruct["MFASMSInfo"].delete(SMSPhoneKey);
		</cfscript>
	</cffunction>

	<cffunction name="getSQLUniqueIdentifier" access="private" output="false" returntype="string">
		<cfset var qrySQLUniqueIdentifier = "">
		
		<cfquery name="qrySQLUniqueIdentifier" datasource="#application.dsn.membercentral.dsn#">
			select newUniqueIdentifier=NEWID();
		</cfquery>

		<cfreturn qrySQLUniqueIdentifier.newUniqueIdentifier>
	</cffunction>
	
	<cffunction name="saveMemberNetworkProfileValidBrowser" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="browserIdentifier" type="string" required="true">
		<cfargument name="existingAuthCode" type="string" required="true">
		<cfargument name="verificationMethodCode" type="string" required="true">
		<cfargument name="arrValidAuthCodes" type="array" required="false">
		
		<cfscript>
			var local = {};
			local.returnStruct = { "success":false, "newBrowserIdentifier":"", "newAuthCode":"" };
			
			local.arrAuthCodes = [];
			if (arguments.keyExists("arrValidAuthCodes")) {
				local.arrAuthCodes = duplicate(arguments.arrValidAuthCodes);
			} else if (len(arguments.browserIdentifier)) {
				local.arrAuthCodes = getValidAuthCodesForBrowserIdentifier(siteID=arguments.siteID, browserIdentifier=arguments.browserIdentifier);
			}
		</cfscript>

		<cfquery name="local.qryNewMemberNetworkProfileValidBrowser" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @newBrowserIdentifier uniqueidentifier, @newAuthCode uniqueidentifier;

			EXEC dbo.ams_createMemberNetworkProfileValidBrowser
				@siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">,
				@browserIdentifier = <cfif len(arguments.browserIdentifier)><cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.browserIdentifier#"><cfelse>NULL</cfif>,
				@existingAuthCode = <cfif len(arguments.existingAuthCode)><cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.existingAuthCode#"><cfelse>NULL</cfif>,
				@verificationMethodCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.verificationMethodCode#">,
				@newBrowserIdentifier = @newBrowserIdentifier OUTPUT,
				@newAuthCode = @newAuthCode OUTPUT;

			SELECT @newBrowserIdentifier AS newBrowserIdentifier, @newAuthCode AS newAuthCode;
		</cfquery>

		<cfscript>
			local.returnStruct.newBrowserIdentifier = local.qryNewMemberNetworkProfileValidBrowser.newBrowserIdentifier;
			local.returnStruct.newAuthCode = local.qryNewMemberNetworkProfileValidBrowser.newAuthCode;

			// invalidate existingAuthCode
			if (len(arguments.existingAuthCode) AND arrayLen(local.arrAuthCodes) AND arrayFindNoCase(local.arrAuthCodes,arguments.existingAuthCode)) {
				local.arrAuthCodes = local.arrAuthCodes.deleteNoCase(arguments.existingAuthCode);
			}
			
			// save cookie with new code in the front so that 50 code limit affects oldest generated codes
			local.arrAuthCodes.prepend(local.returnStruct.newAuthCode);
			
			local.strNewCookie = {
				"#local.returnStruct.newBrowserIdentifier#": local.arrAuthCodes
			};

			//allow cross-domain cookie if sitecode=mc for platformwide superuser login support
			local.allowCrossDomainforMCCookie = (arguments.siteCode eq "mc");

			application.objPlatform.setSignedCookie(cookiename="mc_vb", value="#serializeJSON(local.strNewCookie)#", expires="60", allowCrossDomainWhenSecure=local.allowCrossDomainforMCCookie);

			local.returnStruct.success = true;

			return local.returnStruct;
		</cfscript>
	</cffunction>
	
	<cffunction name="resetExistingLoginCookie" access="private" output="false" returntype="string">
		<cfscript>
			var local = {};
			
			// track browser without validating existing or saving any new authcodes
			// reset existing cookie as-is or create new cookie if it didn't exist or was invalid
			local.browserIdentifier = "";
			local.cookieVal = application.objPlatform.readSignedCookie(cookiename="mc_vb");
			if (local.cookieVal.isValid) {
				local.strCookie = deserializeJSON(local.cookieVal.verifiedValue);
				local.browserIdentifier = local.strCookie.keyList();
			} else {
				local.browserIdentifier = getSQLUniqueIdentifier();
				local.strCookie = {
					"#local.browserIdentifier#": []
				};
			}
			application.objPlatform.setSignedCookie(cookiename="mc_vb", value="#serializeJSON(local.strCookie)#", expires="60", allowCrossDomainWhenSecure=false);
			return local.browserIdentifier;
		</cfscript>
	</cffunction>

	<cffunction name="verifySecurityCode" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.isValid = NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) 
								AND session.mcstruct.keyExists("mc_vb_code") 
								AND isSimpleValue(session.mcstruct.mc_vb_code)
								AND len(session.mcstruct.mc_vb_code)>
	
		<!--- no code exists --->
		<cfif NOT local.isValid>
			<cflocation url="/?pg=login" addtoken="no">
		</cfif>

		<cftry>
			<cfset local.strVerifyCode = deserializeJSON(decrypt(session.mcstruct.mc_vb_code,"M3mberC3ntr@l^_L051N", "CFMX_COMPAT", "Hex"))>
			<cfset local.qryLoginPolicyMethod = getQualifiedLoginPolicyMethod(siteID=arguments.event.getValue('mc_siteinfo.siteid'), memberID=local.strVerifyCode.memberID)>
			<cfset local.arrConfiguredMethods = getConfiguredLoginPolicyMethod(siteID=arguments.event.getValue('mc_siteinfo.siteid'), memberID=local.strVerifyCode.memberID)>

			<!--- invalid --->
			<cfif NOT local.arrConfiguredMethods.containsNoCase(local.strVerifyCode.verificationMethod)>
				<cflocation url="/?pg=login" addtoken="no">
			</cfif>

			<cfset local.chooseMFAMethodLink = "?event=cms.showResource&resID=#this.siteResourceID#&logact=chooseMFAMethod&mode=stream">
			<cfset local.hasAlternateMFAMethods = local.strVerifyCode.verificationMethod EQ 'MFASMS'>
			<cfif NOT local.hasAlternateMFAMethods AND arrayLen(local.arrConfiguredMethods) GT 1>
				<cfset var currentMFAMethod = local.strVerifyCode.verificationMethod>
				<cfset local.arrAlternateMFAMethods = local.arrConfiguredMethods.filter((MFAMethod) => arguments.MFAMethod NEQ currentMFAMethod AND listFindNoCase('MFASMS,MFATOTP',arguments.MFAMethod))>
				<cfset local.hasAlternateMFAMethods = local.arrAlternateMFAMethods.len() GT 0>
			</cfif>
			<cfset var choosenMFAMethod = local.strVerifyCode.verificationMethod>
			<cfset local.choosenLoginPolicyMethod = local.qryLoginPolicyMethod.filter((row) => arguments.row.methodCode EQ choosenMFAMethod)>			
			
			<cfswitch expression="#local.strVerifyCode.verificationMethod#">
				<cfcase value="MFATOTP">
					<cfset local.isValid = len(local.strVerifyCode.identity) AND len(local.strVerifyCode.sid)>
					<cfset local.message = "Enter the 6-digit code from the authentication app that you set up.">
					<cfset local.channel = ''>
				</cfcase>
				<cfcase value="MFASMS">
					<cfset local.isValid = local.strVerifyCode.expiresAt GT now()>
					<cfset local.channel = local.strVerifyCode.channel>
					<cfset local.maskedPhoneNumber = '#left(local.strVerifyCode.phoneNum,5)##repeatString("*",len(local.strVerifyCode.phoneNum) - 7)##right(local.strVerifyCode.phoneNum,2)#'>
					<cfswitch expression="#local.strVerifyCode.channel#">
						<cfcase value="sms">
							<cfset local.message = "A security code was just sent to #local.maskedPhoneNumber#. It expires in 10 minutes.">
						</cfcase>
						<cfcase value="call">
							<cfset local.message = "We're calling you now at #local.maskedPhoneNumber#. Please listen and enter the code. It expires in 10 minutes.">
						</cfcase>
						<cfcase value="whatsapp">
							<cfset local.message = "We're messaging you on WhatsApp now at #local.maskedPhoneNumber#. It expires in 10 minutes.">
						</cfcase>
					</cfswitch>
				</cfcase>
				<cfcase value="Email">
					<cfset local.isValid = len(local.strVerifyCode.emailCode) AND local.strVerifyCode.expiresAt GT now()>
					<cfset local.channel = ''>
					<cfset local.maskedEmailAddress = CreateObject("component","model.system.platform.memberFieldsets").getMaskedEmailAddress(email=local.strVerifyCode.email)>
					<cfset local.message = "A security code was just sent to #local.maskedEmailAddress#. It expires in 10 minutes.">
				</cfcase>
				<cfdefaultcase>
					<cfset local.isValid = false>
				</cfdefaultcase>
			</cfswitch>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.isValid = false>
		</cfcatch>
		</cftry>
		
		<!--- invalid/expired code --->
		<cfif NOT local.isValid>
			<cflocation url="/?pg=login" addtoken="no">
		</cfif>

		<cfif cgi.request_method eq "POST" AND arguments.event.valueExists('securityCode') AND isSimpleValue(arguments.event.getTrimValue('securityCode')) AND len(arguments.event.getTrimValue('securityCode'))>
			<cfscript>
				local.securityCode = arguments.event.getTrimValue('securityCode');
				local.securityCodeMatched = false;

				switch (local.strVerifyCode.verificationMethod) {
					case "MFATOTP":
						local.objTwilioAPI = new model.system.utility.twilioAPI();
						local.strTOTPVerificationCheck = local.objTwilioAPI.callAPI(method="POST", endpoint="Entities/#local.strVerifyCode.identity#/Challenges", 
															payload=[ 
																	{ "type":"formfield", "name":"AuthPayload", "value":local.securityCode }, 
																	{ "type":"formfield", "name":"FactorSid", "value":local.strVerifyCode.sid }
															]
														);

						local.securityCodeMatched = local.strTOTPVerificationCheck.statusCode EQ 201 AND local.strTOTPVerificationCheck.strResult.status EQ 'approved';
						break;
					case "MFASMS":
						local.objTwilioAPI = new model.system.utility.twilioAPI();
						local.strSMSVerificationCheck = local.objTwilioAPI.callAPI(method="POST", endpoint="VerificationCheck", 
															payload=[ { "type":"formfield", "name":"To", "value":local.strVerifyCode.phoneNum }, 
																		{ "type":"formfield", "name":"Code", "value":local.securityCode } 
															]
														);

						if (local.strSMSVerificationCheck.statusCode EQ 200 AND local.strSMSVerificationCheck.strResult.status EQ 'approved') {
							local.securityCodeMatched = true;
							resetMFASMSCounter(phoneNumber=local.strVerifyCode.phoneNum);
						}
						break;
					case "Email":
						local.securityCodeMatched = local.securityCode EQ local.strVerifyCode.emailCode;
						break;
				}
				
				// matched
				if (local.securityCodeMatched) {
					structDelete(session.mcStruct,"mc_vb_code");
					
					try {
						// save browser
						if (arguments.event.getValue('saveBrowser',0) EQ 1) {
							local.strSaveBrowser = saveMemberNetworkProfileValidBrowser(siteID=arguments.event.getValue('mc_siteinfo.siteid'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'), 
													memberID=local.strVerifyCode.memberID, browserIdentifier=local.strVerifyCode.browserIdentifier, existingAuthCode="", 
													verificationMethodCode=local.strVerifyCode.verificationMethod);
							local.browserIdentifier = local.strSaveBrowser.newBrowserIdentifier;
						} else {

							local.browserIdentifier = resetExistingLoginCookie();
						}
					} catch(e) {
						application.objError.sendError(cfcatch=e, objectToDump=local);
					};

					local.loginResponse = doLoginUser(result=local.strVerifyCode.userPassCode, memberID=local.strVerifyCode.memberID, memberStatus=local.strVerifyCode.memberStatus, 
												loginAsRequestByMemberID=0, browserIdentifier=local.browserIdentifier, verificationMethodCode=local.strVerifyCode.verificationMethod);
					insertVerificationMethodValidateRequests(siteID=arguments.event.getValue('mc_siteinfo.siteid'), loginPolicyID=local.choosenLoginPolicyMethod.loginPolicyID, memberID=local.strVerifyCode.memberID, loginID=session.cfcuser["loginID"], mode='loginVerify', verificationMethodID=local.choosenLoginPolicyMethod.policyMethodID, success=1);
					local.data = processLoginResponse(event=arguments.event, loginResponse=local.loginResponse);
					
					return local.data;
						
				} else {
					insertVerificationMethodValidateRequests(siteID=arguments.event.getValue('mc_siteinfo.siteid'), loginPolicyID=local.choosenLoginPolicyMethod.loginPolicyID, memberID=local.strVerifyCode.memberID, loginID=0, mode='loginVerify', verificationMethodID=local.choosenLoginPolicyMethod.policyMethodID, success=0);
					arguments.event.setValue('invalidSecurityCode',true);
				}
			</cfscript>
		<cfelse>		
			<cfset insertVerificationMethodCodeRequests(siteID=arguments.event.getValue('mc_siteinfo.siteid'), loginPolicyID=local.choosenLoginPolicyMethod.loginPolicyID, memberID=local.strVerifyCode.memberID, mode='loginVerify', verificationMethodID=local.choosenLoginPolicyMethod.policyMethodID, twilioChannel=local.channel)>		
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="/views/login/#arguments.event.getValue('viewDirectory')#/frm_verifySecurityCode.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="chooseMFAMethod" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "mfamethod":"", "msg":"", "mfasms_channel":"", "reattemptdelay":0, "codeattempts":0 }>
		<cfset local.isValid = NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) 
								AND session.mcstruct.keyExists("mc_vb_code") 
								AND isSimpleValue(session.mcstruct.mc_vb_code)
								AND len(session.mcstruct.mc_vb_code)>
	
		<!--- no code exists --->
		<cfif NOT local.isValid>
			<cfreturn serializeJSON(local.returnStruct)>
		</cfif>
	
		<cftry>
			<cfset local.strVerifyCode = deserializeJSON(decrypt(session.mcstruct.mc_vb_code,"M3mberC3ntr@l^_L051N", "CFMX_COMPAT", "Hex"))>
			<cfset local.qryLoginPolicyMethod = getQualifiedLoginPolicyMethod(siteID=arguments.event.getValue('mc_siteinfo.siteid'), memberID=local.strVerifyCode.memberID)>
			<cfset local.arrConfiguredMethods = getConfiguredLoginPolicyMethod(siteID=arguments.event.getValue('mc_siteinfo.siteid'), memberID=local.strVerifyCode.memberID)>
			
			<cfset local.prevMFAMethod = local.strVerifyCode.verificationMethod>
			<cfset local.chosenMFAMethod = arguments.event.getValue('mfamtd','')>

			<cfif local.prevMFAMethod EQ 'MFASMS'>
				<cfset local.strMFASMSNextAttemptInfo = getMFASMSNextAttemptInfo(phoneNumber=local.strVerifyCode.phoneNum)>
				<cfset local.returnStruct['reattemptdelay'] = local.strMFASMSNextAttemptInfo.secondsBeforeAllowedReattempt>
				<cfset local.returnStruct['codeattempts'] = local.strMFASMSNextAttemptInfo.codeattempts>
			</cfif>

			<cfif isSimpleValue(local.chosenMFAMethod) AND listFindNoCase('MFATOTP,MFASMS',local.chosenMFAMethod) AND local.arrConfiguredMethods.containsNoCase(local.chosenMFAMethod)>
				<cfset local.mfasms_channel = 'sms'>
				<cfif local.chosenMFAMethod EQ 'MFASMS'
					AND arguments.event.valueExists('mfasms_channel') 
					AND isSimpleValue(arguments.event.getValue('mfasms_channel'))
					AND listFindNoCase('sms,call,whatsapp',arguments.event.getValue('mfasms_channel'))>
					<cfset local.mfasms_channel = arguments.event.getValue('mfasms_channel')>
				</cfif>
				
				<cfset local.result = sendVerificationCode(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), userPassCode=local.strVerifyCode.userPassCode, 
										memberID=local.strVerifyCode.memberID, memberStatus=local.strVerifyCode.memberStatus, verifyMethodCode=local.chosenMFAMethod, 
										browserIdentifier=local.strVerifyCode.browserIdentifier, mfasms_channel=local.mfasms_channel)>
				<cfset local.strVerifyCode = deserializeJSON(decrypt(session.mcstruct.mc_vb_code,"M3mberC3ntr@l^_L051N", "CFMX_COMPAT", "Hex"))>
			</cfif>

			<cfset local.returnStruct['mfamethod'] = lCase(local.strVerifyCode.verificationMethod)>
			
			<cfswitch expression="#local.strVerifyCode.verificationMethod#">
				<cfcase value="MFATOTP">
					<cfset local.returnStruct['success'] = len(local.strVerifyCode.identity) AND len(local.strVerifyCode.sid)>
					<cfset local.returnStruct['msg'] = "Enter the 6-digit code from the authentication app that you set up.">
				</cfcase>
				<cfcase value="MFASMS">
					<cfset local.returnStruct['success'] = local.strVerifyCode.expiresAt GT now()>
					<cfset local.returnStruct['mfasms_channel'] = lCase(local.strVerifyCode.channel)>
					<cfset local.returnStruct['reattemptdelay'] = local.strVerifyCode.secondsBeforeAllowedReattempt>
					<cfset local.returnStruct['codeattempts'] = local.strVerifyCode.codeattempts>
					<cfset local.maskedPhoneNumber = '#left(local.strVerifyCode.phoneNum,5)##repeatString("*",len(local.strVerifyCode.phoneNum) - 7)##right(local.strVerifyCode.phoneNum,2)#'>
					<cfswitch expression="#local.strVerifyCode.channel#">
						<cfcase value="sms">
							<cfset local.returnStruct['msg'] = "A security code was just sent to #local.maskedPhoneNumber#. It expires in 10 minutes.">
						</cfcase>
						<cfcase value="call">
							<cfset local.returnStruct['msg'] = "We're calling you now at #local.maskedPhoneNumber#. Please listen and enter the code. It expires in 10 minutes.">
						</cfcase>
						<cfcase value="whatsapp">
							<cfset local.returnStruct['msg'] = "We're messaging you on WhatsApp now at #local.maskedPhoneNumber#. It expires in 10 minutes.">
						</cfcase>

					</cfswitch>
				</cfcase>
			</cfswitch>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct["success"] = false>
		</cfcatch>
		</cftry>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="showRemoteLoginFormResponsive" access="private" output="false" returntype="string">
		<cfargument name="remoteLoginFormURL" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfif application.objPlatform.isRequestSecure()>
				<cfset local.returnURL = "https://"/>
			<cfelse>
				<cfset local.returnURL = "http://"/>
			</cfif>
			<cfif len(session.mcstruct.landingpage)>
				<cfset local.returnURL = local.returnURL & cgi.SERVER_NAME & session.mcstruct.landingpage>
			<cfelse>
				<cfset local.returnURL = local.returnURL & cgi.SERVER_NAME>
			</cfif>
			<cfset local.returnURL = URLEncodedFormat(local.returnURL)>
			<cfset local.loginURL = "#arguments.remoteLoginFormURL#returnURL=#local.returnURL#">

			<cfsavecontent variable="local.LoginHeadText">
				<cfoutput>
				<script language="javascript">
					setTimeout(() => { 
						var #toScript(local.loginURL, "newloc")#;
						document.location.href = newloc;
					},1000);
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.LoginHeadText#">

			<cfoutput>
			<div id="mc_loginApp">
				<div class="container-fluid">
					<div class="row-fluid">
						<div class="span12">
							<h3>Site Login</h3>
						</div>
					</div>
					<div class="row-fluid">
						<div class="span12">
							Hang on.. we are redirecting you to login.
						</div>
					</div>
				</div>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="showRemoteLoginForm" access="private" output="false" returntype="string">
		<cfargument name="remoteLoginFormURL" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfif application.objPlatform.isRequestSecure()>
				<cfset local.returnURL = "https://"/>
			<cfelse>
				<cfset local.returnURL = "http://"/>
			</cfif>
			<cfif len(session.mcstruct.landingpage)>
				<cfset local.returnURL = local.returnURL & cgi.SERVER_NAME & session.mcstruct.landingpage>
			<cfelse>
				<cfset local.returnURL = local.returnURL & cgi.SERVER_NAME>
			</cfif>
			<cfset local.returnURL = URLEncodedFormat(local.returnURL)>
			<cfset local.loginURL = "#arguments.remoteLoginFormURL#returnURL=#local.returnURL#">

			<cfsavecontent variable="local.LoginHeadText">
				<cfoutput>
				<script language="javascript">
					setTimeout(() => { 
						var #toScript(local.loginURL, "newloc")#;
						document.location.href = newloc;
					},1000);
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.LoginHeadText#">

			<cfoutput>
			<table width="65%" border="0" cellspacing="0" cellpadding="8" align="center" bgcolor="##cccccc">
			<tr>
				<td class="loginPageTitle">Site Login</td>
			</tr>
			<tr>
				<td class="loginPage" valign="top">
					Hang on.. we are redirecting you to login.
				</td>
			</tr>
			</table>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="showMainLoginFormResponsive" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="mc_loginApp">
				<div class="container-fluid">
					<div class="row-fluid">
						<div class="span12">
							<h3>Site Login</h3>
						</div>
					</div>
					<div class="row-fluid">
						<div class="span4">
							<cfif arguments.event.getValue('showErrMessage',0) gt 0>
								<div id="loginErrorDIV" class="alert alert-error">
									<cfif arguments.event.getValue('showErrMessage') is 1>
										Login failed. Try again.
									</cfif>
									<div id="cookies-disabled" class="tsAppBodyText" style="display:none">Your computer is not accepting cookies from our site. You must accept cookies in order to login.</div>
								</div>		
							</cfif>					
							<form name="frmLogin" id="frmLogin" method="post" action="/?pg=login" class="mc_form_login">
								#application.objUser.renderSecurityKeyElement()#
								<div style="padding-bottom:8px;">
									<b>Username:</b><br/>
									<input class="tsAppBodyText" type="text" name="username" id="username" size="25" value="#session.cfcuser.memberdata.username#">
								</div>
								<div style="padding-bottom:10px;">
									<b>Password:</b><br>
									<input class="tsAppBodyText" type="password" name="password" id="password" size="25" value="">
								</div>
								<button class="btn" type="submit" name="btnLogin">LOGIN</button>
							</form>
							<a href="/?pg=login&logact=requestReset">I forgot my username or password</a>	
							<br/><br/>				  
						</div>
						<div class="span8">
							<div id="loginSponsorContainer" style="max-width:180px;"></div>
						</div>
					</div>
				</div>

				<div id="cookies-instructions" class="err-popup" style="display:none"></div>
			</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="showMainLoginForm" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<table id="tblSiteLogin" width="80%" border="0" cellspacing="0" cellpadding="8" align="center" bgcolor="##cccccc">
				<tr>
					<td colspan="2" class="tdSiteLoginLabel tsAppHeading" style="background-color:##014d7e;color:##fff;font-weight:bold;">Site Login</td>
				</tr>
				<tr valign="top">
					<td class="tdSiteLoginFormL tsAppBodyText" width="300">
						<cfif arguments.event.getValue('showErrMessage',0) gt 0>
								<cfif arguments.event.getValue('showErrMessage') is 1>
								<div id="loginErrorDIV" class="loginError" style="margin-bottom:10px;">
									<strong>Login failed. Try again.</strong>
								</div>
							</cfif>
						</cfif>
						<form name="frmLogin" id="frmLogin" method="post" action="/?pg=login" class="mc_form_login">
							#application.objUser.renderSecurityKeyElement()#
							<div style="padding-bottom:8px;">
								<b>Username:</b><br/>
								<input class="tsAppBodyText" type="text" name="username" id="username" size="25" value="#session.cfcuser.memberdata.username#">
							</div>
							<div style="padding-bottom:10px;">
								<b>Password:</b><br>
								<input class="tsAppBodyText" type="password" name="password" id="password" size="25" value="">
							</div>
							<div style="padding-bottom:10px;">
								<a style="font-weight:bold;" href="/?pg=login&logact=requestReset">I forgot my username or password</a>
							</div>
							<button class="tsAppBodyButton" type="submit" name="btnLogin">LOGIN</button>
						</form>
					</td>
					<td class="tdSiteLoginFormR tsAppBodyText">
						<div id="loginSponsorContainer" style="max-width:180px;margin-left:auto;margin-right:auto;"></div>
					</td>
				</tr>
				<tr>
					<td colspan="3" class="tdSiteLoginFooter">
						<div id="cookies-disabled3" class="loginError" style="display:none"><strong>Your computer is not accepting cookies from our site. You must accept cookies in order to login.</strong></div>
					</td>
				</tr>
			</table>
			<div id="cookies-instructions" class="err-popup" style="display:none"></div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="registerLoginFormAdSpace" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cfsavecontent variable="local.sponsorAdJS">
			<cfoutput>
				<script type="text/javascript">
					$(document).ready(function() {
						MCPromises.BackendPlatformServices.then(function() {
							var MCadlimit = 1;
							var MCadContainer = $("##loginSponsorContainer");
							var MCadSitecode = '#arguments.event.getValue('mc_siteinfo.sitecode')#';
							var MCadZoneType = 'Login Page';
		
							MCBackendPlatformServices.SponsorAdsService.MCIncludeAds(MCadContainer, MCadZoneType,MCadSitecode,MCadlimit,'_blank',null);
						}).catch(error => {
							let msg = 'Failed to get ads for loginSponsorContainer';
							if (MCJSErrorReporting && MCJSErrorReporting.promiseRejectionHandler)
								MCJSErrorReporting.promiseRejectionHandler(msg)
							else 
								console.error(`MCJSErrorReporting.promiseRejectionHandler not defined, falling back to console message: ${msg}`);


						});
					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.sponsorAdJS#">
	</cffunction>
	
	<cffunction name="getMainLoginForm" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		
		<!--- use custom login form if defined --->
		<cfif application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true">
			<cftry>
				<cfif fileExists("#application.paths.localSiteComponentsRoot.path##arguments.event.getValue('mc_siteinfo.orgcode')#/#arguments.event.getValue('mc_siteinfo.sitecode')#/custom/Login.cfc")>
					<cfset local.objCustomLogin = CreateObject("component","sitecomponents.#arguments.event.getValue('mc_siteinfo.orgcode')#.#arguments.event.getValue('mc_siteinfo.sitecode')#.custom.Login")>
					<cfif structKeyExists(local.objCustomLogin,"showLoginFormResponsive")>
						<cfset local.data = local.objCustomLogin.showLoginFormResponsive(event=arguments.event)>
					<cfelseif structKeyExists(local.objCustomLogin,"showLoginForm")>
						<cfset local.data = local.objCustomLogin.showLoginForm(event=arguments.event)>
					<cfelseif arguments.event.getValue('mc_siteinfo.useRemoteLogin') is 1 and arguments.event.getValue('mc_siteinfo.useRemoteLoginForm') is 1>
						<cfset local.data = showRemoteLoginFormResponsive(remoteLoginFormURL=arguments.event.getValue('mc_siteinfo.remoteLoginFormURL'))>
					<cfelse>
						<cfset local.data = showMainLoginFormResponsive(arguments.event)>
					</cfif>
				<cfelseif arguments.event.getValue('mc_siteinfo.useRemoteLogin') is 1 and arguments.event.getValue('mc_siteinfo.useRemoteLoginForm') is 1>
					<cfset local.data = showRemoteLoginFormResponsive(remoteLoginFormURL=arguments.event.getValue('mc_siteinfo.remoteLoginFormURL'))>
				<cfelse>
					<cfset local.data = showMainLoginFormResponsive(arguments.event)>
				</cfif>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.data = showMainLoginFormResponsive(arguments.event)>
			</cfcatch>
			</cftry>
		<cfelse>
			<cftry>
				<cfif fileExists("#application.paths.localSiteComponentsRoot.path##arguments.event.getValue('mc_siteinfo.orgcode')#/#arguments.event.getValue('mc_siteinfo.sitecode')#/custom/Login.cfc")>
					<cfset local.objCustomLogin = CreateObject("component","sitecomponents.#arguments.event.getValue('mc_siteinfo.orgcode')#.#arguments.event.getValue('mc_siteinfo.sitecode')#.custom.Login")>
					<cfif structKeyExists(local.objCustomLogin,"showLoginForm")>
						<cfset local.data = local.objCustomLogin.showLoginForm(event=arguments.event)>
					<cfelseif arguments.event.getValue('mc_siteinfo.useRemoteLogin') is 1 and arguments.event.getValue('mc_siteinfo.useRemoteLoginForm') is 1>
						<cfset local.data = showRemoteLoginForm(remoteLoginFormURL=arguments.event.getValue('mc_siteinfo.remoteLoginFormURL'))>
					<cfelse>
						<cfset local.data = showMainLoginForm(arguments.event)>
					</cfif>
				<cfelseif arguments.event.getValue('mc_siteinfo.useRemoteLogin') is 1 and arguments.event.getValue('mc_siteinfo.useRemoteLoginForm') is 1>
					<cfset local.data = showRemoteLoginForm(remoteLoginFormURL=arguments.event.getValue('mc_siteinfo.remoteLoginFormURL'))>
				<cfelse>
					<cfset local.data = showMainLoginForm(arguments.event)>
				</cfif>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.data = showMainLoginForm(arguments.event)>
			</cfcatch>
			</cftry>
		</cfif>

		<cfsavecontent variable="local.loginJS">
			<cfoutput>
				<script type="text/javascript" src="/assets/common/javascript/cookies.js" ></script>
				<script type="text/javascript">
					function cookiecheckinit() {
						if(testSessionCookie()) {
							try {
								document.getElementById("cookies-disabled").style.display = "none";
							} catch (e){}
							try {
								document.getElementById("cookies-instructions").style.display = "none";
							} catch (e){}
						}
						else {
							try {
								document.getElementById("cookies-disabled").style.display = "block";
							} catch (e){}
							try {
								document.getElementById("cookies-instructions").style.display = "block";
							} catch (e){}
						}
					}
						
					function addLoadEvent(func) {
						 var oldonload = window.onload;
						 if (typeof window.onload != 'function') {
						 	window.onload = func;
						 } else {
						 	window.onload = function() {
								if (oldonload) oldonload();
								func();
							}
						}
					}
					addLoadEvent(cookiecheckinit);
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#application.objCommon.minText(local.loginJS)#">

		<cfset registerLoginFormAdSpace(arguments.event)>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getGuestAccountForm" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		
		<cfset local.objLocator = CreateObject("component","model.system.user.accountLocater")>
		<cfset arguments.Event.getCollection()['mc_pageDefinition']['pageTitle'] = "Create Account">
		
		<cfif arguments.event.getValue('mc_siteinfo.allowGuestAccounts') is 1>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<cftry>
					<cfinclude template="/sitecomponents/#arguments.event.getValue('mc_siteinfo.orgcode')#/#arguments.event.getValue('mc_siteinfo.sitecode')#/custom/GuestAccountForm.cfm">
					
					<cfcatch type="any">
						<!--- get fieldset for newacct form --->
						<cfset local.fieldsetID = local.objLocator.getLocatorFieldsetID(siteid=arguments.event.getValue('mc_siteinfo.siteid'), area='newacct')>
						<cfset local.alternateGuestLink = arguments.event.getValue('mc_siteinfo.alternateGuestAccountCreationLink','')>
						
						<cfif application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true">
							<div class="row-fluid">
								<div class="span12 well well-small">
									<fieldset>
										<legend><strong>Create Account</strong></legend>
										<div class="tsAppBodyText">
											<cfif len(local.alternateGuestLink) GT 0>
												<cfoutput>
													Click <a href="#local.alternateGuestLink#">here</a> to enter information to create a guest account.
												</cfoutput>
											<cfelse>
												#local.objLocator.displayLocatorNewAccount(fieldsetID=local.fieldsetID, postURL='/?#cgi.QUERY_STRING#', buttonText="Continue")#
											</cfif>
										</div>
									</fieldset>
								</div>
							</div>
						<cfelse>
							<table width="65%" border="0" cellspacing="0" cellpadding="8" align="center" bgcolor="##cccccc">
								<tr>
									<td colspan="2" style="background-color:##014d7e;color:##fff;font-weight:bold;">Create Account</td>
								</tr>
								<tr>
									<td class="tsAppBodyText" valign="top">
										<div style="line-height:1.5em;">
											<cfif len(local.alternateGuestLink) GT 0>
												<cfoutput>
													Click <a href="#local.alternateGuestLink#">here</a> to enter information to create a guest account.
												</cfoutput>
											<cfelse>
												#local.objLocator.displayLocatorNewAccount(fieldsetID=local.fieldsetID, postURL='/?#cgi.QUERY_STRING#', buttonText="Continue")#
											</cfif>
										</div>
									</td>
								</tr>
							</table>
						</cfif>
						<style type="text/css">
							.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
						</style>
						<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
						<!--- because this is not in a cfdiv (cant be because of the login redirects) override function to submit form --->
						<script language="javascript">
							doALNA = function() {
								if (validateALNA()) frmLocatorNA.submit();
							};
						</script>
					</cfcatch>
				</cftry>
				</cfoutput>	
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "">
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="sendToDestination" access="private" returnType="void">
		<cfset var destination = getDestination()>
		<cflocation url="#destination#" addtoken="false">
	</cffunction>

	<cffunction name="getDestination" access="private" returnType="string" output="no">
		<cfset var local = structNew()>

		<cfif isdefined("session.remoteReturnURL") and len(session.remoteReturnURL)>
			<cfset local.statsSessionID = session.cfcuser.statssessionid>
			<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcuser.memberdata.identifiedAsMemberID gt 0>
				<cfset local.memberid = session.cfcuser.memberdata.identifiedAsMemberID>
			<cfelse>
				<cfset local.memberid = session.cfcuser.memberdata.memberid>
			</cfif>

			<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) 
				AND len(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).userTokenSecret)
				AND (NOT application.objUser.isSuperUser(cfcuser=session.cfcuser) OR session.mcStruct.sitecode EQ 'MC')>
				<cftry>
					<cfset local.strAPIResponse = application.objMCAPI.api(siteCode=session.mcStruct.sitecode, method="POST", endpoint="member/#session.cfcuser.memberdata.memberNumber#/sso/mcusertokenjwt")>
					<cfif not local.strAPIResponse.error>
						<cfset local.mcusertoken = local.strAPIResponse.data.mcusertokenjwt>
					<cfelse>
						<cfthrow message="#arrayToList(local.strAPIResponse.messages)#">
					</cfif>
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					<cfset local.mcusertoken = "">
				</cfcatch>
				</cftry>
				<cfset local.mcusertoken = "&mcusertoken=#local.mcusertoken#">
			<cfelse>
				<cfset local.mcusertoken = "">
			</cfif>

			<cfif find("?",session.remoteReturnURL)>
				<cfset local.separator = "&">
			<cfelse>
				<cfset local.separator = "?">
			</cfif>
			<cfset local.returnURL = session.remoteReturnURL & local.separator & "mid=#local.memberid#&stID=#local.statsSessionID##local.mcusertoken#">

			<cfset structDelete(session,"remoteReturnURL",false)>
		<cfelseif session.mcstruct.keyExists("MCStaffLoginHostInfo") 
				AND application.objUser.isSuperUser(cfcuser=session.cfcuser)
				AND session.mcStruct.sitecode EQ 'MC'>
			<cfset local.returnURL = createObject("component","model.admin.members.memberAdmin").generateLoginAsMemberLink(
										memberID=session.cfcuser.memberdata.memberID,
										username=session.cfcuser.memberdata.username,
										sitecode=session.mcstruct.MCStaffLoginHostInfo.sitecode,
										hostname=session.mcstruct.MCStaffLoginHostInfo.callingHostname,
										returnURL=session.mcstruct.MCStaffLoginHostInfo.returnURL
									).loginLink>
			<cfset structDelete(session.mcstruct,"MCStaffLoginHostInfo",false)>
		<cfelseif len(session.mcstruct.landingpage)>
			<cfset local.returnURL = Session.mcstruct.LandingPage>
		<cfelse>
			<cfset local.returnURL = "/">
		</cfif>

		<cfreturn local.returnURL>
	</cffunction>

	<cffunction name="getMemberData" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="rprID" type="numeric" required="true">
		<cfargument name="statsSessionID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>		

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
			SELECT m.memberID, np.depomemberdataid, m.firstname, m.lastname, me.email, rpr.dateEntered, rpr.hasBeenUsed, rpr.rprID, 'profile' as MemberLoginType
			FROM dbo.ams_networkProfiles AS np
			INNER JOIN dbo.networkSites AS ns ON np.networkID = ns.networkID
				AND ns.isLoginNetwork = 1
				AND ns.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			INNER JOIN dbo.ams_memberNetworkProfiles AS mnp ON np.profileID = mnp.profileID
				AND mnp.status = 'A'
				AND mnp.siteID = ns.siteID
			INNER JOIN dbo.ams_members AS m ON mnp.memberID = m.memberID
				AND m.status = 'A'
				AND m.memberID = m.activeMemberID
				<cfif arguments.memberID NEQ 0>
					AND m.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
				</cfif>
			INNER JOIN dbo.ams_memberEmails AS me ON me.orgID = m.orgID and m.memberID = me.memberID
			INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = m.orgID 
				and metag.memberID = me.memberID 
				AND metag.emailTypeID = me.emailTypeID
			INNER JOIN dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = m.orgID
				and metagt.emailTagTypeID = metag.emailTagTypeID 
				AND metagt.emailTagType = 'Primary'
			INNER JOIN dbo.ams_resetPasswordRequest rpr ON me.email = rpr.emailAddress
				AND rpr.statsSessionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.statsSessionID#">
				AND rpr.rprID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rprID#">
				AND rpr.hasBeenUsed = 0
				AND rpr.dateEntered BETWEEN case when rpr.expire = 1 then dateadd(d,-1,getdate()) else '1/1/2009' end AND getdate()
			
				UNION
			
			SELECT msd.memberID, null as depomemberdataid, m.firstname, m.lastname, me.email, rpr.dateEntered, rpr.hasBeenUsed, rpr.rprID, 'default' as MemberLoginType
			FROM dbo.ams_memberSiteDefaults AS msd
			INNER JOIN dbo.sites as s on s.siteID = msd.siteID
			INNER JOIN dbo.ams_memberEmails AS me ON me.orgID = s.orgID and msd.memberID = me.memberID
			INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = s.orgID 
				and metag.memberID = me.memberID 
				AND metag.emailTypeID = me.emailTypeID
			INNER JOIN dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = s.orgID 
				and metagt.emailTagTypeID = metag.emailTagTypeID 
				AND metagt.emailTagType = 'Primary'
			INNER JOIN dbo.ams_members AS m ON msd.memberID = m.memberID
				AND m.status = 'A'
				AND msd.status = 'A'
				AND m.memberID = m.activeMemberID
				<cfif arguments.memberID NEQ 0>
					AND m.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
				</cfif>
			left outer JOIN dbo.ams_memberNetworkProfiles AS mnp 
				inner join dbo.ams_networkProfiles np
					ON np.profileID = mnp.profileID
					and np.status = 'A'
			on mnp.memberID = m.memberID
			and mnp.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			INNER JOIN dbo.ams_resetPasswordRequest rpr ON me.email = rpr.emailAddress
				AND rpr.statsSessionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.statsSessionID#">
				AND rpr.rprID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rprID#">
				AND rpr.hasBeenUsed = 0
				AND rpr.dateEntered BETWEEN case when rpr.expire = 1 then dateadd(d,-1,getdate()) else '1/1/2009' end AND getdate()
			where msd.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			and mnp.memberID is null
		</cfquery>

		<cfreturn local.data>
	</cffunction>



	<!--- password reset functions --->
	<cffunction name="getResetRequestForm" access="private" output="false" returntype="string">
		<cfset var local = structNew()>		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfform method="post" action="/?#cgi.QUERY_STRING#">		
				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<cfinput type="hidden" name="resetMemberID"  id="resetMemberID" value="#session.cfcuser.memberdata.memberid#">
				</cfif>
					<div id="mc_loginApp">
					<table border="0" cellspacing="0" cellpadding="8" align="center" bgcolor="##cccccc">
					<tr>
						<td class="tsAppHeading" style="background-color:##014d7e;color:##fff;font-weight:bold;">Reset Password Request</td>
					</tr>
					<tr>
						<td class="tsAppBodyText">
							Enter your e-mail address below.<br /><br />  
							If your e-mail address appears in our membership records, we'll send instructions to reset your password.<br /> 
							Please check your SPAM folder if you do not receive the e-mail instructions within a few minutes.
							<br/><br/>
							<b>E-mail Address:</b>
							<cfinput class="tsAppBodyText" type="text" name="email"  id="email" required="yes" message="Enter a valid e-mail address" size="35" validate="regular_expression" pattern="#application.regEx.email#" value="#session.cfcuser.memberdata.email#">
							<button class="tsAppBodyButton" type="submit" name="btnSubmit">Continue</button>
						</td>
					</tr>							
					</table>				
					</div>
				</cfform>
			</cfoutput>	
		</cfsavecontent>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getResetPasswordForm" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.data = ''>

		<!--- decrypt and deserialize id from url --->
		<cfset local.id = arguments.event.getTrimValue('id','')>
		<cfset local.requestInfo = ''>
		<cfset local.decryptString = ''>
		<cftry>
			<cfif len(local.id)>
				<cfset local.decryptString = decrypt(local.id,"TRiaL_SMiTH", "CFMX_COMPAT", "Hex")>
				<cfset local.requestInfo = deserializeJSON(local.decryptString)>

				<cfif isStruct(local.requestInfo)>
					<cfset local.memberData = getMemberData(siteID=arguments.event.getValue('mc_siteInfo.siteID'), rprID=local.requestInfo.r,
						statsSessionID=local.requestInfo.s, memberID=arguments.event.getValue('memberID',local.requestInfo.m))>
					<cfset local.statusCode = local.memberData.recordCount>
					
					<!--- if password reset is for default login, set flag to mark as used later and login, which triggers affiliation process --->
					<!--- tl 7/2010: changed from calling updateHasBeenUsed to setting flag so mail virus scanners wont cancel the link --->
					<cfif local.statusCode is 1 and local.memberData.memberLoginType eq "default">
						<cfset session.mcStruct.resetPwdRPRID = local.memberData.email>

						<cfset local.username = application.objUser.login_getUsername(memberID=local.memberData.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteID'))>

						<cfset local.loginResponse = loginuser(username=local.username, password='', sitecode=arguments.event.getValue('mc_siteinfo.sitecode'), loginAsMemberID=local.memberData.memberID, loginAsRequestByMemberID=local.memberData.memberID)>

						<cfreturn processLoginResponse(event=arguments.event, loginResponse=local.loginResponse)>
					</cfif>
				<cfelse>
					<cfthrow>
				</cfif>
			<cfelse>
				<cfthrow>
			</cfif>
		<cfcatch type="any">
			<cfset local.statusCode = 0>
		</cfcatch>
		</cftry>

		<cfswitch expression="#local.statusCode#">
			<cfcase value="0">
				<cfset local.strLoginOrgIdentityInfo = application.objOrgInfo.getOrgIdentity(orgIdentityID=arguments.event.getValue('mc_siteInfo.loginOrgIdentityID'))>
				<cfsavecontent variable="local.data">
					<cfoutput>
					<div id="mc_loginApp">
					<table border="0" cellspacing="0" cellpadding="8" align="center" bgcolor="##cccccc">
					<tr>
						<td class="tsAppHeading" style="background-color:##014d7e;color:##fff;font-weight:bold;">Reset Password</td>
					</tr>
					<tr>
						<td class="tsAppBodyText">
							The link you accessed is not valid or is no longer available.<br/>
							For your security, password reset links are only good for 24 hours and may only be used once.<br/><br/>
							Request a <a href="/?pg=login&logact=requestReset">new password reset</a> or contact us at #local.strLoginOrgIdentityInfo.phone# for assistance.
						</td>
					</tr>
					</table>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>
			
			<cfcase value="1">
				<cfsavecontent variable="local.LoginJS">
					<cfoutput>
					<style type="text/css">
						.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
					</style>
					<script language="javascript">
						function validateResetForm(){
							var runSubmit = true;
							if( !MCcheckPassword() ) runSubmit = false;
							if( runSubmit ){ return true; }
							else{ alert("Passwords do not match. Try again."); return false; }						
						}
						function MCcheckPassword(){
							var pass = document.frmResetPassword.password.value;
							var passCon	= document.frmResetPassword.confirmPassword.value;
							if( pass == '' && passCon == ''){ return true; }
							else{
								if( pass == passCon ){ return true; }
								else{ return false; }
							}
						}
						function changeUN() {
							$('##UN_ro').hide();
							$('##UN_ed').show();
						}
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfhtmlhead text="#application.objCommon.minText(local.LoginJS)#">
				
				<cfset local.username = application.objUser.login_getUsername(memberID=local.memberData.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteID'))>

				<cfsavecontent variable="local.data">
					<cfoutput>
					<cfform method="post" action="/?pg=login&logact=resetPassword" name="frmResetPassword"  id="frmResetPassword" onsubmit="return validateResetForm()">
					<cfinput type="Hidden" name="id" id="id" value="#local.id#">
					<cfinput type="Hidden" name="memberID" id="memberID" value="#local.memberData.memberID#">
					<div id="mc_loginApp">
					<table border="0" cellspacing="0" cellpadding="8" align="center" bgcolor="##cccccc">
					<tr>
						<td class="tsAppHeading" style="background-color:##014d7e;color:##fff;font-weight:bold;">Reset Password</td>
					</tr>
					<tr>
						<td class="tsAppBodyText">
							Choose a new password for your account.
							
							<cfif arguments.event.getValue('err',0) is 3>
								<div class="alert" style="margin-top:8px;">
									You have entered an invalid username.<br/>
									That username is already in use. 
								</div>
							</cfif>
						</td>
					</tr>
					<tr>
						<td>
							<table border="0" cellspacing="0" cellpadding="8">
							<tr valign="top">
								<td class="tsAppBodyText"><b>Username:</b></td>
								<td class="tsAppBodyText">
									<div id="UN_ro">
										<b>#local.username#</b><br/>
										<div style="margin-top:4px;"><a href="javascript:changeUN();">(Click to change your username.)</a></div>
									</div>
									<div id="UN_ed" style="display:none;">
										<cfinput class="tsAppBodyText" autocomplete="off" type="text" name="mc_username" id="mc_username" required="yes" message="Enter your new username" size="25" maxlength="75" value="#local.username#">
									</div>
								</td>
							</tr>
							<tr>
								<td class="tsAppBodyText"><b>New Password:</b></td>
								<td><cfinput class="tsAppBodyText" autocomplete="off" type="password" name="password" id="password" required="yes" message="Enter a Password" size="25" maxlength="100" value=""></td>
							</tr>
							<tr>
								<td class="tsAppBodyText"><b>Confirm Password:</b></td>
								<td><cfinput class="tsAppBodyText" autocomplete="off" type="password" name="confirmPassword" id="confirmPassword" required="yes" message="Confirm Password" size="25" maxlength="100" value=""></td>
							</tr>
							<tr>
								<td></td>
								<td><button class="tsAppBodyButton" type="submit" name="btnSubmit">Continue</button></td>
							</tr>										
							</table>
						</td>
					</tr>		
					<tr>
						<td class="tsAppBodyText">Important: Make note of your new password.</td>
					</tr>
					</table>
					</div>
					</cfform>
					</cfoutput>	
				</cfsavecontent>
			</cfcase>
			
			<cfdefaultcase>
				<cfsavecontent variable="local.data">
					<cfoutput>
					<div id="mc_loginApp">
					<table border="0" cellspacing="0" cellpadding="8" align="center" bgcolor="##cccccc">
					<tr>
						<td class="tsAppHeading" style="background-color:##014d7e;color:##fff;font-weight:bold;">Reset Password</td>
					</tr>
					<tr>
						<td class="tsAppBodyText">
							Your e-mail address is associated to multiple accounts on this website.<br/><br/>
							Choose your account from the list below to complete the process.<br/><br/>
							<table cellpadding="8" cellspacing="0" align="center">
							<tr>
								<td class="tsAppBodyText tsAppBB"><b>Name</b></td>
								<td class="tsAppBodyText tsAppBB"><b>Username</b></td>
								<td class="tsAppBodyText tsAppBB"><b>Date of Last Login</b></td>
								<td class="tsAppBodyText">&nbsp;</td>
							</tr>
							<cfloop query="local.memberData">
								<cfset local.loginData = getLastLogin(arguments.event.getValue('mc_siteInfo.siteID'),local.memberData.memberID)>
								<cfset local.username = application.objUser.login_getUsername(memberID=local.memberData.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
								<tr>
									<td class="tsAppBodyText">#local.memberData.firstName# #local.memberData.lastName#</td>
									<td class="tsAppBodyText">#local.userName#</td>
									<td class="tsAppBodyText">
										<cfif len(local.loginData.lastLogin)>
											#dateFormat(local.loginData.lastLogin,'m/d/yyyy')# #timeFormat(local.loginData.lastLogin,'h:mm tt')#
										<cfelse>
											N/A
										</cfif>
									</td>
									<td>
										<button class="tsAppBodyButton" type="button" onClick="self.location.href='/?#cgi.QUERY_STRING#&memberID=#local.memberData.memberID#';">Select Account</button>
									</td>
								</tr>
							</cfloop>
							</table>							
						</td>
					</tr>
					</table>
					</div>
					</cfoutput>	
				</cfsavecontent>
			</cfdefaultcase>
		</cfswitch>
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="adminResetRequest" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cfset local.data = doResetRequest(arguments.event)>
		<cfreturn local.data />
	</cffunction>

	<cffunction name="generateResetRequest" access="public" output="false" returntype="struct">
		<cfargument name="siteid" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="expire" type="boolean" required="yes">
		<cfargument name="resetMemberID" type="numeric" required="no" default="0">
		<cfargument name="email" type="string" required="no" default="">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
		<cfset local.strReturn.dataValidated = FALSE>
		<cfset local.strReturn.encString = "">
		<cfset local.strReturn.memberInfo = { memberID=0, firstname='', lastname='', email='', accountStatus='', username='' }>
		
		<cfquery name="local.qryOrgId" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select orgID 
			from dbo.sites 
			where siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.orgID = local.qryOrgId.orgID>

		<cfif application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).useRemoteLogin is not 1>
			<!--- check for memberdata in network profiles --->
			<cfquery name="local.findMCInfo" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.orgID#">;
				declare @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

				SELECT TOP 1 m.memberID, m.firstname, m.lastname, np.status as npStatus, mnp.status as mnpStatus, m.status as mStatus, me.email, metag.emailTypeID
				FROM dbo.ams_networkProfiles AS np
				INNER JOIN dbo.networkSites AS ns ON ns.siteID = @siteID
					AND np.networkID = ns.networkID
					AND ns.isLoginNetwork = 1
				INNER JOIN dbo.ams_memberNetworkProfiles AS mnp ON np.profileID = mnp.profileID
					AND mnp.siteID = ns.siteID
					AND mnp.status IN ('A','I')
				INNER JOIN dbo.ams_memberEmails AS me ON me.orgID = @orgID
					AND mnp.memberID = me.memberID
					<cfif arguments.resetMemberID gt 0>
						AND mnp.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.resetMemberID#">
					<cfelse>
						AND me.email = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.email#">
					</cfif>
				INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID 
					and metag.memberID = me.memberID 
					AND metag.emailTypeID = me.emailTypeID
				INNER JOIN dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = @orgID 
					AND metagt.emailTagTypeID = metag.emailTagTypeID 
					AND metagt.emailTagType = 'Primary'
				INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
					AND mnp.memberID = m.activeMemberID 
					AND m.memberID = m.activeMemberID 
					AND m.status IN ('A','I');

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfif local.findMCInfo.recordCount>
				<cfscript>
				local.strReturn.dataValidated = TRUE;
				local.strReturn.memberInfo.isProfile = true;
				local.strReturn.memberInfo.memberID = local.findMCInfo.memberID; 
				local.strReturn.memberInfo.emailTypeID = local.findMCInfo.emailTypeID; 
				local.strReturn.memberInfo.firstName = local.findMCInfo.firstname;
				local.strReturn.memberInfo.lastName = local.findMCInfo.lastname;
				local.strReturn.memberInfo.email = local.findMCInfo.email;
				if( local.findMCInfo.npStatus EQ "A" AND local.findMCInfo.mnpStatus EQ "A" AND local.findMCInfo.mStatus EQ "A" )
					local.strReturn.memberInfo.accountStatus = "A";
				else
					local.strReturn.memberInfo.accountStatus = "I";
				</cfscript>
	
			<!--- if no records exists then query memberSiteDefaults --->
			<cfelse>
				<cfquery name="local.findMemberDefaultInfo" datasource="#application.dsn.memberCentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					declare @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.orgID#">;
					declare @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

					SELECT top 1 m.firstname, m.lastname, msd.memberID, msd.status as msdStatus, m.status as mStatus, me.email, metag.emailTypeID
					FROM dbo.ams_memberSiteDefaults AS msd
					INNER JOIN dbo.ams_memberEmails AS me ON me.orgID = @orgID
						AND msd.memberID = me.memberID
						<cfif arguments.resetMemberID gt 0>
							AND msd.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.resetMemberID#">
						<cfelse>
							AND me.email = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.email#">
						</cfif>
					INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID 
						and metag.memberID = me.memberID 
						AND metag.emailTypeID = me.emailTypeID
					INNER JOIN dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = @orgID 
						AND metagt.emailTagTypeID = metag.emailTagTypeID 
						AND metagt.emailTagType = 'Primary'
					INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID 
						AND msd.memberID = m.memberID 
						AND m.status IN ('A','I')
					WHERE msd.siteID = @siteID
					AND msd.status IN ('A','I');

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				
				<cfif local.findMemberDefaultInfo.recordCount>
					<cfscript>
					local.strReturn.dataValidated = TRUE;
					local.strReturn.memberInfo.isProfile = false;
					local.strReturn.memberInfo.memberID = local.findMemberDefaultInfo.memberID;
					local.strReturn.memberInfo.emailTypeID = local.findMemberDefaultInfo.emailTypeID; 
					local.strReturn.memberInfo.firstName = local.findMemberDefaultInfo.firstname;
					local.strReturn.memberInfo.lastName = local.findMemberDefaultInfo.lastname;
					local.strReturn.memberInfo.email = local.findMemberDefaultInfo.email;
					if( local.findMemberDefaultInfo.msdStatus EQ "A" AND local.findMemberDefaultInfo.mStatus EQ "A" )
						local.strReturn.memberInfo.accountStatus = "A";
					else
						local.strReturn.memberInfo.accountStatus = "I";
					</cfscript>
				</cfif>			
			</cfif>
			
			<cfset local.strReturn.memberInfo.username = application.objUser.login_getUsername(memberID=local.strReturn.memberInfo.memberID, siteID=arguments.siteID)>

			<cfif local.strReturn.dataValidated and isValid("regex",local.strReturn.memberInfo.email,application.regEx.email) and local.strReturn.memberInfo.accountStatus EQ "A">
				<cfset local.strResetRequest = insertResetRequest(local.strReturn.memberInfo.email,arguments.resetMemberID,arguments.expire)>
				<cfset local.strReturn.strURL = { a=RandRange(*********,*********,'SHA1PRNG'), r=local.strResetRequest.rprID, s=local.strResetRequest.statsSessionID, m=local.strResetRequest.memberID }>
				<cfset local.strReturn.encString = encrypt(serializeJSON(local.strReturn.strURL),"TRiaL_SMiTH", "CFMX_COMPAT", "Hex")>
			</cfif>
		</cfif>
			
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="doResetRequest" access="private" output="false" returntype="numeric">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.emailValid = FALSE;			
			local.strRequest = generateResetRequest(siteid=arguments.event.getValue('mc_siteInfo.siteID'), memberid=arguments.event.getValue('memberID','0'),
				expire=1, resetMemberID=arguments.event.getValue('resetMemberID',0), email=arguments.event.getValue('email',''));
		</cfscript>		
		
		<cfif local.strRequest.dataValidated and isValid("regex",local.strRequest.memberInfo.email,application.regEx.email)>
			<cfset local.emailValid = TRUE>
			<cfif local.strRequest.memberInfo.accountStatus EQ "A">
				<cfset local.strLoginOrgIdentityInfo = application.objOrgInfo.getOrgIdentity(orgIdentityID=arguments.event.getValue('mc_siteInfo.loginOrgIdentityID'))>
				<cfsavecontent variable="local.emailContent">
					<cfoutput>
					<p>#local.strRequest.memberInfo.firstName# #local.strRequest.memberInfo.lastName#:</p>
					<p>We received a request to reset your login credentials for #arguments.event.getValue('mc_siteInfo.siteName')#.</p>
					<p>
						<a style="font-weight:bold;" href="#arguments.event.getValue('mc_siteInfo.scheme')#://#application.objPlatform.getCurrentHostname()#/?pg=login&logact=resetPassword&id=#local.strRequest.encString#">Click This Link to Reset Your Login.</a> 
						<em>(This link will expire in 24 hours)</em>
					</p>
					<p>
						You'll be able to select a new username and password to use with #arguments.event.getValue('mc_siteInfo.siteName')#. 
						<cfif local.strRequest.memberinfo.isProfile>					
							As a reminder, your current username is: <b>#local.strRequest.memberinfo.username#.</b>
						</cfif>
					</p>
					<p>
						Thank you,<br/>
						#local.strLoginOrgIdentityInfo.organizationName#<br/>
						#arguments.event.getValue('mc_siteInfo.mainhostname')#
					</p>
					</cfoutput>
				</cfsavecontent>
				<cfset local.emailContent = application.objEmailWrapper.wrapMessage(emailTitle="#arguments.event.getValue('mc_siteinfo.sitename')# Login Reset Request", emailContent=local.emailContent, sitecode=arguments.event.getValue('mc_siteInfo.sitecode'))>

				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_sendResetLoginEmail">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.memberdata.memberID)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.emailContent#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strLoginOrgIdentityInfo.organizationName#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteinfo.networkEmailFrom')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strLoginOrgIdentityInfo.email#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Login Reset Request for #local.strRequest.memberInfo.firstName# #local.strRequest.memberInfo.lastName#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.strRequest.memberInfo.memberID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strRequest.memberInfo.firstName# #local.strRequest.memberInfo.lastName#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strRequest.memberInfo.email#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.strRequest.memberInfo.emailTypeID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.strRequest.strURL.r#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.messageID">
				</cfstoredproc>

				<!--- send message immediately --->
				<cfif local.messageID gt 0>
					<cftry>
						<cfhttp method="get" url="#application.paths.backendPlatform.internalUrl#?event=integrations.sendgrid.processmessage&messageID=#local.messageID#">
					<cfcatch type="Any">
					</cfcatch>
					</cftry>
				</cfif>
			</cfif>
		</cfif>
		
		<cfif local.strRequest.memberInfo.accountStatus EQ 'I'>
			<cfset local.msg = 4>
		<cfelseif NOT local.strRequest.dataValidated>
			<cfset local.msg = 2>
		<cfelseif NOT local.emailValid>
			<cfset local.msg = 3>
		<cfelse>
			<cfset local.msg = 1>
		</cfif>

		<cfreturn local.msg>
	</cffunction>

	<cffunction name="doPasswordReset" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.NEWUSERNAME = 'NOCHANGE'>

		<!--- ensure id, password is passed in --->
		<cfif not arguments.event.valueExists('id') or not arguments.event.valueExists('password')>
			<cfreturn getResetPasswordForm(arguments.event)>
		</cfif>
		
		<!--- get current info --->
		<cfquery name="local.qryCurrentInfo" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int, @memberID int;
			SET @memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(arguments.event.getValue('memberID'))#">;
			SELECT @orgID = orgID from dbo.ams_members where memberID = @memberID;

			select top 1 m.memberid, m.status, me.email, m.firstName, m.lastname, metag.emailTypeID
			FROM dbo.ams_members as m
			INNER JOIN dbo.ams_memberEmails AS me ON me.orgID = @orgID and me.memberID = m.memberID
			INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID and metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
			INNER JOIN dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = @orgID and metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
			where m.memberID = @memberID
			and m.status IN ('A','I');
		</cfquery>
		<cfif local.qryCurrentInfo.recordcount is 0>
			<cfreturn getResetPasswordForm(arguments.event)>
		</cfif>
		
		<cfset local.username = application.objUser.login_getUsername(memberID=local.qryCurrentInfo.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteID'))>

		<!--- if we are changing username, check to see if it is a valid username before continuing. --->
		<cfif compareNoCase(arguments.event.getTrimValue('mc_username',''),local.username)>
			<cfset local.userNameAvailable = application.objUser.login_isUsernameAvailable(siteID=arguments.event.getValue('mc_siteinfo.siteID'), username=arguments.event.getTrimValue('mc_username',''))>
			<cfif NOT local.userNameAvailable>
				<cflocation url="/?pg=login&logact=resetPassword&id=#arguments.event.getValue('id')#&memberid=#val(arguments.event.getValue('memberID'))#&err=3" addtoken="no"> 
			<cfelse>
				<cfset application.objUser.login_setUsername(memberID=arguments.event.getValue('memberID'), siteID=arguments.event.getValue('mc_siteInfo.siteID'), username=arguments.event.getTrimValue('mc_username',''), recordedByMemberID=local.qryCurrentInfo.memberID)>
			</cfif>
		</cfif>

		<!--- save password --->
		<cfset application.objUser.login_setPassword(memberID=arguments.event.getValue('memberID'), siteID=arguments.event.getValue('mc_siteInfo.siteID'), password=arguments.event.getValue('password'), recordedByMemberID=local.qryCurrentInfo.memberID)>

		<cfset updateHasBeenUsed(local.qryCurrentInfo.email)>

		<!--- email confirmation --->
		<cfif isValid("regex",local.qryCurrentInfo.email,application.regEx.email) and local.qryCurrentInfo.status eq "A">
			<cfset local.strLoginOrgIdentityInfo = application.objOrgInfo.getOrgIdentity(orgIdentityID=arguments.event.getValue('mc_siteInfo.loginOrgIdentityID'))>
			<cfsavecontent variable="local.emailContent">
				<cfoutput>
				<p>#local.qryCurrentInfo.firstName# #local.qryCurrentInfo.lastName#:</p>
				<p>This is confirmation that your login credentials have been successfully changed.</p>
				<p>Thank you,<br/>#local.strLoginOrgIdentityInfo.organizationName#<br/>#arguments.event.getValue('mc_siteInfo.mainhostname')#</p>
				</cfoutput>		
			</cfsavecontent>
			<cfset local.emailContent = application.objEmailWrapper.wrapMessage(emailTitle="#arguments.event.getValue('mc_siteinfo.sitename')# Login Change Confirmation", emailContent=local.emailContent, sitecode=arguments.event.getValue('mc_siteInfo.sitecode'))>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_sendResetLoginEmail">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('memberID'))#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.emailContent#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteinfo.orgname')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteinfo.networkEmailFrom')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteinfo.supportProviderEmail')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Login Change Confirmation for #local.qryCurrentInfo.firstName# #local.qryCurrentInfo.lastName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryCurrentInfo.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.qryCurrentInfo.firstName# #local.qryCurrentInfo.lastName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.qryCurrentInfo.email#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryCurrentInfo.emailTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="0">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.messageID">
			</cfstoredproc>

			<!--- send message immediately --->
			<cfif local.messageID gt 0>
				<cftry>
					<cfhttp method="get" url="#application.paths.backendPlatform.internalUrl#?event=integrations.sendgrid.processmessage&messageID=#local.messageID#">
				<cfcatch type="Any">
				</cfcatch>
				</cftry>
			</cfif>
		</cfif>

		<cfif local.qryCurrentInfo.status EQ "I">
			<cfset local.msg = 4>
		<cfelse>
			<cftry>
				<cfset local.username = application.objUser.login_getUsername(memberID=local.qryCurrentInfo.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteID'))>

				<cfset local.loginResponse = loginuser(username=local.username, password='', sitecode=arguments.event.getValue('mc_siteinfo.sitecode'), loginAsMemberID=local.qryCurrentInfo.memberID, loginAsRequestByMemberID=local.qryCurrentInfo.memberID)>

				<cfreturn processLoginResponse(event=arguments.event, loginResponse=local.loginResponse)>
			<cfcatch type="any">
				<cfset local.msg = 5>
			</cfcatch>
			</cftry>
		</cfif>			

		<cflocation url="/?pg=login&logact=message&msg=#local.msg#" addtoken="no">
	</cffunction>
	
	<cffunction name="insertResetRequest" access="private" returntype="query" output="false">
		<cfargument name="emailAddress" type="string" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="expire" type="boolean" required="no" default="1">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
			SET NOCOUNT ON;
			
			declare @rprID int;

			INSERT INTO dbo.ams_resetPasswordRequest(emailAddress, memberID, statsSessionID, dateEntered, emailContents, hasBeenUsed, expire)
			VALUES(
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.emailAddress#">,
				<cfif arguments.memberID EQ 0>
					NULL,
				<cfelse>
					<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">,
				</cfif>
				<cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcUser.statsSessionID#">,
				getDate(), '', 0, <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.expire#">
			)

			select @rprID = SCOPE_IDENTITY();
						
			SELECT rprID, statsSessionID, isNull(memberID,0) as memberID
			FROM dbo.ams_resetPasswordRequest
			WHERE rprID = @rprID;
		</cfquery>
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="doLoginAsMember" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.k = arguments.event.getTrimValue('k','')>

		<cftry>
			<cfset local.stData = decrypt(local.k,"M@!8T$.LuX&C","CFMX_COMPAT","Hex")>
			<cfset local.arrDataItems = listToArray(local.stData,"|",true)>
			<cfif arrayLen(local.arrDataItems) is not 8>
				<cfthrow message="Invalid Key Parts">
			</cfif>

			<cfset local.siteID = local.arrDataItems[2]>
			<cfset local.memberID = local.arrDataItems[3]>
			<cfset local.username = local.arrDataItems[4]>
			<cfset local.requestedByMemberID = local.arrDataItems[5]>
			<cfset local.returnURL = local.arrDataItems[6]>
			<cfset local.linkGeneratedDate = local.arrDataItems[8]>

			<cfif local.siteID neq arguments.event.getValue('mc_siteinfo.siteID')>
				<cfthrow message="Invalid Key Site">
			</cfif>
			<cfif dateDiff("n",local.linkGeneratedDate,now()) gt 5>
				<cfthrow message="Invalid Key Expired">
			</cfif>
			<cfif local.memberID is 0>
				<cfthrow message="Invalid Key Member">
			</cfif>
			<cfif local.username eq "">
				<cfthrow message="Invalid Key Username">
			</cfif>

			<cfif len(local.returnURL)>
				<cfset session.remoteReturnURL = local.returnURL>
			</cfif>

			<!--- if already logged in as this member, just pass through --->
			<!--- else if logged in, log out. --->
			<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
				<cfif session.cfcuser.memberdata.memberid eq local.memberID>
					<cfset sendToDestination()>
				<cfelse>
					<cfset application.objPlatformStats.updateDateLoggedOut(sessionStruct=Session)>
					<cfset application.objPlatform.resetSession(doRedirect=true)>
				</cfif>
			</cfif>
			<cfset local.loginResponse = loginuser(username=local.username, password='', sitecode=arguments.event.getValue('mc_siteinfo.sitecode'), loginAsMemberID=local.memberID, loginAsRequestByMemberID=local.requestedByMemberID)>
			<cfset local.data = processLoginResponse(event=arguments.event, loginResponse=local.loginResponse)>
		<cfcatch type="Any">
			<cfset local.data = processLoginResponse(event=arguments.event, loginResponse='false')>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getLastLogin" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.platformstatsMC.dsn#" name="local.data">
			SELECT max(dateEntered) as lastLogin
			FROM dbo.ams_memberLogins
			WHERE memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			AND siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
		</cfquery>

		<cfreturn local.data>
	</cffunction>	

	<cffunction name="updateHasBeenUsed" access="private" returntype="void" output="false">
		<cfargument name="emailAddress" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
			UPDATE dbo.ams_resetPasswordRequest
			SET hasBeenUsed = 1
			WHERE emailAddress = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.emailAddress#">
			AND hasBeenUsed = 0
		</cfquery>
		
		<cfif StructKeyExists(session.mcStruct,"resetPwdRPRID")>
			<cfset structDelete(session.mcStruct,"resetPwdRPRID")>
		</cfif>
	</cffunction>

	<cffunction name="MCStaffLogin" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = {};
			local.returnString = "";
			local.siteInfo = arguments.event.getValue('mc_siteinfo');
			local.MCStaffLogin = (local.siteInfo.siteCode NEQ 'MC');
			local.hostnameForCurrentSite=application.objPlatform.getCurrentHostname();
		</cfscript>

		<cfif local.siteInfo.siteCode EQ 'MC'>
			<cflocation url="/?pg=login" addtoken="no">
		<cfelse>
			<cfset local.protocol = application.objPlatform.isRequestSecure() ? 'https' : 'http'>
			<cfset local.MCHostname = lcase("#local.protocol#://" & application.objWebsite.getHostnameForAnotherSite(destinationSitecode='mc', sitecodeForCurrentSite=local.siteInfo.siteCode, hostnameForCurrentSite=local.hostnameForCurrentSite))>
			<cfset local.returnURL = "#local.protocol#://#local.hostnameForCurrentSite#/?pg=login">
			<cfset local.argsEnc = encrypt(serializeJSON({"callingHostname":local.hostnameForCurrentSite, "siteCode":local.siteInfo.siteCode, "returnURL":local.returnURL, "randomizer":createGUID()}),"M3mberC3ntr@l", "CFMX_COMPAT", "Hex")>
			<cfset local.loginlink = "#local.MCHostname#/?pg=login&hostinfo=#local.argsEnc#">

			<cflocation url="#local.loginlink#" addtoken="no">

		</cfif>
		<cfreturn local.returnString>
	</cffunction>

	<cffunction name="getMFAPhoneNumber" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var qryMFAPhoneNumber = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryMFAPhoneNumber">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">,
				@loginNetworkID int, @memberNetworkProfileID int;

			SELECT @loginNetworkID = dbo.fn_getloginnetworkfromsiteid(@siteID);

			SELECT @memberNetworkProfileID = mnp.mnpID
			FROM dbo.ams_memberNetworkProfiles AS mnp
			INNER JOIN dbo.ams_networkProfiles as np on np.profileID = mnp.profileID 
				AND np.networkID = @loginNetworkID
				AND np.[status] = 'A'
			WHERE mnp.memberID = @memberID
			AND mnp.siteID = @siteID
			AND mnp.[status] = 'A';

			-- superuser?
			IF @memberNetworkProfileID IS NULL
				SELECT @memberNetworkProfileID = mnp.mnpID
				FROM dbo.ams_memberNetworkProfiles AS mnp
				INNER JOIN dbo.ams_networkProfiles as np on np.profileID = mnp.profileID 
					AND np.networkID = 1
					AND np.[status] = 'A'
				INNER JOIN dbo.ams_members AS m ON m.orgID = 1 
					AND m.memberID = mnp.memberID 
					AND m.[status] in ('A','I')
				WHERE mnp.memberID = @memberID
				AND mnp.siteID = @siteID
				AND mnp.[status] = 'A';

			SELECT MFAPhoneNumber
			FROM dbo.ams_memberNetworkProfiles
			WHERE mnpID = @memberNetworkProfileID;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryMFAPhoneNumber.MFAPhoneNumber>
	</cffunction>

	<cffunction name="saveMFAPhoneNumber" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="phoneNumber" type="string" required="true">

		<cfset var qrySaveMFAPhoneNumber = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySaveMFAPhoneNumber">
			SET NOCOUNT ON;
			
			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">,
				@phoneNumber varchar(50) = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.phoneNumber)#">,''),
				@loginNetworkID int, @memberNetworkProfileID int;

			SELECT @loginNetworkID = dbo.fn_getloginnetworkfromsiteid(@siteID);

			SELECT @memberNetworkProfileID = mnp.mnpID
			FROM dbo.ams_memberNetworkProfiles AS mnp
			INNER JOIN dbo.ams_networkProfiles as np on np.profileID = mnp.profileID 
				AND np.networkID = @loginNetworkID
				AND np.[status] = 'A'
			WHERE mnp.memberID = @memberID
			AND mnp.siteID = @siteID
			AND mnp.[status] = 'A';

			-- superuser?
			IF @memberNetworkProfileID IS NULL
				SELECT @memberNetworkProfileID = mnp.mnpID
				FROM dbo.ams_memberNetworkProfiles AS mnp
				INNER JOIN dbo.ams_networkProfiles as np on np.profileID = mnp.profileID 
					AND np.networkID = 1
					AND np.[status] = 'A'
				INNER JOIN dbo.ams_members AS m ON m.orgID = 1 
					AND m.memberID = mnp.memberID 
					AND m.[status] in ('A','I')
				WHERE mnp.memberID = @memberID
				AND mnp.siteID = @siteID
				AND mnp.[status] = 'A';
			
			IF @memberNetworkProfileID IS NOT NULL
				UPDATE dbo.ams_memberNetworkProfiles
				SET MFAPhoneNumber = @phoneNumber
				WHERE mnpID = @memberNetworkProfileID;
		</cfquery>

		<cfset sendMFAMethodUpdateNotification(siteID=arguments.siteID, memberID=arguments.memberID, methodCode='MFASMS', action=len(trim(arguments.phoneNumber)) ? 'update' : 'delete')>
	</cffunction>

	<cffunction name="getMFATOTPSettings" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var qryMFATOTPSettings = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryMFATOTPSettings">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">,
				@loginNetworkID int, @memberNetworkProfileID int;

			SELECT @loginNetworkID = dbo.fn_getloginnetworkfromsiteid(@siteID);

			SELECT @memberNetworkProfileID = mnp.mnpID
			FROM dbo.ams_memberNetworkProfiles AS mnp
			INNER JOIN dbo.ams_networkProfiles as np on np.profileID = mnp.profileID 
				AND np.networkID = @loginNetworkID
				AND np.[status] = 'A'
			WHERE mnp.memberID = @memberID
			AND mnp.siteID = @siteID
			AND mnp.[status] = 'A';

			-- superuser?
			IF @memberNetworkProfileID IS NULL
				SELECT @memberNetworkProfileID = mnp.mnpID
				FROM dbo.ams_memberNetworkProfiles AS mnp
				INNER JOIN dbo.ams_networkProfiles as np on np.profileID = mnp.profileID 
					AND np.networkID = 1
					AND np.[status] = 'A'
				INNER JOIN dbo.ams_members AS m ON m.orgID = 1 
					AND m.memberID = mnp.memberID 
					AND m.[status] in ('A','I')
				WHERE mnp.memberID = @memberID
				AND mnp.siteID = @siteID
				AND mnp.[status] = 'A';

			SELECT MFATOTPIdentity, MFATOTPSecret, MFATOTPSid,
				CASE WHEN MFATOTPIdentity IS NOT NULL AND MFATOTPSecret IS NOT NULL AND MFATOTPSid IS NOT NULL THEN 1 ELSE 0 END AS hasSetupTOTP
			FROM dbo.ams_memberNetworkProfiles
			WHERE mnpID = @memberNetworkProfileID;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryMFATOTPSettings>
	</cffunction>

	<cffunction name="saveMFATOTPSettings" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="identity" type="string" required="true">
		<cfargument name="sid" type="string" required="true">
		<cfargument name="secret" type="string" required="true">

		<cfset var qrySaveMFATOTPSettings = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySaveMFATOTPSettings">
			SET NOCOUNT ON;
			
			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">,
				@MFATOTPIdentity uniqueIdentifier = <cfif len(arguments.identity)><cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.identity#"><cfelse>NULL</cfif>,
				@MFATOTPSecret varchar(50) = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.secret)#">,''),
				@MFATOTPSid varchar(34) = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.sid)#">,''),
				@loginNetworkID int, @memberNetworkProfileID int;

			SELECT @loginNetworkID = dbo.fn_getloginnetworkfromsiteid(@siteID);

			SELECT @memberNetworkProfileID = mnp.mnpID
			FROM dbo.ams_memberNetworkProfiles AS mnp
			INNER JOIN dbo.ams_networkProfiles as np on np.profileID = mnp.profileID 
				AND np.networkID = @loginNetworkID
				AND np.[status] = 'A'
			WHERE mnp.memberID = @memberID
			AND mnp.siteID = @siteID
			AND mnp.[status] = 'A';

			-- superuser?
			IF @memberNetworkProfileID IS NULL
				SELECT @memberNetworkProfileID = mnp.mnpID
				FROM dbo.ams_memberNetworkProfiles AS mnp
				INNER JOIN dbo.ams_networkProfiles as np on np.profileID = mnp.profileID 
					AND np.networkID = 1
					AND np.[status] = 'A'
				INNER JOIN dbo.ams_members AS m ON m.orgID = 1 
					AND m.memberID = mnp.memberID 
					AND m.[status] in ('A','I')
				WHERE mnp.memberID = @memberID
				AND mnp.siteID = @siteID
				AND mnp.[status] = 'A';
			
			IF @memberNetworkProfileID IS NOT NULL
				UPDATE dbo.ams_memberNetworkProfiles
				SET MFATOTPIdentity = @MFATOTPIdentity,
					MFATOTPSecret = @MFATOTPSecret,
					MFATOTPSid = @MFATOTPSid
				WHERE mnpID = @memberNetworkProfileID;
		</cfquery>

		<cfset sendMFAMethodUpdateNotification(siteID=arguments.siteID, memberID=arguments.memberID, methodCode='MFATOTP', action=len(arguments.identity) ? 'update' : 'delete')>
	</cffunction>

	<cffunction name="sendMFAMethodUpdateNotification" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="methodCode" type="string" required="true">
		<cfargument name="action" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.recipientEmail = application.objMember.getMainEmail(memberID=arguments.memberID).email>
		<cfif len(local.recipientEmail)>
			<cfset local.siteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=arguments.siteID)>
			<cfset local.siteInfo = application.objSiteInfo.getSiteInfo(siteCode=local.siteCode)>
			<cfset local.loginSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Login', siteID=local.siteInfo.siteID)>
			<cfset local.qryMember = application.objMember.getMemberInfo(memberID=arguments.memberID, orgID=local.siteInfo.orgID)>
			<cfset local.strLoginOrgIdentityInfo = application.objOrgInfo.getOrgIdentity(orgIdentityID=local.siteInfo.loginOrgIdentityID)>

			<cfset local.strMFAMethod = { 
				"MFATOTP": { "name":"Authenticator App", "shortName":"Authenticator" },
				"MFASMS": { "name":"Phone Verification", "shortName":"Text Messaging" }
			}>
	
			<cfif arguments.action EQ 'update'>
				<cfset local.emailSubject = "#local.siteInfo.siteName# Login Security - #local.strMFAMethod[arguments.methodCode].name# information changed">
				<cfsavecontent variable="local.emailContent">
					<cfoutput>	
					#local.qryMember.firstName# #local.qryMember.lastName#: <br/><br/>
					Your #local.strMFAMethod[arguments.methodCode].name# information changed. <br/><br/>
					If you did not request this change please contact #local.strLoginOrgIdentityInfo.organizationName# at #local.strLoginOrgIdentityInfo.phone# or #local.strLoginOrgIdentityInfo.email#.
					</cfoutput>
				</cfsavecontent>
			<cfelse>
				<cfset local.emailSubject = "#local.siteInfo.siteName# Login Security - #local.strMFAMethod[arguments.methodCode].name# was removed from your account">
				<cfsavecontent variable="local.emailContent">
					<cfoutput>	
					#local.qryMember.firstName# #local.qryMember.lastName#: <br/><br/>
					The following MFA Method was removed from your account:<br/>
					#local.strMFAMethod[arguments.methodCode].name# <br/><br/>
					If you did not request this removal please contact #local.strLoginOrgIdentityInfo.organizationName# at #local.strLoginOrgIdentityInfo.phone# or #local.strLoginOrgIdentityInfo.email#.
					</cfoutput>
				</cfsavecontent>
			</cfif>
			
			<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=local.siteInfo.orgname, email=local.siteInfo.networkEmailFrom },
				emailto=[{ name:'', email:local.recipientEmail }],
				emailreplyto=local.siteInfo.supportProviderEmail,
				emailsubject=local.emailSubject,
				emailtitle="#local.siteInfo.siteName# Multi-Factor Authentication",
				emailhtmlcontent=local.emailContent,
				emailAttachments=[],
				siteID=local.siteInfo.siteID,
				memberID=arguments.memberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="LOGINMFAMTD"),
				sendingSiteResourceID=local.loginSiteResourceID
			)>
		</cfif>
	</cffunction>

	<cffunction name="manageSettings" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfset local.validateParams = manageSettings_validateParameters(arguments.event)>
		<cfif NOT local.validateParams>
			<cflocation url="/?pg=login" addtoken="no">
		</cfif>

		<cfset var supportedPolicyMethods = "MFATOTP,MFASMS">
		<cfset local.data = "">
		<cfset local.manageSettingsResourceLink = "?event=cms.showResource&resID=#this.siteResourceID#&logact=manageSettings&mode=stream">

		<cfswitch expression="#arguments.event.getValue('la')#">

			<!--- verify password --->
			<cfcase value="vp">
				<cfset local.isValidPassword = manageSettings_verifyPassword(arguments.event)>
				<cflocation url="/?pg=login&logact=manageSettings#NOT local.isValidPassword ? '&invpwd=1' : ''#" addtoken="no">
			</cfcase>

			<!--- mfa totp --->
			<cfcase value="mfatotp">
				<cfset local.qryLoginPolicyMethod = getQualifiedLoginPolicyMethod(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=session.cfcuser.memberData.memberID)>
				<cfset local.qryTOTPPolicyMethod = local.qryLoginPolicyMethod.filter((row) => listFindNoCase("MFATOTP",arguments.row.methodCode))>

				<!--- invalid --->
				<cfif NOT local.qryTOTPPolicyMethod.recordCount>
					<cfreturn "Invalid Settings.">
				</cfif>

				<cfset local.qryMFATOTPSettings = getMFATOTPSettings(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=session.cfcuser.memberData.memberID)>

				<cfif arguments.event.valueExists('totpAct') AND isSimpleValue(arguments.event.getValue('totpAct')) AND listFindNoCase("createFactor,verifyFactor,delete",arguments.event.getValue('totpAct'))>
					<cfset local.MFATOTP_action = arguments.event.getValue('totpAct')>
				<cfelse>
					<cfset local.MFATOTP_action = "">
				</cfif>

				<cfswitch expression="#local.MFATOTP_action#">
					<cfcase value="createFactor">
						<cfscript>
							local.identity = getSQLUniqueIdentifier();
							local.objTwilioAPI = new model.system.utility.twilioAPI();
							local.strTOTPFactor= local.objTwilioAPI.callAPI(method="POST", endpoint="Entities/#local.identity#/Factors", 
															payload=[ 
																{ "type":"formfield", "name":"FriendlyName", "value":left(arguments.event.getValue('mc_siteinfo.siteName'),64) }, 
																{ "type":"formfield", "name":"FactorType", "value":"totp" } 
															]
														);

							if (local.strTOTPFactor.statusCode EQ 201) {
								local.strNewTOTPFactor = { "identity":local.identity, "sid":local.strTOTPFactor.strResult.sid, "secret":local.strTOTPFactor.strResult.binding.secret, "randomizer":createGUID() };
								local.MFA_TOTPInfoEnc = encrypt(serializeJSON(local.strNewTOTPFactor),"M3mberC3ntr@l^_L051N", "CFMX_COMPAT", "Hex");
								insertVerificationMethodCodeRequests(siteID=arguments.event.getValue('mc_siteinfo.siteid'), loginPolicyID=local.qryTOTPPolicyMethod.loginPolicyID, memberID=session.cfcuser.memberData.memberID, mode='setup', verificationMethodID=local.qryTOTPPolicyMethod.policyMethodID, twilioChannel='');
								savecontent variable="local.data" {
									include "/views/login/#arguments.event.getValue('viewDirectory')#/frm_manageSettings_MFATOTP_QRCode.cfm";
								}
							} else {
								local.data = "Unable to generate the QR Code.";
							}
						</cfscript>
					</cfcase>
					<cfcase value="verifyFactor">
						<cfscript>
							local.strTOTPFactor = deserializeJSON(decrypt(arguments.event.getTrimValue('MFATOTPInfoEnc',''),"M3mberC3ntr@l^_L051N", "CFMX_COMPAT", "Hex"));
							local.securityCode = arguments.event.getTrimValue('securityCode','');

							// invalid
							if (NOT isStruct(local.strTOTPFactor) OR local.securityCode EQ '') {
								return serializeJSON({ "success":true, "verified":false });
							}

							local.objTwilioAPI = new model.system.utility.twilioAPI();
							local.strVerifyTOTPFactor= local.objTwilioAPI.callAPI(method="POST", endpoint="Entities/#local.strTOTPFactor.identity#/Factors/#local.strTOTPFactor.sid#", 
															payload=[ 
																{ "type":"formfield", "name":"AuthPayload", "value":local.securityCode }
															]
														);

							if (local.strVerifyTOTPFactor.statusCode EQ 200 AND local.strVerifyTOTPFactor.strResult.status EQ 'verified') {
								saveMFATOTPSettings(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=session.cfcuser.memberData.memberID, identity=local.strTOTPFactor.identity,
									sid=local.strTOTPFactor.sid, secret=local.strTOTPFactor.secret);
								insertVerificationMethodValidateRequests(siteID=arguments.event.getValue('mc_siteinfo.siteid'), loginPolicyID=local.qryTOTPPolicyMethod.loginPolicyID, memberID=session.cfcuser.memberData.memberID, loginID=session.cfcuser["loginID"], mode='setup', verificationMethodID=local.qryTOTPPolicyMethod.policyMethodID, success=1);
								return serializeJSON({ "success":true, "verified":true });
							} else {
								insertVerificationMethodValidateRequests(siteID=arguments.event.getValue('mc_siteinfo.siteid'), loginPolicyID=local.qryTOTPPolicyMethod.loginPolicyID, memberID=session.cfcuser.memberData.memberID, loginID=session.cfcuser["loginID"], mode='setup', verificationMethodID=local.qryTOTPPolicyMethod.policyMethodID, success=0);
								return serializeJSON({ "success":true, "verified":false });
							}
						</cfscript>
					</cfcase>
					<cfcase value="delete">
						<cfset saveMFATOTPSettings(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=session.cfcuser.memberData.memberID, identity='', sid='', secret='')>
						<cflocation url="/?pg=login&logact=manageSettings" addtoken="no">
					</cfcase>
					<cfdefaultcase>
						<cfsavecontent variable="local.data">
							<cfinclude template="/views/login/#arguments.event.getValue('viewDirectory')#/frm_manageSettings_MFATOTP.cfm">
						</cfsavecontent>
					</cfdefaultcase>
				</cfswitch>
			</cfcase>

			<!--- mfa sms --->
			<cfcase value="mfasms">
				<cfset local.qryLoginPolicyMethod = getQualifiedLoginPolicyMethod(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=session.cfcuser.memberData.memberID)>
				<cfset local.qrySMSPolicyMethod = local.qryLoginPolicyMethod.filter((row) => listFindNoCase("MFASMS",arguments.row.methodCode))>

				<!--- invalid --->
				<cfif NOT local.qrySMSPolicyMethod.recordCount>
					<cfreturn "Invalid Settings.">
				</cfif>

				<cfset local.MFAPhoneNumber = getMFAPhoneNumber(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=session.cfcuser.memberData.memberID)>

				<!--- Manage MFA Phone Number --->
				<cfif arguments.event.valueExists('MFAPhNo') AND isSimpleValue(arguments.event.getValue('MFAPhNo')) AND len(arguments.event.getValue('MFAPhNo'))>
					<cfscript>
						local.objTwilioAPI = new model.system.utility.twilioAPI();
						local.validPhoneNumber = local.objTwilioAPI.validPhoneNumber(phoneNumber=arguments.event.getValue('MFAPhNo'));

						// invalid phone number
						if (NOT local.validPhoneNumber) {
							return serializeJSON({ "success":true, "validphno":false, "reattemptdelay":0, "channel":arguments.event.getValue('mfasms_channel'), "codeattempts":0 });
						}

						local.verifiedPhoneNumber = arguments.event.getValue('MFAPhNo');
						
						// send code
						if (arguments.event.valueExists('verifyMFAPhNo')) {
							local.strSendCode = sendVerificationCode_MFASMS(phoneNumber=local.verifiedPhoneNumber, channel=arguments.event.getValue('mfasms_channel'));
							insertVerificationMethodCodeRequests(siteID=arguments.event.getValue('mc_siteinfo.siteid'), loginPolicyID=local.qrySMSPolicyMethod.loginPolicyID, memberID=session.cfcuser.memberData.memberID, mode='setup', verificationMethodID=local.qrySMSPolicyMethod.policyMethodID, twilioChannel=local.strSendCode.channel);
							return serializeJSON({ "success":true, "validphno":true, "reattemptdelay":local.strSendCode.secondsBeforeAllowedReattempt, "channel":local.strSendCode.channel, "codeattempts":local.strSendCode.codeattempts });
						}
						// verify otp
						else if (arguments.event.valueExists('verifyMFAPhNoOTP')) {
							local.strSMSVerificationCheck = local.objTwilioAPI.callAPI(method="POST", endpoint="VerificationCheck", 
																payload=[ { "type":"formfield", "name":"To", "value":local.verifiedPhoneNumber }, 
																			{ "type":"formfield", "name":"Code", "value":arguments.event.getTrimValue('securityCode','') } 
																]
															);
							
							// save phone number
							if (local.strSMSVerificationCheck.statusCode EQ 200 AND local.strSMSVerificationCheck.strResult.status EQ 'approved') {
								resetMFASMSCounter(phoneNumber=local.verifiedPhoneNumber);
								saveMFAPhoneNumber(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=session.cfcuser.memberData.memberID, phoneNumber=local.verifiedPhoneNumber);
								insertVerificationMethodValidateRequests(siteID=arguments.event.getValue('mc_siteinfo.siteid'), loginPolicyID=local.qrySMSPolicyMethod.loginPolicyID, memberID=session.cfcuser.memberData.memberID, loginID=session.cfcuser["loginID"], mode='setup', verificationMethodID=local.qrySMSPolicyMethod.policyMethodID, success=1);
								return serializeJSON({ "success":true, "verified":true });
							} else {
								insertVerificationMethodValidateRequests(siteID=arguments.event.getValue('mc_siteinfo.siteid'), loginPolicyID=local.qrySMSPolicyMethod.loginPolicyID, memberID=session.cfcuser.memberData.memberID, loginID=session.cfcuser["loginID"], mode='setup', verificationMethodID=local.qrySMSPolicyMethod.policyMethodID, success=0);
								return serializeJSON({ "success":true, "verified":false });
							}
						}
						// invalid action
						else {
							return serializeJSON({ "success":false });
						}
					</cfscript>
				
				<!--- delete MFA Phone Number --->
				<cfelseif arguments.event.valueExists('deleteMFAPhNo')>
					<cfset saveMFAPhoneNumber(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=session.cfcuser.memberData.memberID, phoneNumber='')>
					<cflocation url="/?pg=login&logact=manageSettings" addtoken="no">
				</cfif>

				<cfsavecontent variable="local.data">
					<cfinclude template="/views/login/#arguments.event.getValue('viewDirectory')#/frm_manageSettings_MFASMS.cfm">
				</cfsavecontent>
			</cfcase>

			<!--- set up later --->
			<cfcase value="setuplater">
				<cfif arguments.event.getValue('ManageLoginSettingRights')>
					<cfset local.strLoginCompliance = getLoginPolicyComplianceStatus()>
					<cfset var nonCompliantMethod = listFirst(local.strLoginCompliance.nonmetRequiredMethods)>
					<cfset local.qryLoginPolicyMethods = getQualifiedLoginPolicyMethod(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=session.cfcuser.memberData.memberID)>
					<cfset local.qrySupportedPolicyMethods = local.qryLoginPolicyMethods.filter((row) => listFindNoCase(supportedPolicyMethods,arguments.row.methodCode))>
					<cfset local.qryNonCompliantMethod = local.qrySupportedPolicyMethods.filter((row) => nonCompliantMethod EQ arguments.row.methodCode)>
					<cfif local.qryNonCompliantMethod.recordCount>
						<cfif len(local.qryNonCompliantMethod.complianceDeadline) AND now() GT local.qryNonCompliantMethod.complianceDeadline>
							<cfset local.allowSetUpLater = false>
						<cfelse>
							<cfset local.allowSetUpLater = true>
						</cfif>						
					<cfelse>
						<cfset local.allowSetUpLater = true>
					</cfif>
					<cfif local.allowSetUpLater>
						<cfset session.mcstruct['loginPolicyComplianceStatus'] = 'bypassed'>
						<cfset sendToDestination()>							
					</cfif>
				</cfif>
				<!--- redirect to manage settings --->
				<cflocation url="/?pg=login&logact=manageSettings" addtoken="no">
			</cfcase>

			<cfdefaultcase>
				<cfset arguments.event.getCollection()['mc_pageDefinition']['pagetitle'] = 'Manage Login Settings'>

				<cfif arguments.event.getValue('ManageLoginSettingRights')>
					<cfset local.strLoginCompliance = getLoginPolicyComplianceStatus()>
					<cfset local.arrConfiguredMethods = getConfiguredLoginPolicyMethod(siteID=arguments.event.getValue('mc_siteinfo.siteid'), memberID=session.cfcuser.memberData.memberID)>
					<cfset local.arrConfiguredMethodsWithOutEmail = local.arrConfiguredMethods.filter((MFAMethod) => arguments.MFAMethod NEQ 'Email')>
					<cfset local.qryLoginPolicyMethods = getQualifiedLoginPolicyMethodsInorder(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=session.cfcuser.memberData.memberID)>
					<cfset local.qrySupportedPolicyMethods = local.qryLoginPolicyMethods.filter((row) => listFindNoCase(supportedPolicyMethods,arguments.row.methodCode))>
					<cfset local.showNonCompliantMsg = arguments.event.valueExists('noncompliant') AND local.strLoginCompliance.complianceStatus EQ 'noncompliant' AND local.strLoginCompliance.ismetRequired EQ 0>
					<cfset local.ismetRequired = local.strLoginCompliance.ismetRequired>
					<cfset local.requiredMethods = local.qrySupportedPolicyMethods.filter((row) => arguments.row.isRequired EQ 1)>
					<cfset local.strMFAMethod = { 
						"MFATOTP": { "name":"Authenticator App", "shortName":"Authenticator", "displayName":"Authenticator&nbsp;App" },
						"MFASMS": { "name":"Text Messaging (SMS)", "shortName":"Text Messaging", "displayName":"Phone&nbsp;Verification" },
						"Email": { "name":"Email Verification", "shortName":"Email", "displayName":"Email" }
					}>
					<cfif structKeyExists(session.cfcuser,"loginID") 
						AND arrayLen(arrConfiguredMethodsWithOutEmail) GTE local.qrySupportedPolicyMethods.reqCountOfFactors 
						AND arrayLen(arrConfiguredMethodsWithOutEmail) LT local.qrySupportedPolicyMethods.recCountOfFactors>
						<cfset updateLastRecPromptedDate(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=session.cfcuser.memberData.memberID, loginID=session.cfcuser["loginID"], noOfDaysForRecCountPrompts=local.qrySupportedPolicyMethods.noOfDaysForRecCountPrompts)>
					</cfif>
					<cfset local.proceedToSiteURL = "">
					<cfif session.mcstruct.keyExists("loginPolicyComplianceStatus") 
						AND session.mcstruct.loginPolicyComplianceStatus EQ 'noncompliant' 
						AND local.strLoginCompliance.complianceStatus EQ 'compliant'>
						<cfset structDelete(session.mcstruct,"loginPolicyComplianceStatus")>
						<cfset local.proceedToSiteURL = getDestination()>
					</cfif>
					<cfset local.viewPage = "frm_manageSettings.cfm">
				<cfelse>
					<cfset local.viewPage = "frm_manageSettings_password.cfm">
				</cfif>

				<cfsavecontent variable="local.data">
					<cfinclude template="/views/login/#arguments.event.getValue('viewDirectory')#/#local.viewPage#">
				</cfsavecontent>
			</cfdefaultcase>

		</cfswitch>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="manageSettings_validateParameters" access="private" output="false" returntype="boolean">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			
			// valid user
			local.isValidUser = application.objUser.isLoggedIn(cfcuser=session.cfcuser);
			if (application.objUser.isSuperUser(cfcuser=session.cfcuser) AND arguments.event.getValue('mc_siteinfo.siteCode') NEQ 'MC')
				local.isValidUser = false;

			if (NOT local.isValidUser) return false;

			// access rights
			arguments.event.setValue('ManageLoginSettingRights',session.mcstruct.keyExists("ManageLoginSettings") AND session.mcstruct.ManageLoginSettings);

			// valid action
			local.action = arguments.event.getValue('la','');
			if (NOT isSimpleValue(local.action) OR (len(local.action) AND NOT listFindNoCase("vp,mfatotp,mfasms,setuplater",local.action)))
				local.action = "";
			else if (listFindNoCase("mfatotp,mfasms",local.action) AND NOT arguments.event.getValue('ManageLoginSettingRights'))
				local.action = "";
			

			arguments.event.setValue('la',local.action);

			if (local.action EQ 'mfasms') {
				arguments.event.paramValue('mfasms_channel','sms');
				if (NOT isSimpleValue(arguments.event.getValue('mfasms_channel')) OR NOT listFindNoCase("sms,call,whatsapp",arguments.event.getValue('mfasms_channel')))
					arguments.event.setValue('mfasms_channel','sms');
			}

			return true;
		</cfscript>
	</cffunction>

	<cffunction name="manageSettings_verifyPassword" access="private" output="false" returntype="boolean">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.isValidPassword = false>

		<!--- verify password --->
		<cfif cgi.request_method eq "POST" 
			AND arguments.event.valueExists('mcpwd') 
			AND isSimpleValue(arguments.event.getTrimValue('mcpwd')) 
			AND len(arguments.event.getTrimValue('mcpwd'))>

			<cfset local.isValidPassword = false>

			<!--- super users --->
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
				<cfset local.strLoginAttemptSU = application.objUser.login_attemptSuperUser(sessionID=session.cfcuser.statsSessionID, siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
					username=session.cfcuser.memberData.username, password=arguments.event.getTrimValue('mcpwd'), loginAsMemberID=0)>

				<cfset local.isValidPassword = local.strLoginAttemptSU.result eq "pass">
			
			<!--- non-superusers --->
			<cfelse>
				<cfset local.strLoginAttemptU = application.objUser.login_attemptUser(sessionID=session.cfcuser.statsSessionID, siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
					username=session.cfcuser.memberData.username, password=arguments.event.getTrimValue('mcpwd'), loginAsMemberID=0)>

				<cfset local.isValidPassword = listFindNoCase("pass.mnp,pass.np,pass.msd,",local.strLoginAttemptU.result) AND listFindNoCase("A,I",local.strLoginAttemptU.memberStatus)>
			</cfif>
	
			<cfset session.mcstruct["ManageLoginSettings"] = local.isValidPassword>
		</cfif>

		<cfreturn local.isValidPassword>
	</cffunction>

	<cffunction name="getQualifiedLoginPolicyMethodsInorder" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="LoginPolicy", functionName="Qualify")>
		
		<cfquery name="local.qryQualifiedLoginPolicyMethod" datasource="#application.dsn.membercentral.dsn#">
			EXEC dbo.ams_getQualifiedLoginPolicy 
				@siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">, 
				@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">,
				@qualifyFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.QualifyRFID#">;
		</cfquery>
		
		<cfquery name="local.qryQualifiedLoginPolicyMethodInorder" dbtype="query">
			select *
			from [local].qryQualifiedLoginPolicyMethod
			order by isConfigured
		</cfquery>

		<cfreturn local.qryQualifiedLoginPolicyMethodInorder>
	</cffunction>

	<cffunction name="getLastUsedMethodCode" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.qGetLastUsedMethod = "">
		<cfset local.qGetLastUsedMethodToApply = "">
		<cfset local.laseUseMethod = "">

		<cfquery name="local.qGetLastUsedMethod" datasource="#application.dsn.membercentral.dsn#">		
			SELECT top 1 pv.methodCode 
			FROM platformStatsMC.dbo.ams_memberLogins al
			INNER JOIN dbo.platform_verificationMethods pv ON al.verificationMethodID = pv.verificationMethodID
			INNER JOIN dbo.siteLoginPolicyVerificationMethods spvm on spvm.verificationMethodID = pv.verificationMethodID AND al.siteID = spvm.siteID
			WHERE al.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#"> AND al.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			AND pv.methodCode IN ('MFATOTP', 'MFASMS')
			ORDER BY dateentered desc		
		</cfquery>
		<cfscript>	
			var arrConfiguredMethods = getConfiguredLoginPolicyMethod(siteID=arguments.siteID, memberID=arguments.memberID);
			local.qGetLastUsedMethodToApply = local.qGetLastUsedMethod.filter((row) => arrConfiguredMethods.contains(row.methodCode));
		</cfscript>
		<cfif local.qGetLastUsedMethodToApply.recordcount>
			<cfset local.laseUseMethod = local.qGetLastUsedMethodToApply.methodCode>
		</cfif>	
		<cfreturn local.laseUseMethod>
	</cffunction>	

	<cffunction name="updateLastRecPromptedDate" access="private" returnType="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="LoginID" type="numeric" required="true">
		<cfargument name="noOfDaysForRecCountPrompts" type="numeric" required="true">

		<cfquery name="qryLoginHistory" datasource="#application.dsn.platformstatsMC.dsn#">
			set nocount on;

			declare @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">,
				@loginID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.LoginID#">,
				@noOfDaysForRecCountPrompts  int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.noOfDaysForRecCountPrompts#">,
				@PrevdateLastRecPrompted DATETIME;
					
			SET @PrevdateLastRecPrompted = ( SELECT top 1 dateLastRecPrompted from dbo.ams_memberLogins where memberID = @memberID and siteid = @siteID AND loginID <> @loginID order by dateentered desc);

			IF (@PrevdateLastRecPrompted is null OR GETDATE() >= dateadd(d,@noOfDaysForRecCountPrompts,@PrevdateLastRecPrompted))
				UPDATE dbo.ams_memberLogins 
				SET dateLastRecPrompted = getdate() 
				where siteID = @siteID 
				and loginID = @loginID
			ELSE
				UPDATE dbo.ams_memberLogins 
				SET dateLastRecPrompted = @PrevdateLastRecPrompted 
				where siteID = @siteID 
				and loginID = @loginID
		</cfquery>
	</cffunction>

	<cffunction name="insertVerificationMethodCodeRequests" access="private" returntype="void" output="false">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="loginPolicyID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="mode" type="string" required="yes">
		<cfargument name="verificationMethodID" type="numeric" required="yes">
		<cfargument name="twilioChannel" type="string" required="yes">
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.platformstatsMC.dsn#" name="local.data">
			SET NOCOUNT ON;
			
			INSERT INTO dbo.platform_verificationMethodCodeRequests(siteID, loginPolicyID, memberID, statsSessionID, mode, verificationMethodID, twilioChannel, dateEntered)
			VALUES(
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.loginPolicyID#">,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">,			
				<cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcUser.statsSessionID#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.mode#">,	
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.verificationMethodID#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.twilioChannel#">,	
				getDate()
			)		
		</cfquery>
	</cffunction>

	<cffunction name="insertVerificationMethodValidateRequests" access="private" returntype="void" output="false">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="loginPolicyID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="loginID" type="numeric" required="yes">
		<cfargument name="mode" type="string" required="yes">
		<cfargument name="verificationMethodID" type="numeric" required="yes">
		<cfargument name="success" type="boolean" required="yes">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.platformstatsMC.dsn#" name="local.data">
			SET NOCOUNT ON;
			
			INSERT INTO dbo.platform_verificationMethodValidateRequests(siteID, loginPolicyID, memberID, loginID, statsSessionID, mode, verificationMethodID, dateEntered, success)
			VALUES(
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.loginPolicyID#">,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.loginID#">,			
				<cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcUser.statsSessionID#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.mode#">,	
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.verificationMethodID#">,	
				getDate(),
				<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.success#">
			)		
		</cfquery>
	</cffunction>

</cfcomponent>
