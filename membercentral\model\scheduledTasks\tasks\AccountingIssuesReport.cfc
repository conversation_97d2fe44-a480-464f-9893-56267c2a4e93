<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="590">

		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="15" } ];
			local.strTaskFields = setTaskCustomFields(siteID=application.objSiteInfo.getSiteInfo('MC').siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfset local.itemCount = getQueueItemCount()>
		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue(batchSize=local.strTaskFields.batchSize)>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="public" output="false" returntype="boolean">
		<cfargument name="batchSize" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.success = true>
		<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;padding:2px;">
		<cfset local.tdStyle2 = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##999;padding:0 0 4px 20px;">
		<cfset local.thStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;border-bottom:1px solid ##333;padding:0;">
		<cfset local.pageStyle = "width:600px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;">
		<cfset local.headingStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:11pt;color:##333;font-weight:bold;padding:22px 0 10px 0;">

		<cfloop from="1" to="#arguments.batchSize#" index="local.thisLoop">
			<cfstoredproc procedure="queue_acctIssuesReport_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qryOrgInfo" resultset="1">
				<cfprocresult name="local.qryOpenInvoices" resultset="2">
				<cfprocresult name="local.qryNonPostedBatches" resultset="3">
				<cfprocresult name="local.qryOutOfOrderInvoices" resultset="4">
				<cfprocresult name="local.qryInvProfAllocViolations" resultset="5">
				<cfprocresult name="local.qryFlaggedTransactions" resultset="6">
				<cfprocresult name="local.qryNoNonSurchargePaymentProfile" resultset="7">
			</cfstoredproc>

			<cfif NOT local.qryOrgInfo.recordCount>
				<cfbreak>
			</cfif>

			<cftry>
				<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(local.qryOrgInfo.sitecode)>

				<cfquery name="local.qryUpdateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;
	
					DECLARE @statusProcessing int;
					EXEC dbo.queue_getStatusIDbyType @queueType='acctIssuesReport', @queueStatus='processing', @queueStatusID=@statusProcessing OUTPUT;

					UPDATE dbo.queue_acctIssuesReport
					SET statusID = @statusProcessing,
						dateUpdated = GETDATE()
					WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryOrgInfo.itemID#">;
				</cfquery>

				<cfif len(local.qryOrgInfo.accountingEmail)>
					<cfsavecontent variable="local.orgEmailContent_invoices">
						<cfif local.qryOpenInvoices.recordcount>
							<cfoutput>
							<div style="#local.headingStyle#">Open Invoices</div>
							<div>
								The following invoices are <b>open</b> with due dates more than 7 days in the past.
								Until these invoices are closed, they will not appear on invoice aging reports, cannot have payments applied 
								to them, and cannot be printed or emailed to members. You need to close these invoices.
							</div>
							<br/>
							<table style="width:600px;">
							<tr>
								<td style="#local.thStyle#" colspan="2"><b>Member</b></td>
								<td style="#local.thStyle#padding-left:4px;"><b>Invoice</b></td>
							</tr>
							<cfloop query="local.qryOpenInvoices">
								<tr valign="top">
									<td style="#local.tdStyle#width:18px;" nowrap>#local.qryOpenInvoices.currentrow#.</td>
									<td style="#local.tdStyle#"><a href="#local.mc_siteInfo.scheme#://#local.mc_siteinfo.mainhostname#/?pg=admin&jumpToTool=MemberAdmin%7Csearch%7Cedit&memberID=#local.qryOpenInvoices.memberID#&tab=transactions">#local.qryOpenInvoices.lastname#, #local.qryOpenInvoices.firstname# (#local.qryOpenInvoices.membernumber#)</a></td>
									<td style="#local.tdStyle#padding-left:4px;">
										#local.qryOpenInvoices.invoiceNumber#<br/>
										#dollarformat(local.qryOpenInvoices.invDue)# due on #dateFormat(local.qryOpenInvoices.dateDue,"m/d/yyyy")#
										<cfif local.qryOpenInvoices.hasCard>
											<br/>There is a pay method on file for this invoice.
										</cfif>
									</td>
								</tr>
							</cfloop>
							</table>
							</cfoutput>							
						</cfif>
					</cfsavecontent>

					<cfsavecontent variable="local.orgEmailContent_captured">
						<cfif local.qryFlaggedTransactions.recordcount>
							<cfoutput>
							<div style="#local.headingStyle#">Transaction Messages</div>
							<div>
								The following transactions have been flagged as needing your attention.
								Review each and delete the alert in Control Panel once it has been addressed.
							</div>
							<br/>
							<table style="width:600px;">
							<tr>
								<td style="#local.thStyle#" colspan="2"><b>Member</b></td>
								<td style="#local.thStyle#padding-left:4px;"><b>Message</b></td>
							</tr>
							<cfloop query="local.qryFlaggedTransactions">
								<tr valign="top">
									<td style="#local.tdStyle#width:18px;" nowrap>#local.qryFlaggedTransactions.currentrow#.</td>
									<td style="#local.tdStyle#"><a href="#local.mc_siteInfo.scheme#://#local.mc_siteinfo.mainhostname#/?pg=admin&jumpToTool=MemberAdmin%7Csearch%7Cedit&memberID=#local.qryFlaggedTransactions.memberID#&tab=transactions">#local.qryFlaggedTransactions.memberName#</a></td>
									<td style="#local.tdStyle#padding-left:4px;">
										#dateFormat(local.qryFlaggedTransactions.dateRecorded,"m/d/yyyy")#<br/>
										#local.qryFlaggedTransactions.message#
									</td>
								</tr>
							</cfloop>
							</table>
							</cfoutput>							
						</cfif>
					</cfsavecontent>

					<cfsavecontent variable="local.orgEmailContent_batches">
						<cfif local.qryNonPostedBatches.recordcount>
							<cfoutput>
							<div style="#local.headingStyle#">Non-Posted Batches</div>
							<div>
								The following batches are <b>not posted</b> and have deposit dates more than 7 days in the past. 
								Until these batches are posted, they will not appear on any accounting reports and your data analysis will be incomplete. 
								You need to post the batch or change its deposit date.
							</div>
							<br/>
							<table style="width:600px;">
							<tr>
								<td style="#local.thStyle#" colspan="2"><b>Batch</b></td>
								<td style="#local.thStyle#padding-left:4px;">&nbsp;</td>
							</tr>
							<cfloop query="local.qryNonPostedBatches">
								<tr valign="top">
									<td style="#local.tdStyle#width:18px;" nowrap>#local.qryNonPostedBatches.currentrow#.</td>
									<td style="#local.tdStyle#">
										<a href="#local.mc_siteInfo.scheme#://#local.mc_siteinfo.mainhostname#/?pg=admin&jumpToTool=BatchAdmin%7Clist%7CviewBatch&bid=#local.qryNonPostedBatches.batchID#">#local.qryNonPostedBatches.batchName#</a>
										<br/>#local.qryNonPostedBatches.profileName#
									</td>
									<td style="#local.tdStyle#padding-left:4px;" nowrap>
										Dep Date: #dateFormat(local.qryNonPostedBatches.depositDate,"m/d/yyyy")#
										<br/>Status: #local.qryNonPostedBatches.status#
									</td>
								</tr>
							</cfloop>
							</table>							
							</cfoutput>
						</cfif>
					</cfsavecontent>

					<cfsavecontent variable="local.orgEmailContent_outOfOrderInvoices">
						<cfif local.qryOutOfOrderInvoices.recordcount>
							<cfoutput>
							<div style="#local.headingStyle#">Out Of Order Invoices</div>
							<div>
								The following members have invoices due that are <b>out of order</b> based on your settings.
								You may need to deallocate/reallocate funds accordingly.
							</div>
							<br/>
							<table style="width:600px;">
							<tr>
								<td style="#local.thStyle#" colspan="2"><b>Member</b></td>
								<td style="#local.thStyle#padding-left:4px;"><b>MemberNumber</b></td>
							</tr>
							<cfloop query="local.qryOutOfOrderInvoices">
								<tr valign="top">
									<td style="#local.tdStyle#width:18px;" nowrap>#local.qryOutOfOrderInvoices.currentrow#.</td>
									<td style="#local.tdStyle#"><a href="#local.mc_siteInfo.scheme#://#local.mc_siteinfo.mainhostname#/?pg=admin&jumpToTool=MemberAdmin%7Csearch%7Cedit&memberID=#local.qryOutOfOrderInvoices.memberID#&tab=transactions">#local.qryOutOfOrderInvoices.memberName#</a></td>
									<td style="#local.tdStyle#padding-left:4px;">
										#local.qryOutOfOrderInvoices.memberNumber#
									</td>
								</tr>
							</cfloop>
							</table>
							</cfoutput>							
						</cfif>
					</cfsavecontent>

					<cfsavecontent variable="local.orgEmailContent_InvProfAllocViolations">
						<cfif local.qryInvProfAllocViolations.recordcount>
							<cfoutput>
							<div style="#local.headingStyle#">Invoice Profile / Payment Profile Violations</div>
							<div>
								The following allocations are tied to payment profiles that violate the constraint set on invoice profiles.
							</div>
							<br/>
							<table style="width:600px;">
							<tr>
								<td style="#local.thStyle#" colspan="2"><b>Allocation</b></td>
							</tr>
							<cfloop query="local.qryInvProfAllocViolations">
								<tr valign="top">
									<td style="#local.tdStyle#width:18px;" nowrap>#local.qryInvProfAllocViolations.currentrow#.</td>
									<td style="#local.tdStyle#">
										On #dateformat(local.qryInvProfAllocViolations.dateRecorded,"m/d/yyyy")# 
										#local.qryInvProfAllocViolations.memberName# (#local.qryInvProfAllocViolations.memberNumber#) 
										allocated #dollarformat(local.qryInvProfAllocViolations.allocAmount)# of #local.qryInvProfAllocViolations.detail# 
										to invoice #local.qryInvProfAllocViolations.invoiceNumber#. 
										Invoice profile #local.qryInvProfAllocViolations.invoiceProfileName# cannot be paid 
										by pay profile #local.qryInvProfAllocViolations.payProfileName#.
									</td>
								</tr>
							</cfloop>
							</table>
							</cfoutput>							
						</cfif>
					</cfsavecontent>

					<cfsavecontent variable="local.orgEmailContent_noNonSurchargePaymentProfile">
						<cfif local.qryNoNonSurchargePaymentProfile.recordcount>
							<cfoutput>
							<div style="#local.headingStyle#">Non-Surcharge Payment Profiles Needed</div>
							<div>
								The following items allow for members to use a surcharge-enabled payment profile without offering a non-surcharge alternative such as cash or check. 
								This is a requirement to implementing surcharge on your site.
							</div>
							<br/>
							<table style="width:600px;">
							<tr>
								<td style="#local.thStyle#" colspan="2"><b>Area</b></td>
							</tr>
							<cfloop query="local.qryNoNonSurchargePaymentProfile">
								<tr valign="top">
									<td style="#local.tdStyle#width:18px;" nowrap>#local.qryNoNonSurchargePaymentProfile.currentrow#.</td>
									<td style="#local.tdStyle#">
										#local.qryNoNonSurchargePaymentProfile.applicationTypeName#: 
										<a href="#local.qryNoNonSurchargePaymentProfile.linkToAppAdmin#">#local.qryNoNonSurchargePaymentProfile.title#</a>
									</td>
								</tr>
							</cfloop>
							</table>
							</cfoutput>							
						</cfif>
					</cfsavecontent>

					<cfsavecontent variable="local.orgEmailContent">
						<cfoutput>
						<html>
						<head>
							<title>Accounting Issues Report</title>
						</head>
						<body>
						<div style="#local.pageStyle#">
							<div>
								As of #dateformat(now(),"dddd, mmmm d, yyyy")# at #timeformat(now(),"h:mm tt")# the following accounting issues exist on your website. 
							</div>
							<br/>

							<cfif len(trim(local.orgEmailContent_noNonSurchargePaymentProfile))>
								#local.orgEmailContent_noNonSurchargePaymentProfile#
								<br/><br/>
							</cfif>
							<cfif len(trim(local.orgEmailContent_batches))>
								#local.orgEmailContent_batches#
								<br/><br/>
							</cfif>
							<cfif len(trim(local.orgEmailContent_captured))>
								#local.orgEmailContent_captured#
								<br/><br/>
							</cfif>
							<cfif len(trim(local.orgEmailContent_invoices))>
								#local.orgEmailContent_invoices#
								<br/><br/>
							</cfif>
							<cfif len(trim(local.orgEmailContent_outOfOrderInvoices))>
								#local.orgEmailContent_outOfOrderInvoices#
								<br/><br/>
							</cfif>
							<cfif len(trim(local.orgEmailContent_InvProfAllocViolations))>
								#local.orgEmailContent_InvProfAllocViolations#
								<br/><br/>
							</cfif>

							<div><b>Questions?</b><br/>Contact <NAME_EMAIL> if you have any questions about this report.</div>
							<br/>
							<div><b>E-mail Removal</b><br/>If you no longer wish to receive these types of emails, you can adjust the 
							recipient list by logging into your website's Control Panel, clicking on the Accounting tab, and modifying this 
							report's recipient address in the "Accounting Settings" tool.</div>
						</div>
						</body>
						</html>
						</cfoutput>
					</cfsavecontent>
					
					<cfscript>
						local.arrEmailTo = [];
						local.toEmailArr = listToArray(local.qryOrgInfo.accountingEmail,';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
					</cfscript>
					
					<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.qryOrgInfo.orgName, email=local.mc_siteInfo.networkEmailFrom },
						emailto=local.arrEmailTo,
						emailreplyto="<EMAIL>",
						emailsubject="#dateformat(now(),"mmm d, yyyy")#: #local.qryOrgInfo.orgName# Accounting Issues Report",
						emailtitle="Accounting Issues Report",
						emailhtmlcontent=local.orgEmailContent,
						emailAttachments=[],
						siteID=local.mc_siteinfo.siteID,
						memberID=local.mc_siteInfo.sysmemberid,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
						sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID
					)>
				</cfif>

				<cfquery name="local.qryClearQueueItem" datasource="#application.dsn.platformQueue.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrgInfo.orgID#">, 
							@itemID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrgInfo.itemID#">;

						BEGIN TRAN;
							DELETE FROM datatransfer.dbo.tr_reportIssues_openInv WHERE orgID = @orgID;
							DELETE FROM datatransfer.dbo.tr_reportIssues_nonPostedBatches WHERE orgID = @orgID;
							DELETE FROM datatransfer.dbo.tr_reportIssues_outOfOrderInv WHERE orgID = @orgID;
							DELETE FROM datatransfer.dbo.tr_reportIssues_invProfAllocViolations WHERE orgID = @orgID;
							DELETE FROM datatransfer.dbo.tr_reportIssues_flaggedTransactions WHERE orgID = @orgID;
							DELETE FROM datatransfer.dbo.tr_reportIssues_noNonSurchargePaymentProfile WHERE orgID = @orgID;
							DELETE FROM dbo.queue_acctIssuesReport WHERE itemID = @itemID;
						COMMIT TRAN;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfset local.success = true>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.success = false>
			</cfcatch>
			</cftry>
		</cfloop>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_acctIssuesReport;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>
	
</cfcomponent>