<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">
	
	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfscript>
			var local = structnew();
			local.siteCode = "TXRX";
			local.siteID = application.objSiteInfo.getSiteInfo(local.siteCode).siteID;
			local.sysMemberID = application.objSiteInfo.getSiteInfo(local.siteCode).sysmemberid;
			local.siteResourceID = application.objSiteInfo.getSiteInfo(local.siteCode).siteSiteResourceID;
		</cfscript>
		
		<!--- get events which ended 30 days ago --->
		<cfquery name="local.qryEvents" datasource="#application.dsn.memberCentral.dsn#">
			SET nocount on

			DECLARE @compareDate datetime, @siteID int;
			SELECT @compareDate = dateadd(dd,-30,getdate());
			SELECT @siteID = dbo.fn_getSiteIDFromSiteCode('TXRX');
			
			select e.eventID
			from dbo.ev_events as e 
			inner join dbo.ev_times as t on e.eventID = t.eventID and e.status <> 'D' and e.siteID = @siteID
			inner join dbo.timezones as tz on tz.timezoneid = t.timezoneID and t.timezoneid = 6
			where e.eventid in (
				select distinct eventID from customApps.dbo.TXRX_Evaluation_Responses
			)
			and convert(varchar(10), @compareDate, 120) = convert(varchar(10), t.endTime, 120);
		</cfquery>

		<cfoutput query="local.qryEvents">
			<cfset local.report = getReportContent(eventID=local.qryEvents.eventID)>
			<cfset local.download_filenameShown = "SummaryEvaluationReport.pdf">
			<cfset local.download_SubFolder = "mc_" & CreateUUID() & CreateUUID()>
			<cfset local.download_HoldingFolder = "#application.paths.SharedTempNoWeb.path##local.download_SubFolder#">
			<cfset local.download_HoldingPath = "#local.download_HoldingFolder#/#local.download_filenameShown#">
			<cfif NOT DirectoryExists("#local.download_HoldingFolder#")>
				<cfdirectory action="CREATE" directory="#local.download_HoldingFolder#">
			</cfif>
			<cfdocument format="PDF" filename="#local.download_HoldingPath#" margintop="1" marginbottom="1" marginright="1" marginleft="1" backgroundvisible="Yes" scale="100">
				<cfoutput>#local.report.content#</cfoutput>
			</cfdocument>

			<cfset local.emailAttachments = []>
			<cfif FileExists(local.download_HoldingPath)>
				<cfset local.emailAttachments = [{ file=local.download_filenameShown, folderpath=local.download_HoldingFolder }]>
			</cfif>
			
			<cfsavecontent variable="local.emailContent">
				<div>Find the evaluation report attached for #local.report.eventTitle# <cfif local.report.eventDate NEQ ""> on #local.report.eventDate#</cfif>.</div>
			</cfsavecontent>
			
			<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="MemberCentral", email='<EMAIL>' },
				emailto=[{ name:'Lisa Goerlitx', email:"<EMAIL>" }],
				emailreplyto="<EMAIL>",
				emailsubject="Evaluation Reports",
				emailtitle="Evaluation Report",
				emailhtmlcontent=local.emailContent,
				emailAttachments=local.emailAttachments,
				siteID=local.siteID,
				memberID=local.sysMemberID ,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="CUSTOMSCHEDTASK"),
				sendingSiteResourceID=local.siteResourceID
			)>
		</cfoutput>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.qryEvents.recordcount)>
	</cffunction>
	<cffunction name="getReportContent" access="private" returntype="struct">
		<cfargument name="eventID" type="numeric" required="true">
		
		<cfset var local = structnew()>
		<cfset local.strReturn = structnew()>
		
		<cfstoredproc datasource="#application.dsn.customApps.dsn#" procedure="txrx_conferenceEvaluations" result="local.spResult">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">
			<cfprocresult resultset="1" name="local.qryReport">
			<cfprocresult resultset="2" name="local.qrySection1">
			<cfprocresult resultset="3" name="local.qrySection2">
			<cfprocresult resultset="4" name="local.qryDidPromote">
			<cfprocresult resultset="5" name="local.qryNotMetLearningObjectives">
			<cfprocresult resultset="6" name="local.qryThingsDifferently">
			<cfprocresult resultset="7" name="local.qryActivityEnjoyedMost">
			<cfprocresult resultset="8" name="local.qryActivityEnjoyedLeast">
			<cfprocresult resultset="9" name="local.qryAdditionalComments">
			<cfprocresult resultset="10" name="local.qryHowCommitted">
			<cfprocresult resultset="11" name="local.qryActivitySpeaker">
		</cfstoredproc>	


		<cfset local.strReturn.eventTitle = local.qryReport.eventTitle>
		<cfif local.qryReport.eventDate NEQ "">
			<cfset local.strReturn.eventDate = DateFormat(local.qryReport.eventDate, "mm/dd/yyyy")>
		<cfelse>
			<cfset local.strReturn.eventDate = "">
		</cfif>
		<cfsavecontent variable="local.strReturn.content">
		<cfoutput>
			<div class="tsAppHeading" align="center">Summary of Evaluation Forms</div>
			<br/>
			<div class="tsAppHeading">#local.qryReport.eventTitle#</div>
			<div class="tsAppBodyText">Date Run: #local.qryReport.dateRun#</div>
			<div class="tsAppBodyText">ACPE## #local.qryReport.ACPENumber#</div>
			<div class="tsAppBodyText">#local.qryReport.location#</div>
			<br/>
			<div class="tsAppBodyText"><strong>Total number of attendees: #local.qryReport.numAttendees#</strong></div>
			<div class="tsAppBodyText"><strong>Total number of evaluation forms: #local.qryReport.numResponses#</strong></div>
	
			<br>
			<table class="bodyText" cellpadding="4" cellspacing="0" width="100%">
			<tr>
				<td colspan="2"><strong>I. ACTIVITY EVALUATION</strong></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
					<tr>
						<td>Poor=1 Excellent=5</td>
						<th>&nbsp;</td>
						<th align="center" class="bodyText" width="35">1</th>
						<th align="center" class="bodyText" width="35">2</th>
						<th align="center" class="bodyText" width="35">3</th>
						<th align="center" class="bodyText" width="35">4</th>
						<th align="center" class="bodyText" width="35">5</th>
						<th>&nbsp;</th>
						<th align="center" class="bodyText" width="65">Average</th>
					</tr>
					<cfloop query="local.qrySection1">
					<tr>
						<td>#local.qrySection1.question#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qrySection1.1#</td>
						<td align="center" class="bodyText">#local.qrySection1.2#</td>
						<td align="center" class="bodyText">#local.qrySection1.3#</td>
						<td align="center" class="bodyText">#local.qrySection1.4#</td>
						<td align="center" class="bodyText">#local.qrySection1.5#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qrySection1.average#</td>
					</tr>
					</cfloop>
				</table>
				</td>
			</tr>
			<tr>
				<td colspan="2"><strong>II. IMPACT OF THE ACTIVITY</strong></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
					<tr>
						<td width="35%">A. Information presented:</td>
						<td width="1%">&nbsp;</td>
						<td align="center" class="bodyText">##(yes)</td>
						<td align="center" class="bodyText">%</td>
						<td align="center" class="bodyText"></td>
						<td align="center" class="bodyText"></td>
						<td></td>
					</tr>
					<cfloop query="local.qrySection2" startrow="1" endrow="4">
					<tr>
						<td>&nbsp;&nbsp;&nbsp;&nbsp;#local.qrySection2.question#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qrySection2.numYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctYes#</td>
						<td align="center" class="bodyText"></td>
						<td align="center" class="bodyText"></td>
						<td></td>
					</tr>
					</cfloop>

					<tr><td colspan="7">&nbsp;</td></tr>

					<tr>
						<td></td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText" width="35">Agree</td>
						<td align="center" class="bodyText" width="35">%</td>
						<td align="center" class="bodyText" width="35">Disagree</td>
						<td align="center" class="bodyText" width="35">%</td>
						<td></td>
					</tr>
					<cfloop query="local.qrySection2" startrow="5" endrow="7">
					<tr>
						<td>#local.qrySection2.question#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qrySection2.numYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.numNo#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctNo#</td>
						<td></td>
					</tr>
					</cfloop>
					<cfif local.qryDidPromote.recordcount>
					<tr>
						<td>If you disagreed with question D, please indicate which particular product or company was promoted.</td>
						<td>&nbsp;</td>
						<td colspan='5' class="bodyText">
							<table>
								<cfloop query="local.qryDidPromote">
									<tr><td>#local.qryDidPromote.didPromote#</td></tr>
								</cfloop>
							</table>
						</td>
					</tr>

					<tr>
						<td></td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">Agree</td>
						<td align="center" class="bodyText">%</td>
						<td align="center" class="bodyText">Disagree</td>
						<td align="center" class="bodyText">%</td>
						<td></td>
					</tr>
					</cfif>

					<cfloop query="local.qrySection2" startrow="8" endrow="8">
					<tr>
						<td>#local.qrySection2.question#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qrySection2.numYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.numNo#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctNo#</td>
						<td></td>
					</tr>
					</cfloop>

					<cfif local.qryNotMetLearningObjectives.recordcount>
					<tr>
						<td>If you disagreed with question E, please indicate which learning objectives were not met</td>
						<td>&nbsp;</td>
						<td colspan='5' class="bodyText">
							<table>
								<cfloop query="local.qryNotMetLearningObjectives">
									<tr><td>#local.qryNotMetLearningObjectives.NotMetLearningObjectives#</td></tr>
								</cfloop>
							</table>
						</td>
					</tr>

					<tr>
						<td></td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">Agree</td>
						<td align="center" class="bodyText">%</td>
						<td align="center" class="bodyText">Disagree</td>
						<td align="center" class="bodyText">%</td>
						<td></td>
					</tr>
					</cfif>

					<cfloop query="local.qrySection2" startrow="9" endrow="9">
					<tr>
						<td>#local.qrySection2.question#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qrySection2.numYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctYes#</td>
						<td align="center" class="bodyText">#local.qrySection2.numNo#</td>
						<td align="center" class="bodyText">#local.qrySection2.pctNo#</td>
						<td></td>
					</tr>
					</cfloop>
					
					<cfif local.qryThingsDifferently.recordcount>
					<tr><td colspan="7">&nbsp;</td></tr>
					<tr>
						<td valign="top">List two things you will do differently</td>
						<td>&nbsp;</td>
						<td colspan="5">
							<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
								<cfset local.oddeven = 0>											
								<cfloop query="local.qryThingsDifferently">
									<cfset local.oddeven = local.oddeven + 1>				
									<tr <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>><td>#local.qryThingsDifferently.ThingsDifferently#</td></tr>
								</cfloop>
							</table>
						</td>
					</tr>
					</cfif>
				</table>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
					<tr>
						<td>#local.qryHowCommitted.question#</td>
						<th>&nbsp;</td>
						<th align="center" class="bodyText" width="35">1</th>
						<th align="center" class="bodyText" width="35">2</th>
						<th align="center" class="bodyText" width="35">3</th>
						<th align="center" class="bodyText" width="35">4</th>
						<th align="center" class="bodyText" width="35">5</th>
						<th>&nbsp;</th>
						<th align="center" class="bodyText" width="65">Average</th>
					</tr>
					<cfloop query="local.qryHowCommitted">
					<tr>
						<td>&nbsp;</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qryHowCommitted.1#</td>
						<td align="center" class="bodyText">#local.qryHowCommitted.2#</td>
						<td align="center" class="bodyText">#local.qryHowCommitted.3#</td>
						<td align="center" class="bodyText">#local.qryHowCommitted.4#</td>
						<td align="center" class="bodyText">#local.qryHowCommitted.5#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qryHowCommitted.average#</td>
					</tr>
					</cfloop>
				</table>
				</td>
			</tr>			
			<tr>
				<td colspan="2"><strong>III. ACTIVITY COMMENTS</strong></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
					<tr>
						<td valign="top">1. What aspects of this live training activity did you enjoy most?</td>
						<td>&nbsp;</td>
						<td class="bodyText">
							<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
								<cfset local.oddeven = 1>								
								<cfloop query="local.qryActivityEnjoyedMost">
									<cfset local.oddeven = local.oddeven + 1>
									<tr <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>><td>#local.qryActivityEnjoyedMost.ActivityEnjoyedMost#</td></tr>
								</cfloop>
							</table>
						</td>
					</tr>
					<tr><td colspan="3"></td></tr>	
					<tr>
						<td valign="top">2. What aspects of this live training activity did you enjoy least?</td>
						<td>&nbsp;</td>
						<td class="bodyText">
							<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
								<cfset local.oddeven = 1>								
								<cfloop query="local.qryActivityEnjoyedLeast">
									<cfset local.oddeven = local.oddeven + 1>
									<tr <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>><td>#local.qryActivityEnjoyedLeast.ActivityEnjoyedLeast#</td></tr>
								</cfloop>
							</table>
						</td>
					</tr>						
					<tr><td colspan="3">&nbsp;</td></tr>	
					<tr>
						<td valign="top">3. Please provide any additional comments about the seminar</td>
						<td>&nbsp;</td>
						<td class="bodyText">
							<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
								<cfset local.oddeven = 1>								
								<cfloop query="local.qryAdditionalComments">
									<cfset local.oddeven = local.oddeven + 1>									
									<tr <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>><td>#local.qryAdditionalComments.AdditionalComments#</td></tr>
								</cfloop>
							</table>
						</td>
					</tr>						
					</table>
				</td>
			</tr>			
			<tr>
				<td colspan="2"><strong>IV. ACTIVITY SPEAKER</strong></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellpadding="2" cellspacing="0" class="tsAppBodyText">
					<tr>
						<td colspan="9"><strong>Speaker Name: #local.qryReport.speakerName#</strong></td>
					</tr>
					<tr>
						<td>Poor=1 Excellent=5</td>
						<th>&nbsp;</td>
						<th align="center" class="bodyText" width="35">1</th>
						<th align="center" class="bodyText" width="35">2</th>
						<th align="center" class="bodyText" width="35">3</th>
						<th align="center" class="bodyText" width="35">4</th>
						<th align="center" class="bodyText" width="35">5</th>
						<th>&nbsp;</th>
						<th align="center" class="bodyText" width="65">Average</th>
					</tr>
					<cfloop query="local.qryActivitySpeaker">
					<tr>
						<td>#local.qryActivitySpeaker.question#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qryActivitySpeaker.1#</td>
						<td align="center" class="bodyText">#local.qryActivitySpeaker.2#</td>
						<td align="center" class="bodyText">#local.qryActivitySpeaker.3#</td>
						<td align="center" class="bodyText">#local.qryActivitySpeaker.4#</td>
						<td align="center" class="bodyText">#local.qryActivitySpeaker.5#</td>
						<td>&nbsp;</td>
						<td align="center" class="bodyText">#local.qryActivitySpeaker.average#</td>
					</tr>
					</cfloop>
				</table>
				</td>
			</tr>			
			</table>
		</cfoutput>
		</cfsavecontent>
			
		<cfreturn local.strReturn>
	</cffunction>

</cfcomponent>