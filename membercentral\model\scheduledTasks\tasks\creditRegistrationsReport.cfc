<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">
	
	<!--- These are the reports to run. No other reports should run until they are defined here. --->
	<!--- These orgcodes MUST be linked in the tblCreditSponsors table or they will be ignored! --->
	<cfset variables.strSponsors = {
		TAGD = "<EMAIL>;<EMAIL>;<EMAIL>"
		}>

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>
		
		<cfquery name="local.qryCreditSponsors" datasource="#application.dsn.TLASITES_SEMINARWEB.dsn#">
			SELECT sponsorID, sponsorName, orgCode
			FROM dbo.tblCreditSponsors
			WHERE orgCode IS NOT NULL
			AND orgCode IN (<cfqueryparam value="#StructKeyList(variables.strSponsors)#" cfsqltype="CF_SQL_VARCHAR" list="yes">)
			ORDER BY sponsorID
		</cfquery>
		
		<cfset local.taskStartDate = getLastRunDate(taskCFC=arguments.strTask.taskCFC, defaultDate="#month(now())#/1/#year(now())#")>
		<cfset local.taskEndDate = dateAdd('d',-1,now())>

		<cfloop query="local.qryCreditSponsors">
			<cftry>
				<cfset local.tmpStrSponsor = { sponsorID=local.qryCreditSponsors.sponsorID, sponsorName=local.qryCreditSponsors.sponsorName, orgcode=local.qryCreditSponsors.orgcode }>
				<cfset buildEmail(strSponsor=local.tmpStrSponsor, taskStartDate=local.taskStartDate, taskEndDate=local.taskEndDate)>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.tmpStrSponsor)>
			</cfcatch>
			</cftry>
		</cfloop>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.qryCreditSponsors.recordCount)>
	</cffunction>
	
	<cffunction name="buildEmail" access="private" returntype="void" output="no">
		<cfargument name="strSponsor" type="struct" required="yes">
		<cfargument name="taskStartDate" type="date" required="yes">
		<cfargument name="taskEndDate" type="date" required="yes">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="sw")>
		<cfset local.reportFileName = "RegistrationsReport.csv">
		
		<cfswitch expression="#arguments.strSponsor.orgcode#">
			<cfcase value="TAGD">
				<cfstoredproc procedure="TAGD_DARegistrationsReport" datasource="#application.dsn.TLASITES_SEMINARWEB.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.taskStartDate#">
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.taskEndDate#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\#local.reportFileName#">
					<cfprocresult name="local.qryCompleteCount" resultset="1">
					<cfprocresult name="local.qryColumns" resultset="2">
				</cfstoredproc>
			</cfcase>
		</cfswitch>

		<cfset local.fields = valueList(local.qryColumns.COLUMN_NAME)>
		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
					
		<cfif FileExists("#local.strFolder.folderPath#/#local.reportFileName#") AND local.qryCompleteCount.returnCount gt 0>
			<cffile action="READ" file="#local.strFolder.folderPath#/#local.reportFileName#" variable="local.csvText">
			<cffile action="WRITE" addnewline="yes" file="#local.strFolder.folderPath#/#local.reportFileName#" output="#ListQualify(local.fields,chr(34))##chr(13)##chr(10)##local.csvText#" fixnewline="no">

			<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(arguments.strSponsor.orgCode)>

			<cfsavecontent variable="local.emailcontent">
				<cfoutput>
				#local.mc_siteinfo.orgname#:<br /><br />
				<cfif dateformat(arguments.taskStartDate,"m/d/yyyy") eq dateformat(arguments.taskEndDate,"m/d/yyyy")>
					Report for #dateformat(arguments.taskStartDate,"m/d/yyyy")#
				<cfelse>
					Report for #dateformat(arguments.taskStartDate,"m/d/yyyy")# - #dateformat(arguments.taskEndDate,"m/d/yyyy")#
				</cfif>
				<br /><br />
				Attached is a CSV file with the registrations for your On-Demand! seminars.<br /> 
				<br />
				If you have any questions regarding this list, please contact us.<br />
				<br />
				Thanks,<br />
				SeminarWeb Support
				</cfoutput>
			</cfsavecontent>

			<cfscript>
				local.arrEmailTo = [];
				local.toEmailArr = listToArray(variables.strSponsors[arguments.strSponsor.orgCode],';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
			</cfscript>

			<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name='SeminarWeb', email='<EMAIL>' },
				emailto=local.arrEmailTo,
				emailreplyto="<EMAIL>",
				emailsubject="SeminarWeb Registrant On-Demand Data - #arguments.strSponsor.sponsorName#",
				emailtitle='SeminarWeb Registrant On-Demand Data',
				emailhtmlcontent=local.emailContent,
				<!--- Generate download link instead of attachment --->
				<cfset local.objReportHelper = CreateObject("component","membercentral.model.reports.report")>
				<cfset local.reportDocResult = local.objReportHelper.generateScheduledReportDocument(
					siteID=local.mc_siteInfo.siteID,
					siteCode=local.mc_siteInfo.siteCode,
					orgCode=local.mc_siteInfo.orgCode,
					reportFilePath=local.strFolder.folderPath & "/" & local.reportFileName,
					reportFileName=local.reportFileName,
					reportTitle="Credit Registrations Report",
					reportDescription="CSV report of credit registrations",
					memberID=local.mc_siteInfo.sysMemberID
				)>
				<cfif local.reportDocResult.success>
					<cfset local.emailContent = local.emailContent & local.reportDocResult.downloadHTML>
				<cfelse>
					<cfset local.emailContent = local.emailContent & "<p>Error generating download link for report.</p>">
				</cfif>
				siteID=local.mc_siteinfo.siteID,
				memberID=local.mc_siteInfo.sysmemberid,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
				sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID
			)>
		</cfif>
	</cffunction>

</cfcomponent>