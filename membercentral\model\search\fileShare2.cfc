<cfcomponent extends="Bucket">

	<cfset variables.thisBucketTypeID = 34>
	<cfset variables.thisBucketType = "FileShare2">
	<cfset variables.thisBucketCartItemTypeID = 0> <!--- ignored --->
	<cfset variables.thisBucketMaxPerPage = 10>
	<cfset variables.thisBucketMaxShown = 10000>
	<cfset variables.thisBaseLink = "">

	<cffunction name="showHeader" access="private" output="false" returntype="string">
		<cfargument name="bucketID"  type="numeric" required="yes">
		<cfargument name="bucketName" type="string" required="yes">
		<cfargument name="bucketText" type="string" required="yes">
		<cfargument name="applicationInstanceName" type="string" required="yes">
		<cfargument name="overrideFSID"  type="numeric" required="yes">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		<cfset	local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		<cfset	local.settingsStruct = prepSettings(local.qryBucketInfo, overrideFSID)>
		<cfset local.appRightsStruct =	application.objSiteResource.buildRightAssignments(siteResourceID=local.settingsStruct.siteResourceID,memberID=local.settingsStruct.memberID,siteID=local.settingsStruct.siteID)>

		<cfset local.header = "">
		
		<cfsavecontent variable="local.header">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/fileshare2/header.cfm">
		</cfsavecontent>

		<cfreturn local.header>
	</cffunction>

	<cffunction name="showNotAllowed" access="private" output="no" returntype="struct">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="bucketName" required="yes" type="string">
		<cfargument name="accessDeniedMessage" required="yes" type="string">
		<cfargument name="includeBucketCount" required="no" type="boolean" default="true">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>
		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/fileshare2/notAllowed.cfm">
		</cfsavecontent>
		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfif arguments.includeBucketCount>
			<cfset local.strCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
			<cfset StructInsert(local.returnStruct,"itemcount",local.strCount.itemCount)>
		<cfelse>
			<cfset StructInsert(local.returnStruct,"itemcount",-1)>
		</cfif>
		<cfset StructInsert(local.returnStruct,"success",true)>
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="showSearchNotAccepted" access="private" returntype="struct" output="no" hint="returns not accepted text">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="bucketName" type="string" required="yes">
		<cfargument name="bucketText" type="string" required="yes">
		<cfargument name="applicationInstanceName" type="string" required="yes">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/fileshare2/searchNotAccepted.cfm">
		</cfsavecontent>
		<cfset local.stResults = ReReplace(local.stResults,'\s{2,}','','ALL')>

		<cfset local.returnStruct = StructNew()>
		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfset StructInsert(local.returnStruct,"itemcount",-1)>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="showSearchForm" access="public" output="false" returntype="string">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="searchID" required="no" type="numeric" default="0">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		<cfscript>
			local.qryBucketInfo = getBucketInfo(arguments.bucketID);
			local.settingsStruct = prepSettings(local.qryBucketInfo);
			local.strSearchForm = prepSearchForSearchForm(arguments.searchID,arguments.bucketID);
			
			local.pageInfo = structNew();
			local.pageInfo.name = local.qryBucketInfo.bucketName;
			local.pageInfo.settings = local.settingsStruct;
			
			if (local.settingsStruct.fileShareID NEQ "")
			{
				local.fileShares = getFileShares(local.settingsStruct.siteID,local.settingsStruct.memberID, local.settingsStruct.fileShareID, local.settingsStruct.restrictToFileshares);
			} else {
				local.fileShares = getFileShares(local.settingsStruct.siteID,local.settingsStruct.memberID, 0, local.settingsStruct.restrictToFileshares);
			}
			local.siteResourceID = 0;
			if (local.fileShares.siteResourceID NEQ "")
				local.siteResourceID = local.fileShares.siteResourceID;
		</cfscript>
		
		<cfif not variables.initRun>
			<cfset init()>
		</cfif>
		
		<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset local.bucketText = getBrowseText(local.settingsStruct.qryCategoryTrees)/>
			<cfset showCommonJS(bucket=variables.thisBucketType, bucketID=arguments.bucketID, viewDirectory=arguments.viewDirectory)>

			<!--- show custom JS --->
			<cfsavecontent variable="local.js">
				<cfoutput>
					<style type="text/css">
						.ui-multiselect-checkboxes label {  font-size: 8pt!important; }
					</style>
					<script type="text/javascript">
						var dispDynamicOptFilters = false;
						var isAdvFiltersVisible = false;

						function showOptionalFilters(appInstID, initialLoad) {
							var optionalFilters = function(f) {
								if (f.success && f.success.toLowerCase() == 'true'){
									$('##optionalFilters').html(f.htmlcontent);
									dispDynamicOptFilters = true;
									if(isAdvFiltersVisible)
										$('##optionalFilters').show();
									if(initialLoad && !isAdvFiltersVisible && hasAdvFilterValues())
										$('span.advFilters').trigger('click');
								}
							};
							var theParams = { appInstanceID:appInstID, memberID:#local.settingsStruct.memberID#,
									s_jurisdiction:'#local.strSearchForm.s_jurisdiction#', s_aut:'#local.strSearchForm.s_aut#',
									s_fname:'#local.strSearchForm.s_fname#',  s_lname:'#local.strSearchForm.s_lname#', viewDirectory:'#arguments.viewDirectory#' };
							TS_AJX('SBT34','getOptionalFilters',theParams,optionalFilters,optionalFilters,10000,optionalFilters);
						}
						function hasAdvFilterValues() {
							var theForm = document.forms["frms_Search"];
							b_trimAllTxt(theForm);
							var passCheck = false;
							var basicFilters = "s_applicationinstanceid,s_key_all,s_key_phrase,s_key_one,s_key_x";
							var isAdvFilter = false;
							for (i=0; i < theForm.elements.length; i++) {
								var isAdvFilter = basicFilters.indexOf(theForm.elements[i].name) == -1;
								if (theForm.elements[i].type == 'text' && isAdvFilter && theForm.elements[i].value.length > 0) { passCheck = true; break; }
								
								if ((theForm.elements[i].type == 'select-multiple' || theForm.elements[i].type == 'select-one') && isAdvFilter) {
									for (var x=0; x < theForm.elements[i].length; x++) {
										if (theForm.elements[i].options[x].selected) {
											tmp = theForm.elements[i].options[x].value;
											tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
											if (tmp != 0) { passCheck = true; break; }
										}
									}
								}
							}
							if (!passCheck) {
								return false;
							}
							return true;
						}

						$(document).ready(function(){
							mca_setupDatePickerRangeFields('s_datefrom','s_dateto');
							mca_setupDatePickerRangeFields('s_depodatefrom','s_depodateto');
							mca_setupDatePickerRangeFields('s_postdatefrom','s_postdateto');
							
							<cfif local.settingsStruct.fileShareID EQ "">
								$("##s_applicationinstanceid").multiselect({ 
									selectedList: 1,
								}); 
								var arrChecked = $("##s_applicationinstanceid").multiselect("getChecked");
								if (arrChecked.length == 1)
								{
									showOptionalFilters(arrChecked[0].value, 1);
								}

								$("##s_applicationinstanceid").on("multiselectclick", function(event, ui) {
									var arrChecked = $("##s_applicationinstanceid").multiselect("getChecked");
									if (arrChecked.length == 1) {
										showOptionalFilters(arrChecked[0].value, 0);
									}
									else {
										dispDynamicOptFilters = false;
										$("##optionalFilters").html('').hide();
									}
								});
							</cfif>
							$('.advFilters').on('click',function(){
								if($('.advancedFiltersWrapper').css('display') == 'none'){
									var dispElements = dispDynamicOptFilters ? '.advancedFiltersWrapper,.advancedFiltersWrapperDynamic' : '.advancedFiltersWrapper';
									$(dispElements).show('100',setAdvExpandIcon);
								}
								else
									$('.advancedFiltersWrapper,.advancedFiltersWrapperDynamic').hide('100',setAdvExpandIcon);
							});
							var isAutoExpandAdvFilters = #local.settingsStruct.autoExpandAdvFilters EQ 1#;
							var refineSearchID = #arguments.searchID#;
							if(isAutoExpandAdvFilters || (refineSearchID > 0 && hasAdvFilterValues())){
								$('span.advFilters').trigger('click');
							}
						});
					</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.js#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="/views/search/commonSearchFormJS.cfm">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/fileshare2/searchForm.cfm">
		</cfsavecontent>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="prepSearchForSearchForm" access="private" returntype="struct" output="no" hint="parses the searchXML and populates search form">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfset var local = structNew()>
		<cfscript>
		local.returnStruct = StructNew();
		if (arguments.searchID gt 0) {

			local.qryBucketInfo = getBucketInfo(arguments.bucketID);
			local.settingsStruct = prepSettings(local.qryBucketInfo);
			// convert origin searchXML to bucket searchXML
			local.searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID);
			// read/clean from xml
			local.returnStruct.s_applicationinstanceid = local.searchXML.search["s_applicationinstanceid"].xmlText;

			if (local.settingsStruct.hasManyFileshares eq false AND local.returnStruct.s_applicationinstanceid neq local.settingsStruct.applicationInstanceId) {
				// this means this search bucket is a different fileshare.  Override the instance to search
				local.returnStruct.s_applicationinstanceid = local.settingsStruct.applicationInstanceId;
				
				// Override the jursdiction expanded exists
				if ( structKeyExists(local.searchXML.search["s_jurisdiction"].xmlAttributes, "expanded") ){
					local.s_jurisdiction_key_one = local.searchXML.search["s_docflags"].xmlText;
				}
			}
			
			local.returnStruct.s_category = local.searchXML.search["s_category"].xmlText;

			local.returnStruct.s_docflags = local.searchXML.search["s_docflags"].xmlText;
			local.returnStruct.s_jurisdiction = local.searchXML.search["s_jurisdiction"].xmlText;
			
			local.returnStruct.s_fname = local.searchXML.search["s_fname"].xmlText;
			local.returnStruct.s_lname = local.searchXML.search["s_lname"].xmlText;
			local.returnStruct.s_aut = local.searchXML.search["s_aut"].xmlText;			
			
			local.returnStruct.s_depodatefrom = prepareSearchDate(local.searchXML.search["s_depodatefrom"].xmlText);
			local.returnStruct.s_depodateto = prepareSearchDate(local.searchXML.search["s_depodateto"].xmlText);

			local.returnStruct.s_postdatefrom = prepareSearchDate(local.searchXML.search["s_postdatefrom"].xmlText);
			local.returnStruct.s_postdateto = prepareSearchDate(local.searchXML.search["s_postdateto"].xmlText);

			local.returnStruct.s_datefrom = prepareSearchDate(local.searchXML.search["s_datefrom"].xmlText);
			local.returnStruct.s_dateto = prepareSearchDate(local.searchXML.search["s_dateto"].xmlText);
			local.returnStruct.s_key_one = local.searchXML.search["s_key_one"].xmlText;
			local.returnStruct.s_key_all = local.searchXML.search["s_key_all"].xmlText;
			if (isdefined("local.s_jurisdiction_key_one")) {
				if (local.s_jurisdiction_key_one NEQ '' AND local.returnStruct.s_key_all NEQ '')
					local.returnStruct.s_key_all = local.returnStruct.s_key_all & " " & local.s_jurisdiction_key_one;
				else if (local.s_jurisdiction_key_one NEQ '')
					local.returnStruct.s_key_all = local.s_jurisdiction_key_one;
			}
			local.returnStruct.s_key_phrase = local.searchXML.search["s_key_phrase"].xmlText;
			local.returnStruct.s_key_x 	= local.searchXML.search["s_key_x"].xmlText;
			
		} else {
			local.returnStruct.s_applicationinstanceid = '';
			local.returnStruct.s_category = '';
			local.returnStruct.s_docflags = '';
			local.returnStruct.s_jurisdiction = '';
			local.returnStruct.s_fname = '';
			local.returnStruct.s_lname = '';
			local.returnStruct.s_aut = '';
			
			local.returnStruct.s_depodatefrom = '';
			local.returnStruct.s_depodateto = '';

			local.returnStruct.s_postdatefrom = '';
			local.returnStruct.s_postdateto = '';

			local.returnStruct.s_datefrom = '';
			local.returnStruct.s_dateto = '';
			
			local.returnStruct.s_key_all = '';
			local.returnStruct.s_key_one = '';
			local.returnStruct.s_key_phrase = '';
			local.returnStruct.s_key_x = '';
		}
		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="prepSearch" access="private" returntype="struct" output="no" hint="parses the searchXML and prepares search criteria">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfset var local = structNew()>
		
		<cfscript>
			local.qryBucketInfo = getBucketInfo(arguments.bucketID);
			local.settingsStruct = prepSettings(local.qryBucketInfo);
			// convert origin searchXML to bucket searchXML
			local.searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID);
			local.s_jurisdiction = local.searchXML.search["s_jurisdiction"].xmlText;
			local.s_jurisdiction_key_all = '';
		</cfscript>

		<cfquery name="local.qryValidateCategories" datasource="#application.dsn.membercentral.dsn#">
			select categoryID
			from dbo.cms_categories WITH(NOLOCK)
			WHERE categoryID in (0#local.s_jurisdiction#)
			and categoryTreeID in (0#valuelist(local.settingsStruct.qryCategoryTrees.categoryTreeID)#)
		</cfquery>

		<cfscript>
			if (local.qryValidateCategories.recordCount gt 0) {
				local.s_jurisdiction = valueList(local.qryValidateCategories.categoryID);
			}
			// read/clean from xml
			local.s_applicationinstanceid = local.searchXML.search["s_applicationinstanceid"].xmlText;
			if( NOT len(local.s_applicationinstanceid) ){
				local.s_applicationinstanceid = 0;
			}
			else if (local.settingsStruct.hasManyFileshares eq true) {
				
				local.settingsStruct.applicationInstanceId = local.s_applicationinstanceid;
				local.fsOverride = getFileShareInfoFromAppID(local.s_applicationinstanceid);
				if(local.fsOverride.recordCount){
					local.strSearch.sectionID = local.fsOverride.rootSectionID;
					local.settingsStruct.fileShareID =  local.fsOverride.fileShareID;
					local.settingsStruct.siteResourceId =  local.fsOverride.siteResourceId;
					local.settingsStruct = prepSettings(local.qryBucketInfo, local.fsOverride.fileShareID);
				}

				// Override the jursdiction expanded exists
				if ( structKeyExists(local.searchXML.search["s_jurisdiction"].xmlAttributes, "expanded") ){
					local.s_jurisdiction_key_one = local.searchXML.search["s_docflags"].xmlText;
				}
			}
			else if (local.s_applicationinstanceid neq local.settingsStruct.applicationInstanceId) {
				// this means this search bucket is a different fileshare.  Override the instance to search
				local.s_applicationinstanceid = local.settingsStruct.applicationInstanceId;
				
				// Override the jursdiction expanded exists
				if ( structKeyExists(local.searchXML.search["s_jurisdiction"].xmlAttributes, "expanded") ){
					local.s_jurisdiction_key_one = local.searchXML.search["s_docflags"].xmlText;
				}
			}
			
			local.s_category = local.searchXML.search["s_category"].xmlText;
			if( NOT len(local.s_category) ){
				local.s_category = 0;
			}
			
			local.s_aut = local.searchXML.search["s_aut"].xmlText;
			local.s_fname = prepareSearchString(local.searchXML.search["s_fname"].xmlText);
			local.s_lname = prepareSearchString(local.searchXML.search["s_lname"].xmlText);
			
			local.s_datefrom = prepareSearchDate(local.searchXML.search["s_datefrom"].xmlText);
			local.s_dateto = prepareSearchDate(local.searchXML.search["s_dateto"].xmlText);
			local.s_depodatefrom = prepareSearchDate(local.searchXML.search["s_depodatefrom"].xmlText);
			local.s_depodateto = prepareSearchDate(local.searchXML.search["s_depodateto"].xmlText);
			local.s_docflags = local.searchXML.search["s_docflags"].xmlText;
		
			local.s_postdatefrom = prepareSearchDate(local.searchXML.search["s_postdatefrom"].xmlText);
			local.s_postdateto = prepareSearchDate(local.searchXML.search["s_postdateto"].xmlText);
			local.s_key_one = local.searchXML.search["s_key_one"].xmlText;
			local.s_key_all = local.searchXML.search["s_key_all"].xmlText;

			if (isdefined("local.s_jurisdiction_key_one")) {
				if (local.s_jurisdiction_key_one NEQ '' AND local.s_key_all NEQ '')
					local.s_key_all = local.s_key_all & " " & local.s_jurisdiction_key_one;
				else if (local.s_jurisdiction_key_one NEQ '')
					local.s_key_all = local.s_jurisdiction_key_one;
			}
			local.s_key_all = prepareSearchString(local.s_key_all,false);
			local.s_key_one = prepareSearchString(local.s_key_one,true);
			local.s_key_phrase = preparePhraseString(local.searchXML.search["s_key_phrase"].xmlText);
			local.s_key_x = prepareSearchString(local.searchXML.search["s_key_x"].xmlText);	

			// prepare keywords
			local.keywordsInclude = "";
			if (Len(local.s_key_all)) local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_all,chr(7));
			if (Len(local.s_key_one)) local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_one,chr(7));
			if (Len(local.s_key_phrase)) local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_phrase,chr(7));
			local.keywordsInclude = Replace(local.keywordsInclude,chr(7)," and ","ALL");
	
			local.keywordsExclude = "";
			if (Len(local.s_key_x)) local.keywordsExclude = replaceNoCase(local.s_key_x," and "," or ","all");
	
			if (len(local.keywordsExclude) and len(local.keywordsInclude)) local.finalKeywords = local.keywordsInclude & " and not (" & local.keywordsExclude & ")";
			else if (len(local.keywordsInclude)) local.finalKeywords = local.keywordsInclude;
			else if (len(local.keywordsExclude)) local.finalKeywords = "a and not (" & local.keywordsExclude & ")";
			else  local.finalKeywords = "";
	
			if (len(local.finalKeywords)) {
				local.searchKeysList = 'mcsearchResourceTypeApplicationCreatedDocumentxxx AND mcsearchSiteID#local.settingsStruct.siteID#xxx AND (#listChangeDelims(listMap(valueList(local.settingsStruct.fileShares.rootSectionID),(item) => "mcsearchSectionID#item#xxx")," OR ")#)';
				local.finalFullTextKeywords = "(#local.finalKeywords#) and (#local.searchKeysList#)";
			} 
			else local.finalFullTextKeywords = '';

			local.returnStruct = structNew();
			
			structInsert(local.returnStruct,"searchXML",local.searchXML);
			
			structInsert(local.returnStruct,"fsAppInstanceID",local.s_applicationinstanceid);
			structInsert(local.returnStruct,"sectionID",local.s_category);
			structInsert(local.returnStruct,"settingsStruct",local.settingsStruct);
			structInsert(local.returnStruct,"keywords",local.finalKeywords);
			structInsert(local.returnStruct,"fulltextkeywords",local.finalFullTextKeywords);

			structInsert(local.returnStruct,"fromdate",local.s_datefrom);
			structInsert(local.returnStruct,"todate",local.s_dateto);
			structInsert(local.returnStruct,"depodatefrom",local.s_depodatefrom);
			structInsert(local.returnStruct,"depodateto",local.s_depodateto);
			structInsert(local.returnStruct,"postdatefrom",local.s_postdatefrom);
			structInsert(local.returnStruct,"postdateto",local.s_postdateto);
			
			structInsert(local.returnStruct,"firstName",local.s_fname);
			structInsert(local.returnStruct,"lastName",local.s_lname);
			structInsert(local.returnStruct,"author",local.s_aut);
			structInsert(local.returnStruct,"jurisdiction",local.s_jurisdiction);
			structInsert(local.returnStruct,"docflags",local.s_docflags);
			
			// do i have enough criteria to run a search?
			if (not len(local.s_fname)
					and not len(local.s_lname)
					and not len(local.s_aut)
					and not len(local.s_jurisdiction)
					and not len(local.finalKeywords) 
					and not len(local.s_datefrom) 
					and not len(local.s_dateto)
					and not len(local.s_depodatefrom) 
					and not len(local.s_depodateto)
					and not len(local.s_postdatefrom) 
					and not len(local.s_postdateto)
				)
				{ structInsert(local.returnStruct,"searchAccepted",false); }
			else {structInsert(local.returnStruct,"searchAccepted",true); }

		</cfscript>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="prepSettings" access="private" returntype="struct" output="no" hint="parses the settingsXML into a standardized struct">
		<cfargument name="bucketSettings" required="yes" type="query">
		<cfargument name="overrideFSID" required="no" type="numeric" default="0">
		<cfset var local = StructNew()>
		<cfscript>
			// standardize settings
			local.settingsStruct 	= StructNew();
			local.settingsStruct.appRightsStruct 	= StructNew();
			local.bucketSettingsXML = XMLParse(arguments.bucketSettings.bucketSettings);
	
			// get site/org Info from bucket id
			local.siteInfo = getSiteInfoFromBucketID(arguments.bucketSettings.bucketID);
			// set userMemberID from orgID
			local.settingsStruct.siteID = local.siteInfo.siteID;
			local.settingsStruct.orgID = local.siteInfo.orgID;
			local.settingsStruct.memberID = getMemberIDFromSiteID(local.siteInfo.siteID);
			local.settingsStruct.hasManyFileshares = false;
			
			if (StructKeyExists(local.bucketSettingsXML.settings.XmlAttributes,"fileShareID") and len(local.bucketSettingsXML.settings.XmlAttributes.fileShareID)){
				local.settingsStruct.fileShareID = local.bucketSettingsXML.settings.XmlAttributes.fileShareID;
			}
			else { local.settingsStruct.fileShareID = ""; }

			if (StructKeyExists(local.bucketSettingsXML.settings.XmlAttributes,"restrictToFileshares") and len(local.bucketSettingsXML.settings.XmlAttributes.restrictToFileshares)){
				local.settingsStruct.restrictToFileshares = local.bucketSettingsXML.settings.XmlAttributes.restrictToFileshares;
 				local.settingsStruct.hasManyFileshares = true;
			}
			else { local.settingsStruct.restrictToFileshares = ""; }

			if (StructKeyExists(local.bucketSettingsXML.settings.XmlAttributes,"bucketHeading") and len(local.bucketSettingsXML.settings.XmlAttributes.bucketHeading)){
				local.settingsStruct.bucketHeading = local.bucketSettingsXML.settings.XmlAttributes.bucketHeading;
			}
			else { local.settingsStruct.bucketHeading = ""; }
			
			if (StructKeyExists(local.bucketSettingsXML.settings.XmlAttributes,"categoryID") and len(local.bucketSettingsXML.settings.XmlAttributes.categoryID)){
				local.settingsStruct.categoryID = local.bucketSettingsXML.settings.XmlAttributes.categoryID;
			}
			else { local.settingsStruct.categoryID = ""; }
			
			if (arguments.overrideFSID NEQ 0) {
				local.settingsStruct.fileShareID = arguments.overrideFSID;
			}

			if (local.settingsStruct.fileShareID NEQ ""){
				local.fileShares = getFileShares(local.settingsStruct.siteID,local.settingsStruct.memberID, local.settingsStruct.fileShareID, local.settingsStruct.restrictToFileshares);
			}
			else { local.fileShares = getFileShares(local.settingsStruct.siteID,local.settingsStruct.memberID, 0, local.settingsStruct.restrictToFileshares); }

			local.settingsStruct.fileShares = local.fileShares;

			if (StructKeyExists(local.bucketSettingsXML.settings.XmlAttributes,"autoExpandAdvFilters") and len(local.bucketSettingsXML.settings.XmlAttributes.autoExpandAdvFilters)){
				local.settingsStruct.autoExpandAdvFilters = local.bucketSettingsXML.settings.XmlAttributes.autoExpandAdvFilters;
			}
			else { local.settingsStruct.autoExpandAdvFilters = 0; }

			// Valid values (list, folder);  Default = folder
			if (StructKeyExists(local.bucketSettingsXML.settings.XmlAttributes,"defaultBrowseMode") and len(local.bucketSettingsXML.settings.XmlAttributes.defaultBrowseMode)){
				local.settingsStruct.defaultBrowseMode = local.bucketSettingsXML.settings.XmlAttributes.defaultBrowseMode;
				if (local.settingsStruct.defaultBrowseMode NEQ "folder" AND local.settingsStruct.defaultBrowseMode NEQ "list") { local.settingsStruct.defaultBrowseMode = "folder"; }
			}
			else { local.settingsStruct.defaultBrowseMode = "folder"; }

			local.settingsStruct.siteResourceID = 0;
			local.settingsStruct.applicationInstanceID = 0;
			if (local.fileShares.siteResourceID NEQ ""){
				local.settingsStruct.siteResourceId = local.fileShares.siteResourceID;
				local.settingsStruct.applicationInstanceID = local.fileShares.applicationInstanceID;
			}
			
			// Build the appRights
			local.settingsStruct.appRightsStruct = application.objSiteResource.buildRightAssignments(
																siteResourceID=local.settingsStruct.siteResourceId,
																memberID=local.settingsStruct.memberID, 
																siteID=local.settingsStruct.siteID);

			if (local.fileShares.applicationInstanceID NEQ ""){
				// Determine the base link based on the application instanceid 
				local.objFileshare2 = createObject("component","model.fileshare2.fileshare2");
				local.objFileshare2.appInstanceID = local.fileShares.applicationInstanceID;
				local.objFileshare2.init();
				variables.thisBaseLink = local.objFileshare2.getAppBaseLink();
				local.settingsStruct.fileShareSettings = local.objFileShare2.fileShareSettings;
				local.settingsStruct.objFS2 = local.objFileShare2;
			}
		</cfscript>

		<!--- get category trees --->
		<cfquery name="local.settingsStruct.qryCategoryTrees" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT ct.categoryTreeID, ct.siteID, ct.siteResourceID, ct.categoryTreeName, ct.categoryTreeDesc, ct.categoryTreeCode, ct.controllingSiteResourceID
			FROM dbo.cms_categoryTrees ct 
			INNER JOIN dbo.cms_siteResources sr on sr.siteResourceID = ct.siteResourceID and sr.siteResourceStatusID = 1
			WHERE ct.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.settingsStruct.siteID#">	
			AND ct.controllingSiteResourceID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#local.settingsStruct.siteResourceId#">
			ORDER BY ct.sortOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.settingsStruct>
	</cffunction>

	<cffunction name="getResultsCount" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["itemcount"] = 'N/A'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR 
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR 
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResultsCount(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResultsCount" access="private" returntype="struct" output="no" hint="searches and returns a count">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfscript>
			local.strSearch = prepSearch(arguments.siteID,arguments.searchID,arguments.bucketID);
			local.qryBucketInfo = getBucketInfo(arguments.bucketID);
			local.qryFSOverride = getFileShareInfoFromAppID(local.strSearch.fsAppInstanceID);

			if (local.strSearch.settingsStruct.hasManyFileshares eq true AND len(local.strSearch.fsAppInstanceID) AND isnumeric(local.strSearch.fsAppInstanceID) AND local.strSearch.fsAppInstanceID gt 0 and local.qryFSOverride.recordCount) {
				local.settingsStruct = prepSettings(local.qryBucketInfo, local.qryFSOverride.fileshareID);			
			}
			else {
				local.settingsStruct = prepSettings(local.qryBucketInfo);
			}

			local.returnStruct = StructNew();
			
			if(len(local.strSearch.fromdate)){
				local.fromDate = createDateTime(year(local.strSearch.fromdate),month(local.strSearch.fromdate),day(local.strSearch.fromdate),0,0,0);
			}
			if(len(local.strSearch.todate)){
				local.toDate = createDateTime(year(local.strSearch.todate),month(local.strSearch.todate),day(local.strSearch.todate),23,59,59);
			}

			// More the 1 file share searched but a limited list
			if (local.strSearch.settingsStruct.hasManyFileshares eq true AND len(local.strSearch.fsAppInstanceID) AND NOT isnumeric(local.strSearch.fsAppInstanceID)) {
				local.aaqryFS = getFileShareInfoFromAppID(local.strSearch.fsAppInstanceID);
				local.fsFSIDs = valueList(local.aaqryFS.fileshareID);
				local.settingsStruct.restrictToFileshares = local.fsFSIDs;
			}

			if (local.settingsStruct.fileShareID NEQ "")
			{
				local.fileShares = getFileShares(local.settingsStruct.siteID,local.settingsStruct.memberID, local.settingsStruct.fileShareID, local.settingsStruct.restrictToFileshares);
			} else {
				local.fileShares = getFileShares(local.settingsStruct.siteID,local.settingsStruct.memberID, 0, local.settingsStruct.restrictToFileshares);
			}
			local.fsAppIDs = valueList(local.fileShares.applicationInstanceID);
		</cfscript>

		<cfif NOT local.strSearch.searchAccepted OR NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) OR NOT StructKeyExists(local.settingsStruct.appRightsStruct,'fsDeleteAny') >
			<cfset StructInsert(local.returnStruct,"itemcount",'N/A')>
			<cfset StructInsert(local.returnStruct,"success",true)>
			<cfset saveBucketCount(arguments.searchID,arguments.bucketID,-1)>
		<cfelse>
			<cfset local.cachedItemCount = getCachedBucketCount(arguments.searchID,arguments.bucketID)>
			<cfif local.cachedItemCount gte 0>
				<cfset StructInsert(local.returnStruct,"itemcount",local.cachedItemCount)>
				<cfset StructInsert(local.returnStruct,"success",true)>
			<cfelse>
				<cfif cacheItemCount(arguments.searchID, arguments.bucketID) eq 0>
					<cfset local.requiredCategories = structNew()>
					<cfif len(trim(local.strSearch.jurisdiction))>
						<cfloop query="local.settingsStruct.qryCategoryTrees">
							<cfquery name="local.qryCatIds" datasource="#application.dsn.membercentral.dsn#">
								SET NOCOUNT ON;
								SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

								SELECT c.categoryID
								FROM dbo.cms_categories AS c
								INNER JOIN dbo.cms_categoryTrees as t on c.categoryTreeID = t.categoryTreeID
								INNER JOIN dbo.cms_siteResources sr on sr.siteResourceID = t.siteResourceID and sr.siteResourceStatusID = 1								
								WHERE c.categoryID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#trim(local.strSearch.jurisdiction)#" list="true">)
								AND t.categoryTreeID = <cfqueryparam value="#local.settingsStruct.qryCategoryTrees.categoryTreeID#" cfsqltype="CF_SQL_INTEGER">;

								SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
							</cfquery>
							<cfif local.qryCatIds.recordCount GT 0>
								<cfset local.requiredCategories[local.settingsStruct.qryCategoryTrees.categoryTreeID] = ValueList(local.qryCatIds.categoryID)>
							</cfif>
						</cfloop>
					</cfif>

					<cfset local.viewRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="FileShare2", functionName="view")>
					<cfquery name="local.qryResults2" datasource="#application.dsn.memberCentral.dsn#" result="local.qryStat2">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						declare @siteID int, @memberID int, @viewFunctionID int, @datetime datetime, @searchID int, @bucketID int, @resourceTypeID int;
						set @datetime = getDate();

						IF OBJECT_ID('tempdb..##tblSearchSiteResourceCache') IS NOT NULL
							EXEC('DROP TABLE ##tblSearchSiteResourceCache');
						CREATE TABLE ##tblSearchSiteResourceCache (searchID int, bucketID int, siteResourceID int, rank int, applicationInstanceID int);

						set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
						set @memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strSearch.settingsStruct.memberID#">;
						set @viewFunctionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.viewRFID#">;
						set @searchID = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(arguments.searchID)#">;
						set @bucketID = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(arguments.bucketID)#">;
						select @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedDocument');
						<!--- Clear entries if they exist. --->
						delete from searchMC.dbo.tblSearchSiteResourceCache
						where searchID = @searchID
						and bucketID = @bucketID;

						<cfif len(local.strSearch.keywords)>
							declare	@s varchar(8000), @full_s varchar(8000);
							set @s = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.strSearch.keywords#">;
							set @full_s = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.strSearch.fulltextkeywords#">;

							IF OBJECT_ID('tempdb..##fullTextMatches') IS NOT NULL
								EXEC('DROP TABLE ##fullTextMatches');
							IF OBJECT_ID('tempdb..##fullTextMatches_content') IS NOT NULL
								EXEC('DROP TABLE ##fullTextMatches_content');
							IF OBJECT_ID('tempdb..##fullTextMatches_vmetadata') IS NOT NULL
								EXEC('DROP TABLE ##fullTextMatches_vmetadata');
							IF OBJECT_ID('tempdb..##fullTextMatches_lmetadata') IS NOT NULL
								EXEC('DROP TABLE ##fullTextMatches_lmetadata');
							IF OBJECT_ID('tempdb..##fullTextMatches_categoryPaths') IS NOT NULL
								EXEC('DROP TABLE ##fullTextMatches_categoryPaths');
							CREATE TABLE ##fullTextMatches (documentID int PRIMARY KEY, rank int);
							CREATE TABLE ##fullTextMatches_content (searchkey int PRIMARY KEY, rank int, documentID int);
							CREATE TABLE ##fullTextMatches_vmetadata (searchkey int PRIMARY KEY, rank int, documentID int);
							CREATE TABLE ##fullTextMatches_lmetadata (searchkey int PRIMARY KEY, rank int, documentID int);
							CREATE TABLE ##fullTextMatches_categoryPaths (searchkey int PRIMARY KEY, rank int, documentID int);


							declare @fs2RootSectionIDs TABLE (sectionID int PRIMARY KEY)
							insert into @fs2RootSectionIDs (sectionID)
							VALUES #listMap(valueList(local.settingsStruct.fileShares.rootSectionID),(item) => "(#item#)")#


							insert into ##fullTextMatches_content (searchkey, rank, documentID)
							select search1.[key], search1.rank, sdv.documentID
							from containstable(searchMC.dbo.cms_documentVersions, searchtext, @full_s) search1
							inner join searchMC.dbo.cms_documentVersions sdv 
								on sdv.siteID=@siteID 
								and sdv.resourceTypeID = @resourceTypeID
								and sdv.id = search1.[key] 
							inner join @fs2RootSectionIDs sec
								on sec.sectionID = sdv.sectionID

							insert into ##fullTextMatches_vmetadata (searchkey, rank, documentID)
							select search1.[key], search1.rank, sdv.documentID
							from containstable(membercentral.dbo.cms_documentVersions, *, @s) search1
							inner join searchMC.dbo.cms_documentVersions sdv on sdv.siteID=@siteID and sdv.resourceTypeID=@resourceTypeID and sdv.documentVersionid = search1.[key];

							insert into ##fullTextMatches_lmetadata (searchkey, rank, documentID)
							select distinct search1.[key], search1.rank, sdv.documentID
							from containstable(membercentral.dbo.cms_documentLanguages, *, @s) search1
							inner join searchMC.dbo.cms_documentVersions sdv on sdv.siteID=@siteID and sdv.resourceTypeID = @resourceTypeID and sdv.documentLanguageID = search1.[key];
							
							insert into ##fullTextMatches_categoryPaths (searchkey, rank, documentID)
							select distinct cs.[key], cs.rank, csr.siteResourceID
							from containstable(membercentral.dbo.cms_categories, categoryPath, @full_s) cs
							inner join membercentral.dbo.cms_categorySiteResources csr on csr.categoryID = cs.[key]
							inner join membercentral.dbo.cms_documents d on d.siteResourceID = csr.siteResourceID
							inner join @fs2RootSectionIDs sec on d.sectionID = sec.sectionID;
							
							insert into ##fullTextMatches (documentID, rank)
							select documentID, max(rank) as rank
							FROM (
								select documentID, rank from ##fullTextMatches_content
								UNION
								select documentID, rank from ##fullTextMatches_vmetadata
								UNION
								select documentID, rank from ##fullTextMatches_lmetadata
								UNION
								select documentID, rank from ##fullTextMatches_categoryPaths
							) t2
							group by documentID;
						</cfif>
						
						insert into ##tblSearchSiteResourceCache (searchID, bucketID, siteResourceID, rank, applicationInstanceID)
						SELECT distinct @searchID, @bucketID, documents.siteResourceID as docSiteResourceID,
							<cfif len(local.strSearch.keywords)>searchHits.rank<cfelse>1000</cfif> as rank,
							ai.applicationInstanceID
						FROM dbo.sites s
						INNER JOIN dbo.cms_applicationInstances ai ON ai.siteID = s.siteID
							AND s.siteID = @siteID
						INNER JOIN dbo.fs_fileshare fs ON ai.applicationInstanceID = fs.applicationInstanceID
							AND ai.applicationInstanceID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.fsAppIDs#" list="true">)
						INNER JOIN dbo.cms_siteResources appSR ON ai.siteResourceID = appSR.siteResourceID
							and appsr.siteResourceStatusID = 1
						INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
							and srfrp.siteResourceID = appSR.siteResourceID
							and srfrp.functionID = @viewFunctionID
						INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID and gprp.siteID = @siteID
						INNER JOIN dbo.ams_members rightsMem on rightsMem.groupPrintID = gprp.groupPrintID
							and (rightsMem.memberID = @memberID)						
						INNER JOIN dbo.cms_documents documents ON documents.sectionID = fs.rootSectionID
							AND documents.siteID = @siteID 
							<cfif len(local.strSearch.fromdate)>
								AND documents.dateCreated >= <cfqueryparam value="#local.strSearch.fromdate#" cfsqltype="cf_sql_timestamp">
							</cfif>
							<cfif len(local.strSearch.todate)>
								AND documents.dateCreated <= <cfqueryparam value="#local.strSearch.todate#  23:59:59" cfsqltype="cf_sql_timestamp">
							</cfif>
						INNER JOIN dbo.cms_documentLanguages dl on dl.documentID = documents.documentId
						INNER JOIN dbo.cms_documentVersions dv on dv.documentLanguageID = dl.documentLanguageID and dv.isActive = 1
						<cfif len(local.strSearch.depodatefrom) OR len(local.strSearch.depodateto) OR len(local.strSearch.postdatefrom) OR 
							len(local.strSearch.postdateto) OR len(local.strSearch.author) OR len(local.strSearch.firstName) OR
							len(local.strSearch.lastName)>
							<cfif len(local.strSearch.depodatefrom)>
								AND dv.publicationDate >= <cfqueryparam value="#local.strSearch.depodatefrom#" cfsqltype="cf_sql_timestamp">
							</cfif>
							<cfif len(local.strSearch.depodateto)>
								AND dv.publicationDate <= <cfqueryparam value="#local.strSearch.depodateto# 23:59:59" cfsqltype="cf_sql_timestamp">
							</cfif>
							<cfif len(local.strSearch.postdatefrom)>
								AND dv.dateModified >= <cfqueryparam value="#local.strSearch.postdatefrom#" cfsqltype="cf_sql_timestamp">
							</cfif>
							<cfif len(local.strSearch.postdateto)>
								AND dv.dateModified <= <cfqueryparam value="#local.strSearch.postdateto# 23:59:59" cfsqltype="cf_sql_timestamp">
							</cfif>
							<cfif len(local.strSearch.author)>
								AND dv.author LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#local.strSearch.author#%">
							</cfif>
						</cfif>
						INNER JOIN dbo.cms_siteResources as docSR ON documents.siteResourceID = docSR.siteResourceID
							<cfif local.settingsStruct.appRightsStruct.fsDeleteAny >
								AND docSR.siteResourceStatusID in (1,2)
							<cfelse>
								AND docSR.siteResourceStatusID = 1
							</cfif>
						<cfif len(local.strSearch.firstName) or len(local.strSearch.lastName)>
							INNER JOIN dbo.ams_members contributors ON contributors.memberID = dv.contributorMemberID
							INNER JOIN dbo.ams_members contributors2 ON contributors2.memberID = contributors.activeMemberID
								<cfif len(local.strSearch.firstName)>
									AND contributors2.firstName LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#local.strSearch.firstName#%">
								</cfif>
								<cfif len(local.strSearch.lastName)>
									AND contributors2.lastName LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#local.strSearch.lastName#%">
								</cfif>
						</cfif>
						<cfif local.settingsStruct.categoryID NEQ "">
							INNER JOIN dbo.cms_categorySiteResources csr ON csr.siteResourceID = documents.siteResourceID
								AND csr.categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.settingsStruct.categoryID#">
						</cfif>
						<cfloop index="local.thisKey" list="#structKeyList(local.requiredCategories)#">
							INNER JOIN dbo.cms_categorySiteResources csrtree#local.thisKey# 
								ON csrtree#local.thisKey#.siteResourceID = documents.siteResourceID
								AND csrtree#local.thisKey#.categoryID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.requiredCategories[local.thisKey]#" list="true">)
						</cfloop>
						<cfif len(local.strSearch.keywords)>
							inner join ##fullTextMatches searchHits on searchHits.documentID = documents.documentID
						</cfif>;

						<cfif len(local.strSearch.keywords)>
							IF OBJECT_ID('tempdb..##fullTextMatches') IS NOT NULL
								EXEC('DROP TABLE ##fullTextMatches');
							IF OBJECT_ID('tempdb..##fullTextMatches_content') IS NOT NULL
								EXEC('DROP TABLE ##fullTextMatches_content');
							IF OBJECT_ID('tempdb..##fullTextMatches_vmetadata') IS NOT NULL
								EXEC('DROP TABLE ##fullTextMatches_vmetadata');
							IF OBJECT_ID('tempdb..##fullTextMatches_lmetadata') IS NOT NULL
								EXEC('DROP TABLE ##fullTextMatches_lmetadata');
							IF OBJECT_ID('tempdb..##fullTextMatches_categoryPaths') IS NOT NULL
								EXEC('DROP TABLE ##fullTextMatches_categoryPaths');
						</cfif>

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

						insert into searchMC.dbo.tblSearchSiteResourceCache (searchID, bucketID, siteResourceID, rank, applicationInstanceID)
						select searchID, bucketID, siteResourceID, rank, applicationInstanceID
						from ##tblSearchSiteResourceCache;

						IF OBJECT_ID('tempdb..##tblSearchSiteResourceCache') IS NOT NULL
							EXEC('DROP TABLE ##tblSearchSiteResourceCache');
					</cfquery>
				</cfif>
				
				<cfquery name="local.qryResults" datasource="#application.dsn.searchMC.dsn#" result="local.qryStat">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select count(*) as itemcount
					from dbo.tblSearchSiteResourceCache
					where searchID = <cfqueryparam value="#arguments.searchID#" cfsqltype="CF_SQL_INTEGER">
					and bucketID = <cfqueryparam value="#arguments.bucketID#" cfsqltype="CF_SQL_INTEGER">;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfset saveStats(arguments.searchID,'#variables.thisBucketType#.getResultsCount',local.qryStat.ExecutionTime,local.qryResults.itemCount)>
				<cfset saveBucketCount(arguments.searchID,arguments.bucketID,local.qryResults.itemCount)>
				<cfset StructInsert(local.returnStruct,"itemcount",local.qryResults.itemCount)>
				<cfset StructInsert(local.returnStruct,"success",true)>
			</cfif>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getResults" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">
		<cfargument name="startRow" required="no">
		<cfargument name="sortType" required="no">
		<cfargument name="filter" required="no" default="">
		<cfargument name="postFilter" required="no" default="">
		<cfargument name="viewDirectory" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["errmsg"] = 'Invalid Request'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID)) OR
			(NOT StructKeyExists(arguments, "startRow") OR NOT IsNumeric(arguments.startRow)) OR
			NOT StructKeyExists(arguments, "sortType") OR
			(NOT StructKeyExists(arguments, "viewDirectory") OR NOT ListFind(variables.viewDirectories, arguments.viewDirectory))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResults(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID,
				bucketID=arguments.bucketID, startrow=arguments.startrow, sortType=arguments.sortType,
				filter=arguments.filter, postFilter=arguments.postFilter, viewDirectory=arguments.viewDirectory)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResults" access="private" returntype="struct" output="no" hint="searches and returns a query result">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="startRow" required="yes" type="numeric">
		<cfargument name="sortType" required="yes" type="string">
		<cfargument name="filter" required="yes" type="string">
		<cfargument name="postFilter" required="yes" type="string">
		<cfargument name="viewDirectory" required="yes" type="string">

		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>
		
		<cfscript>
			local.strSearch = prepSearch(arguments.siteID,arguments.searchID,arguments.bucketID);
			local.qryBucketInfo = getBucketInfo(arguments.bucketID);
			local.settingsStruct = prepSettings(local.qryBucketInfo);
			local.returnStruct = StructNew();
			local.bucketText = getBrowseText(local.settingsStruct.qryCategoryTrees);			
		</cfscript>
		<!--- have filter override settings --->
		<cfset local.strSearch.filter_category1 = "0">
		<cfset local.strSearch.filter_category2 = "0">
		<cfset local.strSearch.filter_appInstanceID = "0">
		<cfif len(arguments.postfilter) GT 1 AND arguments.postfilter[1] EQ ','>
			<cfset arguments.postfilter = removeChars(arguments.postfilter, 1, 1)>
		</cfif>

		<cfif listlen(arguments.filter,"|") is 4>
			<cfif getToken(arguments.filter,4,"|") gt 0>
				<cfset local.strSearch.filter_appInstanceID = getToken(arguments.filter,4,"|")>
				<cfset local.strSearch.fsAppInstanceID = local.strSearch.filter_appInstanceID>
				<cfset local.fsOverride = getFileShareInfoFromAppID(local.strSearch.fsAppInstanceID)>
				<cfset local.strSearch.sectionID = local.fsOverride.rootSectionID>
				<cfset local.settingsStruct.fileShareID =  local.fsOverride.fileShareID>
				<cfset local.settingsStruct.siteResourceId =  local.fsOverride.siteResourceId>
				<cfset local.settingsStruct.applicationInstanceID =  local.strSearch.filter_appInstanceID>
				<!---  Override the base link based on the application instanceid --->
				<cfset local.settingsStruct 	= prepSettings(local.qryBucketInfo, local.fsOverride.fileShareID)>

 				<cfset local.objFileshare2 = createObject("component","model.fileshare2.fileshare2")>
				<cfset local.objFileshare2.appInstanceID = local.settingsStruct.applicationInstanceID>
				<cfset local.objFileshare2.init()>
				<cfset variables.thisBaseLink = local.objFileshare2.getAppBaseLink()>
				<cfset local.settingsStruct.fileShareSettings = local.objFileShare2.fileShareSettings>
				<cfset local.settingsStruct.objFS2 = local.objFileShare2>
				<cfset local.strSearch.settingsStruct = local.settingsStruct>
				<cfset local.bucketText = getBrowseText(local.settingsStruct.qryCategoryTrees)/>

				<!--- override category trees --->
				<cfquery name="local.settingsStruct.qryCategoryTrees" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT ct.categoryTreeID, ct.siteID, ct.siteResourceID, ct.categoryTreeName, ct.categoryTreeDesc, ct.categoryTreeCode, ct.controllingSiteResourceID
					FROM dbo.cms_categoryTrees ct 
					inner join dbo.cms_siteResources sr on sr.siteResourceID = ct.siteResourceID and sr.siteResourceStatusID = 1
					WHERE ct.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.settingsStruct.siteID#">	
					AND ct.controllingSiteResourceID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#local.settingsStruct.siteResourceId#">
					order by ct.sortOrder;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			</cfif>
		</cfif>
		
		<cfscript>
			if(len(local.strSearch.fromdate)){
				local.fromDate = createDateTime(year(local.strSearch.fromdate),month(local.strSearch.fromdate),day(local.strSearch.fromdate),0,0,0);
			}
			if(len(local.strSearch.todate)){
				local.toDate = createDateTime(year(local.strSearch.todate),month(local.strSearch.todate),day(local.strSearch.todate),23,59,59);
			}

			if (local.settingsStruct.fileShareID NEQ ""){
				local.fileShares = getFileShares(local.settingsStruct.siteID,local.settingsStruct.memberID, local.settingsStruct.fileShareID, local.settingsStruct.restrictToFileshares);
			} 
			else {
				local.fileShares = getFileShares(local.settingsStruct.siteID,local.settingsStruct.memberID, 0, local.settingsStruct.restrictToFileshares);
			}
			
			local.siteResourceID = 0;
			if (local.settingsStruct.fileShareID EQ "" AND local.strSearch.fsAppInstanceID eq 0)
			{
				local.fsIDs = valueList(local.fileShares.applicationInstanceID);
				local.siteResourceID = valueList(local.fileShares.siteResourceID);
				local.strSearch.sectionID = valueList(local.fileShares.rootSectionID);
			}
			else
			{
				if (local.fileShares.siteResourceID NEQ "")
					local.siteResourceID = local.fileShares.siteResourceID;
				if (local.fileShares.rootSectionID NEQ "")
					local.strSearch.sectionID = local.fileShares.rootSectionID;
			}

			local.numColumns = 3;
		</cfscript>

		<!--- is filter is present, parse it. filter is in format: category1|category2|showAllResults|applicationInstanceID --->
		
		<cfif local.fileShares.alwaysShowFolders EQ "1">
			<cfset local.strSearch.filter_showAllResults = "0">
		<cfelse>
			<cfset local.strSearch.filter_showAllResults = "1">
		</cfif>
		<cfif local.settingsStruct.fileShareID EQ "" AND local.strSearch.fsAppInstanceID eq 0>
			<cfset local.strSearch.filter_showAllResults = "0">
		</cfif>

		<!--- defaultBrowseMode override fileshare settings if filtering not set --->	
		<cfset local.args = arguments>
		<cfif listlen(arguments.filter,"|") NEQ 4 AND local.settingsStruct.defaultBrowseMode EQ "list" >
			<cfset local.strSearch.filter_showAllResults = "1">
		<cfelse>
			<cfset local.strSearch.filter_showAllResults = "0">
		</cfif>
	
		<cfif listlen(arguments.filter,"|") is 4>
			<cfif getToken(arguments.filter,1,"|") gt 0>
				<cfset local.strSearch.filter_category1 = getToken(arguments.filter,1,"|")>
			</cfif>
			<cfif getToken(arguments.filter,2,"|") neq 0>
				<cfset local.strSearch.filter_category2 = getToken(arguments.filter,2,"|")>
			</cfif>
			<cfif getToken(arguments.filter,3,"|") gt 0>
				<cfset local.strSearch.filter_showAllResults = getToken(arguments.filter,3,"|")>
			<cfelse>
				<!--- override max per page to show more drilldown options --->
				<cfset variables.thisBucketMaxPerPage = 10>
			</cfif>
		<cfelse>
			<!--- override max per page to show more drilldown options --->
			<cfset variables.thisBucketMaxPerPage = 10>
		</cfif>

		<cftry>
			<cfset local.sortType = getToken(arguments.sortType,1,"|")>
			<cfset local.currentSort = getToken(arguments.sortType,2,"|")>
			<cfset local.sortOrder = "DESC">
			<cfif local.currentSort eq "DESC">
				<cfset local.sortOrder = "ASC">
			</cfif>
			<cfcatch type="any">
				<cfset local.sortType = arguments.sortType>
				<cfset local.sortOrder = "DESC">
			</cfcatch>
		</cftry>
		
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfreturn getNotLoggedInResults(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID,viewDirectory=arguments.viewDirectory)>
		<cfelseif NOT StructKeyExists(local.settingsStruct.appRightsStruct,'fsDeleteAny')
			OR (NOT variables.cfcuser_isSiteAdmin AND val(local.qryBucketInfo.restrictToGroupID) GT 0 AND local.qryBucketInfo.isMemberInRestrictedGroup NEQ 1)>
			<cfreturn showNotAllowed(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, bucketName=local.qryBucketInfo.bucketName, 
				accessDeniedMessage=local.qryBucketInfo.accessDeniedMessage, includeBucketCount=false, viewDirectory=arguments.viewDirectory)>
		<cfelseif NOT local.strSearch.searchAccepted>
			<cfreturn showSearchNotAccepted(searchID=arguments.searchID,bucketID=arguments.bucketID,bucketName=local.qryBucketInfo.bucketName, bucketText=local.bucketText, applicationInstanceName=local.fileShares.applicationInstanceName, viewDirectory=arguments.viewDirectory)>
		<cfelse>
			<cfset local.strCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>

			<cfif local.fileShares.alwaysShowFolders EQ "0" OR 
					(local.strSearch.filter_category1 gt 0 AND local.strSearch.filter_category2 neq 0) OR
					local.strSearch.filter_showAllResults eq "1">
						
				<cfquery name="local.qryGetFileShareSettings" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select fs.fileShareID, fs.showResultSorting, fs.defaultResultSorting
					from dbo.fs_fileShare fs
					where fs.fileShareID = <cfqueryparam value="#val(local.settingsStruct.fileShareID)#" cfsqltype="cf_sql_integer">;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfswitch expression="#local.sortType#">
					<cfcase value="date">
						<cfset local.sqlSortType = "order by results.publicationDate #local.sortOrder#">
					</cfcase>
					<cfcase value="title">
						<cfset local.sqlSortType = "order by results.docTitle #local.sortOrder#">
					</cfcase>
					<cfdefaultcase>
						<cfset local.sqlSortType = "order by results.rank desc, results.publicationDate desc">	
						<cfif local.qryGetFileShareSettings.recordCount and val(local.qryGetFileShareSettings.showResultSorting) and len(trim(local.qryGetFileShareSettings.defaultResultSorting))>
							<cfset local.sortType = local.qryGetFileShareSettings.defaultResultSorting>
							<cfswitch expression="#local.qryGetFileShareSettings.defaultResultSorting#">
								<cfcase value="date">
									<cfset local.sortOrder  = "DESC">	
									<cfset local.sqlSortType = "order by results.publicationDate #local.sortOrder#">
								</cfcase>
								<cfcase value="title">
									<cfset local.sortOrder  = "ASC">	
									<cfset local.sqlSortType = "order by results.docTitle #local.sortOrder#">
								</cfcase>
							</cfswitch>
						</cfif>
					</cfdefaultcase>
				</cfswitch>				
				
				<cfset local.viewRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="ApplicationCreatedDocument", functionName="view")>
				<cfquery name="local.qryResults" datasource="#application.dsn.membercentral.dsn#" result="local.qryStat">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
					
					declare @atSpecial datetime = getdate();
					declare @siteID int, @memberID int, @viewFunctionID int, @communityResourceTypeID int,
						@searchID int, @bucketID int, @categoryID1 int, @categoryID2 int, @startRow int, @endRow int, @pageSize int,
						@docCount int;
					<cfif len(local.strSearch.keywords)>
						declare	@s varchar(8000) = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.strSearch.keywords#">;
					</cfif>

					set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
					set @memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strSearch.settingsStruct.memberID#">;
					set @viewFunctionID = <cfqueryparam value="#local.viewRFID#" cfsqltype="CF_SQL_INTEGER">;
					set @communityResourceTypeID = dbo.fn_getResourceTypeID('Community');
					set @searchID = <cfqueryparam value="#arguments.searchID#" cfsqltype="CF_SQL_INTEGER">;
					set @bucketID = <cfqueryparam value="#arguments.bucketID#" cfsqltype="CF_SQL_INTEGER">;
					set @categoryID1 = <cfqueryparam value="#local.strSearch.filter_category1#" cfsqltype="CF_SQL_INTEGER">;
					set @categoryID2 = <cfqueryparam value="#local.strSearch.filter_category2#" cfsqltype="CF_SQL_INTEGER">;
					set @startRow = <cfqueryparam value="#arguments.startRow#" cfsqltype="CF_SQL_INTEGER">;
					set @endRow = <cfqueryparam value="#(arguments.startRow + variables.thisBucketMaxPerPage -1)#" cfsqltype="CF_SQL_INTEGER">;
					set @pageSize = <cfqueryparam value="#variables.thisBucketMaxPerPage#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tmpResults') is not null
						DROP TABLE ##tmpResults;
					CREATE TABLE ##tmpResults (siteResourceID int, docCount int);
					
					WITH results AS(
						select documents.siteResourceID, ssrc.rank, v.publicationDate, l.docTitle
						from searchMC.dbo.tblSearchSiteResourceCache as ssrc
						inner join dbo.cms_documents as documents on ssrc.searchID = @searchID
							and ssrc.bucketID = @bucketID
							and documents.siteResourceID = ssrc.siteResourceID
						<cfif local.settingsStruct.fileShareID EQ "" AND local.strSearch.fsAppInstanceID eq 0>
							AND ssrc.applicationInstanceID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.fsIDs#" list="true">)
						<cfelseif local.strSearch.fsAppInstanceID NEQ 0>
							AND ssrc.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strSearch.fsAppInstanceID#">
						</cfif>
						INNER JOIN dbo.cms_documentLanguages l on documents.documentID = l.documentID
						INNER JOIN dbo.cms_documentVersions v on l.documentLanguageID = v.documentLanguageID AND v.isActive = 1
						<cfif local.strSearch.filter_category1 gt 0 and local.strSearch.filter_category2 gt 0 and local.strSearch.filter_showAllResults eq "0">
							inner join (
								select sr.siteResourceiD
								from searchMC.dbo.tblSearchSiteResourceCache src
								inner join membercentral.dbo.cms_categorySiteResources sr on sr.siteResourceID = src.siteResourceID
								inner join membercentral.dbo.cms_categories c on c.categoryID = sr.categoryID
								where src.searchID = @searchID
								and src.bucketID = @bucketID
								and sr.categoryID = @categoryID1
							) cat1 on cat1.siteResourceID = documents.siteResourceID
							inner join (
								select sr.siteResourceiD
								from searchMC.dbo.tblSearchSiteResourceCache src
								inner join membercentral.dbo.cms_categorySiteResources sr on sr.siteResourceID = src.siteResourceID
								inner join membercentral.dbo.cms_categories c on c.categoryID = sr.categoryID
								where src.searchID = @searchID
								and src.bucketID = @bucketID
								and sr.categoryID = @categoryID2
							) cat2 on cat2.siteResourceID = documents.siteResourceID
						<cfelseif local.strSearch.filter_category1 gt 0 and local.strSearch.filter_category2 neq 0 and local.strSearch.filter_showAllResults eq "0">
							inner join (
								select src.siteResourceID
								from searchMC.dbo.tblSearchSiteResourceCache src
								inner join membercentral.dbo.cms_categorySiteResources sr on sr.siteResourceID = src.siteResourceID
									and src.searchID =  @searchID
									and src.bucketID = @bucketID
									and sr.categoryID = @categoryID1
								left outer join cms_categorySiteResources sr2
									inner join dbo.cms_categories AS c on c.categoryID = sr2.categoryID
									inner join dbo.cms_categoryTrees as t on c.categoryTreeID = t.categoryTreeID
										and t.sortOrder = 2
									on sr2.siteResourceID = src.siteResourceID
								where c.categoryID is null
							) cat1 on cat1.siteResourceID = documents.siteResourceID
						<cfelseif len(arguments.postfilter) gt 1 and local.strSearch.filter_showAllResults eq "1">
							<cfset local.requiredCategories = structNew()>
							<cfloop query="local.settingsStruct.qryCategoryTrees">
								<cfquery name="local.qryCatIds" datasource="#application.dsn.membercentral.dsn#">
									SET NOCOUNT ON;
									SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

									SELECT c.categoryID
									FROM dbo.cms_categories AS c
									INNER JOIN dbo.cms_categoryTrees as t on c.categoryTreeID = t.categoryTreeID
									INNER JOIN dbo.cms_siteResources sr on sr.siteResourceID = t.siteResourceID and sr.siteResourceStatusID = 1								
									WHERE c.categoryID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#trim(arguments.postfilter)#" list="true">)
									AND t.categoryTreeID = <cfqueryparam value="#local.settingsStruct.qryCategoryTrees.categoryTreeID#" cfsqltype="CF_SQL_INTEGER">;

									SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
								</cfquery>
								<cfif local.qryCatIds.recordCount GT 0>
									<cfset local.requiredCategories[local.settingsStruct.qryCategoryTrees.categoryTreeID] = ValueList(local.qryCatIds.categoryID)>
								</cfif>
							</cfloop>
							<cfloop index="local.thisKey" list="#structKeyList(local.requiredCategories)#">
								INNER JOIN dbo.cms_categorySiteResources csrtree#local.thisKey# ON csrtree#local.thisKey#.siteResourceID = documents.siteResourceID
									AND csrtree#local.thisKey#.categoryID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.requiredCategories[local.thisKey]#" list="true">)
							</cfloop>
						</cfif>
					), 
					TempCount AS (
						SELECT COUNT(*) AS 'Count' FROM results
					)
					insert into ##tmpResults (siteResourceID, docCount)
					SELECT results.siteResourceID, TempCount.Count
					FROM results, TempCount
					#local.sqlSortType#
					OFFSET @startRow - 1 ROWS 
					FETCH NEXT @pageSize ROWS ONLY;

					SELECT TOP 1 @docCount=docCount FROM ##tmpResults;

					SELECT * 
					FROM (
						SELECT docs.*, @docCount AS 'Count'
						FROM (
							SELECT ai.applicationInstanceID, ai.siteID, 
								fs.fileShareID, fs.rootSectionID, fs.showPUblicationDate, fs.showResultSorting, fs.defaultResultSorting,
								sections.sectionID, sections.sectionName, sections.ovModeID, sections.ovTemplateID, sections.parentSectionID, 
								sections.siteResourceID as sectionSiteResourceID,
								documents.documentID, documents.siteResourceID as docSiteResourceID, src.rank, v.contributorMemberID, documents.redirectID, 
								l.docTitle, l.docDesc, v.fileName, v.fileExt, v.publicationDate, v.dateCreated, l.documentLanguageID,
								v.dateModified, v.author,
								docSR.resourceTypeID, srs.siteResourceStatusDesc,
								contributors2.firstName, contributors2.lastName, contributors2.company,
								fileShareName = ai.applicationInstanceName + case  WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END,
								(
									select count(dh.docHitID)
									FROM dbo.cms_documents d
									INNER JOIN dbo.cms_documentLanguages l on d.documentID = documents.documentID
										and d.documentID = l.documentID
									INNER JOIN dbo.cms_documentVersions v on l.documentLanguageID = v.documentLanguageID
									INNER JOIN platformstatsMC.dbo.statsDocumentHits dh on dh.documentVersionID = v.documentVersionID
								) as downloadCount
							FROM dbo.sites s 
							INNER JOIN dbo.cms_applicationInstances ai ON ai.siteID = s.siteID
								AND s.siteID = @siteID
							INNER JOIN dbo.fs_fileshare fs ON ai.applicationInstanceID = fs.applicationInstanceID
							INNER JOIN dbo.cms_siteResources appSR ON ai.siteResourceID = appSR.siteResourceID
								<cfif local.settingsStruct.fileShareID EQ "" AND local.strSearch.fsAppInstanceID eq 0>
									AND fs.applicationInstanceID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.fsIDs#" list="true">)
								<cfelseif local.strSearch.fsAppInstanceID NEQ 0>
									AND fs.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strSearch.fsAppInstanceID#">
								</cfif>
							INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteResourceID = appSR.parentSiteResourceID
							left outer join dbo.cms_siteResources AS grandparentResource
								inner join dbo.cms_applicationInstances AS CommunityInstances on communityInstances.siteResourceID = grandParentResource.siteResourceID
								on grandparentResource.siteResourceID = parentResource.parentSiteResourceID
								and grandparentResource.resourceTypeID = @communityResourceTypeID
							inner join dbo.cms_pagesections sections on sections.sectionID = fs.rootSectionID 
							INNER JOIN dbo.cms_documents documents ON documents.sectionID = fs.rootSectionID
								AND documents.siteID = @siteID 
								<cfif local.settingsStruct.fileShareID EQ "" AND local.strSearch.fsAppInstanceID eq 0>
									AND sections.sectionID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.strSearch.sectionID#" list="true">)
								<cfelseif local.strSearch.sectionID NEQ 0>
									AND sections.sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strSearch.sectionID#">
								</cfif>
							INNER JOIN dbo.cms_siteResources as docSR ON documents.siteResourceID = docSR.siteResourceID
							INNER JOIN dbo.cms_siteResourceStatuses as srs on docSR.siteResourceStatusID = srs.siteResourceStatusID 
							INNER JOIN searchMC.dbo.tblSearchSiteResourceCache src on src.siteResourceID = docSR.siteResourceID
								and src.searchID = @searchID
								and src.bucketID = @bucketID
								and src.siteResourceID in (select siteResourceID from ##tmpResults)
							INNER JOIN dbo.cms_documentLanguages l on documents.documentID = l.documentID
							INNER JOIN dbo.cms_documentVersions v on l.documentLanguageID = v.documentLanguageID AND v.isActive = 1
							<cfif local.strSearch.filter_category1 gt 0 and local.strSearch.filter_category2 gt 0 and local.strSearch.filter_showAllResults eq "0">
								inner join (
									select sr.siteResourceiD
									from searchMC.dbo.tblSearchSiteResourceCache src
									inner join membercentral.dbo.cms_categorySiteResources sr on sr.siteResourceID = src.siteResourceID
									inner join membercentral.dbo.cms_categories c on c.categoryID = sr.categoryID
									where src.searchID = @searchID
									and src.bucketID = @bucketID
									and sr.categoryID = @categoryID1
								) cat1 on cat1.siteResourceID = docSR.siteResourceID
								inner join 
								(
									select sr.siteResourceiD
									from searchMC.dbo.tblSearchSiteResourceCache src
									inner join membercentral.dbo.cms_categorySiteResources sr on sr.siteResourceID = src.siteResourceID
									inner join membercentral.dbo.cms_categories c on c.categoryID = sr.categoryID
									where src.searchID = @searchID
									and src.bucketID = @bucketID
									and sr.categoryID = @categoryID2
								) cat2 on cat2.siteResourceID = docSR.siteResourceID
							<cfelseif local.strSearch.filter_category1 gt 0 and local.strSearch.filter_category2 neq 0 and local.strSearch.filter_showAllResults eq "0">
								inner join 
								(
									select sr.siteResourceID
									from searchMC.dbo.tblSearchSiteResourceCache src
									inner join membercentral.dbo.cms_categorySiteResources sr on sr.siteResourceID = src.siteResourceID
										and src.searchID = @searchID
										and src.bucketID = @bucketID
										and sr.categoryID = @categoryID1
									left outer join cms_categorySiteResources sr2
										inner join dbo.cms_categories AS c on c.categoryID = sr2.categoryID
										inner join dbo.cms_categoryTrees as t on c.categoryTreeID = t.categoryTreeID
											and t.sortOrder = 2
									on sr2.siteResourceID = src.siteResourceID
									where c.categoryID is null
								) cat1 on cat1.siteResourceID = docSR.siteResourceID
							<cfelseif len(arguments.postfilter) gt 1 and local.strSearch.filter_showAllResults eq "1">
								inner join (
									select src.siteResourceID
									from searchMC.dbo.tblSearchSiteResourceCache src
									inner join membercentral.dbo.cms_categorySiteResources sr on sr.siteResourceID = src.siteResourceID
										and src.searchID =  @searchID
										and src.bucketID = @bucketID
										and sr.categoryID in (<cfqueryparam value="#arguments.postfilter#" cfsqltype="CF_SQL_INTEGER" list="yes">)
								) cat1 on cat1.siteResourceID = documents.siteResourceID									
							</cfif>
							INNER JOIN dbo.ams_members contributors ON contributors.memberID = v.contributorMemberID
							INNER JOIN dbo.ams_members contributors2 ON contributors2.memberID = contributors.activeMemberID
						) docs
					) results
					#local.sqlSortType#

					IF OBJECT_ID('tempdb..##tmpResults') is not null
						DROP TABLE ##tmpResults;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfset saveStats(arguments.searchID,'#variables.thisBucketType#.getResults',local.qryStat.ExecutionTime)>

				<!--- adjust maxperpage based on actual data if necessary and get page variables --->
				<cfscript>
					local.MaxPerPage = iif(local.qryResults.recordcount gt variables.thisBucketMaxPerPage,variables.thisBucketMaxPerPage,local.qryResults.recordcount);
					if (local.MaxPerPage gt 0) {
						local.NumTotalPages = Ceiling(local.qryResults.count / variables.thisBucketMaxPerPage);
						local.NumCurrentPage = int((int(arguments.startRow) + variables.thisBucketMaxPerPage - 1) / variables.thisBucketMaxPerPage);
					} else {
						local.NumTotalPages = 0;
						local.NumCurrentPage = 0;
					}
				</cfscript>
			</cfif>

			<cfsavecontent variable="local.stResults">
				<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/fileshare2/results.cfm">
			</cfsavecontent>

			<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>
			<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
			<cfset StructInsert(local.returnStruct,"itemcount",val(local.strCount.itemcount))>
			<cfset StructInsert(local.returnStruct,"success",true)>
			<cfreturn local.returnStruct>
		</cfif>	
	</cffunction>

	<cffunction name="getNotLoggedInResults" access="private" returntype="struct" output="no" hint="searches and returns not logged in text">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>
		<cfset local.strResultsCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/fileshare2/notLoggedInResults.cfm">
		</cfsavecontent>
		<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>

		<cfset local.returnStruct = StructNew()>
		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfset StructInsert(local.returnStruct,"itemcount",local.strResultsCount.itemCount)>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getIcon" access="public" output="yes" returntype="string">
		<cfargument name="fileExt" required="yes" type="string">
		
		<cfswitch expression="#fileExt#">
			<cfcase value="doc,docx"><cfset local.fileImg = "doc.png"></cfcase>
			<cfcase value="html"><cfset local.fileImg = "html.png"></cfcase>
			<cfcase value="pdf"><cfset local.fileImg = "pdf.png"></cfcase>
			<cfcase value="ppt,pptx"><cfset local.fileImg = "ppt.png"></cfcase>
			<cfcase value="txt"><cfset local.fileImg = "txt.png"></cfcase>
			<cfcase value="xls,xlsx"><cfset local.fileImg = "xls.png"></cfcase>
			<cfcase value="jpg,jpeg"><cfset local.fileImg = "jpg.png"></cfcase>
			<cfcase value="gif"><cfset local.fileImg = "gif.png"></cfcase>
			<cfcase value="zip"><cfset local.fileImg = "zip.png"></cfcase>
			<cfcase value="xml"><cfset local.fileImg = "xml.png"></cfcase>
			<cfcase value="png"><cfset local.fileImg = "png.png"></cfcase>
			<cfcase value="rtf"><cfset local.fileImg = "rtf.png"></cfcase>
			<cfdefaultcase><cfset local.fileImg = "file.png"></cfdefaultcase>
		</cfswitch>
		<cfreturn local.fileImg>
	</cffunction>

	<cffunction name="getFileDescription" access="public" output="yes" returntype="string">
		<cfargument name="fileExt" required="yes" type="string">
		
		<cfswitch expression="#fileExt#">
			<cfcase value="doc,docx"><cfset local.desc = "Word Document"></cfcase>
			<cfcase value="html"><cfset local.desc = "Web Page"></cfcase>
			<cfcase value="pdf"><cfset local.desc = "PDF Document"></cfcase>
			<cfcase value="ppt,pptx"><cfset local.desc = "Power Point Document"></cfcase>
			<cfcase value="txt"><cfset local.desc = "Text Document"></cfcase>
			<cfcase value="xls,xlsx"><cfset local.desc = "Excel Document"></cfcase>
			<cfcase value="jpg,jpeg"><cfset local.desc = "JPG Image"></cfcase>
			<cfcase value="gif"><cfset local.desc = "GIF Image"></cfcase>
			<cfcase value="zip"><cfset local.desc = "Zip Archive"></cfcase>
			<cfcase value="xml"><cfset local.desc = "XML Document"></cfcase>
			<cfcase value="png"><cfset local.desc = "PNG Image"></cfcase>
			<cfcase value="rtf"><cfset local.desc = "Rich Text Document"></cfcase>
			<cfcase value="wpd"><cfset local.desc = "Word Perfect Document"></cfcase>
			<cfcase value="ptx"><cfset local.desc = "E-Transcript"></cfcase>
			<cfdefaultcase><cfset local.desc = "Other File Type"></cfdefaultcase>
		</cfswitch>
		<cfreturn local.desc>
	</cffunction>
		
	<cffunction name="getBrowseText" access="public" output="false" returntype="string">
		<cfargument name="qryCategoryTrees" type="query" required="true">
		<cfset var local = structNew() />
		<cfset local.data = "">
		<!--- @TODO Need to update the pg variable for the file share bound to this search bucket --->
		<cfloop query="arguments.qryCategoryTrees">
			<cfset local.data = local.data & '<a href="/?#variables.thisBaseLink#&byT=#arguments.qryCategoryTrees.categoryTreeID#">#arguments.qryCategoryTrees.categoryTreeName#</a>' & " or " />
		</cfloop>
		<cfif len(local.data) gt len(" or ")>
			<cfset local.data = left(local.data, len(local.data) - len(" or "))>
		</cfif>
		<cfreturn local.data />
	</cffunction>
			
	<cffunction name="getTags" access="public" output="yes" returntype="string">
		<cfargument name="siteResourceID" required="yes" type="string">
		<cfargument name="showTagsAsLinks" required="yes" type="string">
		
		<cfset local.tag = "">
		<cfset local.Links = showTagsAsLinks>		

		<cfquery name="local.qryTags" datasource="#application.dsn.membercentral.dsn#">
			SELECT c.categoryID, c.categoryName, c.categoryTreeID
			FROM dbo.cms_categories AS c  WITH(NOLOCK)
			INNER JOIN dbo.cms_categoryTrees as t WITH(NOLOCK) on c.categoryTreeID = t.categoryTreeID
			INNER JOIN dbo.cms_categorySiteResources csr WITH(NOLOCK) on csr.categoryID = c.categoryID
			WHERE csr.siteResourceID = <cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY t.sortOrder
		</cfquery>
	
		<cfloop query="local.qryTags">
			<cfif local.links EQ "0">
				<cfset local.tag = local.tag & '&nbsp;' & local.qryTags.categoryName & '&nbsp;&nbsp;&nbsp;'>
			<cfelse>
				<cfset local.tag = local.tag & '<a href="/?#variables.thisBaseLink#&catID=#local.qryTags.categoryID#&byT=#local.qryTags.categoryTreeID#" target="_blank">' & local.qryTags.categoryName & '</a>&nbsp;&nbsp;&nbsp;'>
			</cfif>
		</cfloop>
		
		<cfreturn local.tag>
	</cffunction>

	<cffunction name="getFolders" access="public" output="yes" returntype="query">
		<cfargument name="searchID" required="yes" type="string">
		<cfargument name="bucketID" required="yes" type="string">
		<cfargument name="sortOrder" required="yes" type="string">
		<cfargument name="filter_category1" required="yes" type="string">
		<cfargument name="applicationInstanceID" required="yes" type="string">
		
		<cfset local.tag = "">

		<cfif arguments.filter_category1 gt 0>
			<cfquery name="local.qryFolders" datasource="#application.dsn.membercentral.dsn#" result="local.qryFolders1">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select isnull(c.categoryID,-1) as categoryID, isnull(c.categoryName, 'Uncategorized') as categoryName, c.categoryTreeID, count(*) as docCount
				from searchMC.dbo.tblSearchSiteResourceCache src
				inner join dbo.cms_categorySiteResources sr	on sr.siteResourceID = src.siteResourceID
					and src.searchID = <cfqueryparam value="#arguments.searchID#" cfsqltype="CF_SQL_INTEGER">
					and src.bucketID = <cfqueryparam value="#arguments.bucketID#" cfsqltype="CF_SQL_INTEGER">
					<cfif arguments.applicationInstanceID NEQ "0">
						and src.applicationInstanceID = <cfqueryparam value="#arguments.applicationInstanceID#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
					and sr.categoryID = <cfqueryparam value="#arguments.filter_category1#" cfsqltype="CF_SQL_INTEGER">
				left outer join dbo.cms_categorySiteResources sr2
					inner join dbo.cms_categories AS c on c.categoryID = sr2.categoryID
					inner join dbo.cms_categoryTrees as t on c.categoryTreeID = t.categoryTreeID
						and t.sortOrder = <cfqueryparam value="#arguments.sortOrder#" cfsqltype="CF_SQL_INTEGER">
				on sr2.siteResourceID = src.siteResourceID
				group by isnull(c.categoryID,-1), isnull(c.categoryName, 'Uncategorized'), c.categoryTreeID
				order by categoryName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelse>
			<cfquery name="local.qryFolders" datasource="#application.dsn.membercentral.dsn#" result="local.qryFolders2">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT distinct c.categoryID, c.categoryName, c.categoryTreeID, t.sortOrder, count(sr.siteResourceID) as docCount
				FROM searchMC.dbo.tblSearchSiteResourceCache src
				INNER JOIN dbo.cms_categorySiteResources sr ON sr.siteResourceID = src.siteResourceID
					and src.searchID = <cfqueryparam value="#arguments.searchID#" cfsqltype="CF_SQL_INTEGER">
					and src.bucketID = <cfqueryparam value="#arguments.bucketID#" cfsqltype="CF_SQL_INTEGER">
					<cfif arguments.applicationInstanceID NEQ "0">
						and src.applicationInstanceID = <cfqueryparam value="#arguments.applicationInstanceID#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
				inner join dbo.cms_categories AS c ON c.categoryID = sr.categoryID
				INNER JOIN dbo.cms_categoryTrees as t ON c.categoryTreeID = t.categoryTreeID
					and t.sortOrder =  <cfqueryparam value="#arguments.sortOrder#" cfsqltype="CF_SQL_INTEGER">
				group by c.categoryID, c.categoryName, c.categoryTreeID, t.sortOrder
				order by t.sortOrder, c.categoryName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfreturn local.qryFolders>
	</cffunction>

	<cffunction name="getCategoryName" access="public" output="yes" returntype="string">
		<cfargument name="categoryID" required="yes" type="string">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryCategoryName" datasource="#application.dsn.membercentral.dsn#">
			SELECT distinct c.categoryName
			FROM dbo.cms_categories AS c  WITH(NOLOCK)
			WHERE c.categoryID = <cfqueryparam value="#arguments.categoryID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfreturn local.qryCategoryName.categoryName>
	</cffunction>

	<cffunction name="getCategoryNames" access="public" output="yes" returntype="string">
		<cfargument name="categoryIDs" required="yes" type="string">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryCategoryName" datasource="#application.dsn.membercentral.dsn#">
			SELECT distinct c.categoryName
			FROM dbo.cms_categories AS c WITH(NOLOCK)
			WHERE c.categoryID in ( <cfqueryparam value="#arguments.categoryIDs#" cfsqltype="CF_SQL_INTEGER" list="true">)
		</cfquery>
		
		<cfreturn ValueList(local.qryCategoryName.categoryName, "^")>
	</cffunction>

	<cffunction name="createKeywordExtras" access="public" output="yes" returntype="string">
		<cfargument name="expandedCategoryNames" required="yes" type="string">
		
		<cfset local.s_jurisdiction_key_all = "" />
		<cfset local.temp  = replace(arguments.expandedCategoryNames, "^", " ", "all") />

		<cfreturn local.temp>
	</cffunction>

	<cffunction name="getMemberIDFromSiteID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfset var local = structNew() />

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.siteInfo">
			SELECT s.siteID, s.orgID
			FROM dbo.sites s
			WHERE s.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
		</cfquery>
		
		<cfset local.memberID = application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=local.siteInfo.orgID)>
			
		<cfreturn local.memberID />
	</cffunction>
	<cffunction name="getSiteInfoFromBucketID" access="public" output="false" returntype="query">
		<cfargument name="bucketID" type="numeric" required="true">
		<cfset var local = structNew() />
		
			<cfquery datasource="#application.dsn.tlasites_search.dsn#" name="local.site">
				SELECT sb.siteID
				FROM dbo.tblSearchBuckets sb  WITH(NOLOCK)
				WHERE sb.bucketID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.bucketID#">
			</cfquery>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
				SELECT s.siteID,s.orgID
				FROM dbo.sites s WITH(NOLOCK)
				WHERE s.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.site.siteID#">
			</cfquery>
			
		<cfreturn local.data />
	</cffunction>

	<cffunction name="getFileShares" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="fileShareID" type="numeric" required="true">
		<cfargument name="restrictToFileshares" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.viewRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Fileshare2", functionName="view")>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
			set nocount on;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @memberID int, @fileShareID int, @groupPrintID int, @communityResourceTypeID int, @functionID int;
			set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
			set @memberID = isnull(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">,0);
			select @communityResourceTypeID = dbo.fn_getResourceTypeID('Community');
			set @functionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.viewRFID#">;

			<cfif StructKeyExists(arguments, "fileShareID")>
				set @fileShareID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fileShareID#">
			</cfif>
				
			if @memberID > 0
				select @groupPrintID = groupPrintID from ams_members where memberID = @memberID
			else
				select @groupPrintID = publicGroupPrintID
				from sites s
				inner join organizations o on o.orgID = s.orgID
					and s.siteID = @siteID

			SELECT 
				memberID = @memberID, ai.applicationInstanceID, ai.siteID, s.orgID, ai.siteResourceID, appSR.resourceTypeID,
				fs.fileShareID, 
				fs.rootSectionID, 
				fs.showPublicationDate, 
				fs.isNetworkWide,
				fs.showVersioning, 
				fs.showAuthor, 
				fs.showContributedBy, 
				fs.showFirm, 
				fs.authorLabel, 
				fs.showTags, 
				fs.alwaysShowFolders,
				fs.showTagsAsLinks, 
				fs.showAddress,
				fs.showCustomFields,
				fs.isMultipleSelectSearch,
				fs.showDocDownloadCountToMembers,
				fs.showShareButton,
				ai.applicationInstanceName, fileShareName = ai.applicationInstanceName + 
				case  WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')'
				ELSE '' END
			FROM dbo.sites s
			INNER JOIN dbo.cms_applicationInstances ai ON ai.siteID = s.siteID
				and s.siteID = @siteID
			INNER JOIN dbo.fs_fileshare fs ON ai.applicationInstanceID = fs.applicationInstanceID
				<cfif arguments.fileShareID NEQ 0>
					AND fs.fileShareID = @fileShareID
				</cfif>
				<cfif len(arguments.restrictToFileshares)>
					AND fs.fileShareID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#trim(arguments.restrictToFileshares)#" list="true">)
				</cfif>
			INNER JOIN dbo.cms_siteResources appSR ON appsr.siteID = @siteID and ai.siteResourceID = appSR.siteResourceID
				and appsr.siteResourceStatusID = 1
			inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
				and srfrp.siteResourceID = appSR.siteResourceID
				and srfrp.functionID = @functionID
			inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.rightPrintID = srfrp.rightPrintID
				and gprp.groupPrintID = @groupPrintID and gprp.siteID = @siteID
			INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteID = @siteID and parentResource.siteResourceID = appSR.parentSiteResourceID
			left outer join dbo.cms_siteResources AS grandparentResource WITH(NOLOCK)
				inner join dbo.cms_applicationInstances AS CommunityInstances WITH(NOLOCK) on communityInstances.siteResourceID = grandParentResource.siteResourceID
			on grandparentResource.siteID = @siteID and grandparentResource.siteResourceID = parentResource.parentSiteResourceID
				and grandparentResource.resourceTypeID = @communityResourceTypeID
				and grandparentResource.siteResourceStatusID = 1

			ORDER BY FileShareName

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getFileShareInfoFromAppID" access="remote" output="false" returntype="query">
		<cfargument name="appInstanceID" type="string" required="true">
		<cfset var local = structNew() />
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.fsInfo">
				SELECT ai.siteID, fs.fileShareID, fs.rootSectionID, ai.siteResourceID
				FROM dbo.cms_applicationInstances ai WITH(NOLOCK)
				INNER JOIN dbo.fs_fileShare fs WITH(NOLOCK) ON fs.applicationInstanceID = ai.applicationInstanceID
					AND ai.applicationInstanceID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.appInstanceID#" list="true">)
			</cfquery>
		<cfreturn local.fsInfo />
	</cffunction>
	
	<cffunction name="getFileShareNameFromAppID" access="private" returntype="string">
		<cfargument name="appInstanceID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(0,0,15,0)#">
			set nocount on;

			declare @RTID int;
			select @RTID = dbo.fn_getResourceTypeID('Community');

			SELECT 
				fileShareName = ai.applicationInstanceName + 
				case  WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')'
				ELSE ''
				END
			FROM dbo.fs_fileshare fs WITH(NOLOCK)
			INNER JOIN dbo.cms_applicationInstances ai WITH(NOLOCK) ON ai.applicationInstanceID = fs.applicationInstanceID
				AND fs.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.appInstanceID#">
			INNER JOIN dbo.sites s WITH(NOLOCK) ON ai.siteID = s.siteID
			INNER JOIN dbo.cms_siteResources appSR WITH(NOLOCK) ON ai.siteResourceID = appSR.siteResourceID
			INNER JOIN dbo.cms_siteResources AS parentResource WITH(NOLOCK) ON parentResource.siteResourceID = appSR.parentSiteResourceID
			left outer join dbo.cms_siteResources AS grandparentResource WITH(NOLOCK)
				inner join dbo.cms_applicationInstances AS CommunityInstances WITH(NOLOCK) on communityInstances.siteResourceID = grandParentResource.siteResourceID
				on grandparentResource.siteResourceID = parentResource.parentSiteResourceID
				and grandparentResource.resourceTypeID = @RTID;
		</cfquery>
		<cfreturn local.data.fileShareName>
	</cffunction>
	
	<cffunction name="getSectionNameFromID" access="private" returntype="string">
		<cfargument name="sectionID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qsiteresourceid" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(0,0,15,0)#">
			select sectionName 
			from cms_pageSections WITH(NOLOCK)
			where sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.sectionID#">
		</cfquery>
		<cfreturn local.qsiteresourceid.sectionName>
	</cffunction>	
	
	<cffunction name="getExtraFSColumns" returntype="query" output="no">
		<cfargument name="siteResourceID" required="Yes" type="numeric">
		
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfquery name="local.qryExtraDocData" datasource="#application.dsn.membercentral.dsn#">
			select *
			from dbo.cms_siteResourceDataColumns  WITH(NOLOCK)
			where containerSiteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceID#">
			order by columnOrder
		</cfquery>

		<cfreturn local.qryExtraDocData>
	</cffunction>
	
	<cffunction name="saveSearchForm" access="public" output="no" returntype="numeric" hint="saves the form vars to a search and returns the searchid">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="formvars" required="yes" type="struct">

		<cfset var local = structNew()>
		
		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfif StructKeyExists(arguments.formvars,"s_applicationinstanceid") and isnumeric(arguments.formvars.s_applicationinstanceid) and arguments.formvars.s_applicationinstanceid gt 0>
			<cfset local.applicationInstanceID = arguments.formvars.s_applicationinstanceid>
			<cfset local.applicationInstanceName = xmlFormat(getFileShareNameFromAppID(local.applicationInstanceID))>
		<cfelse>
			<cfif StructKeyExists(arguments.formvars,"s_applicationinstanceid") and len(arguments.formvars.s_applicationinstanceid)>
				<cfset local.applicationInstanceID = arguments.formvars.s_applicationinstanceid>
			<cfelse>
	 			<cfset local.applicationInstanceID = 0> 
			</cfif>
			<cfset local.applicationInstanceName = "">
		</cfif>
			
		<cfif StructKeyExists(arguments.formvars,"s_category") and isnumeric(arguments.formvars.s_category) and arguments.formvars.s_category gt 0>
			<cfset local.sectionID = arguments.formvars.s_category>
			<cfset local.sectionName = getSectionNameFromID(local.sectionID)>
		<cfelse>
			<cfset local.sectionID = 0>
			<cfset local.sectionName = "">
		</cfif>
		
		<cfif StructKeyExists(arguments.formvars,"s_jurisdiction") >
			<cfset local.jurisdictionID 	= arguments.formvars.s_jurisdiction>
			<cfset local.jurisdictionName = xmlFormat(getCategoryNames(local.jurisdictionID))>

			<cfset local.additionalKeywords = xmlFormat(createKeywordExtras (local.jurisdictionName)) />			
		<cfelse>
			<cfset local.jurisdictionID = 0>
			<cfset local.jurisdictionName = "">
			<cfset local.additionalKeywords = "" />
		</cfif>		
		
		<cfsavecontent variable="local.xmlSearch">
			<cfoutput>
				<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2">
					<bid><cfif StructKeyExists(arguments.formvars,"bid")>#val(arguments.formvars.bid)#</cfif></bid>
					
					<cfif local.applicationInstanceID gt 0>
						<s_applicationinstanceid expanded="#local.applicationInstanceName#">#local.applicationInstanceID#</s_applicationinstanceid>
					<cfelse>
						<s_applicationinstanceid/>
					</cfif>
					
					<s_aut><cfif StructKeyExists(arguments.formvars,"s_aut")>#xmlFormat(trim(arguments.formvars.s_aut))#</cfif></s_aut>
					<cfif local.sectionID gt 0>
						<s_category expanded="#local.sectionName#">#local.sectionID#</s_category>
					<cfelse>
						<s_category/>
					</cfif>
					<cfif StructKeyExists(arguments.formvars,"s_datefrom") and isValid("date",arguments.formvars.s_datefrom)>
						<s_datefrom>#dateformat(arguments.formvars.s_datefrom,"yyyy-mm-dd")#Z</s_datefrom>
					<cfelse>
						<s_datefrom xsi:nil="true" />
					</cfif>
					<cfif StructKeyExists(arguments.formvars,"s_dateto") and isValid("date",arguments.formvars.s_dateto)>
						<s_dateto>#dateformat(arguments.formvars.s_dateto,"yyyy-mm-dd")#Z</s_dateto>
					<cfelse>
						<s_dateto xsi:nil="true" />
					</cfif>
					<cfif StructKeyExists(arguments.formvars,"s_depodatefrom") and isValid("date",arguments.formvars.s_depodatefrom)>
						<s_depodatefrom>#dateformat(arguments.formvars.s_depodatefrom,"yyyy-mm-dd")#Z</s_depodatefrom>
					<cfelse>
						<s_depodatefrom xsi:nil="true" />
					</cfif>
					<cfif StructKeyExists(arguments.formvars,"s_depodateto") and isValid("date",arguments.formvars.s_depodateto)>
						<s_depodateto>#dateformat(arguments.formvars.s_depodateto,"yyyy-mm-dd")#Z</s_depodateto>
					<cfelse>
						<s_depodateto xsi:nil="true" />
					</cfif>
					<!---  if a jurisdiction is selected.  This will be populated with the additional keywords for search --->
					<cfif len(local.additionalKeywords) gt 0>
						<s_docflags>#local.additionalKeywords#</s_docflags>
					<cfelse>
						<s_docflags />
					</cfif>
					<s_fname><cfif StructKeyExists(arguments.formvars,"s_fname")>#xmlFormat(trim(replace(arguments.formvars.s_fname,chr(34),'','ALL')))#</cfif></s_fname>
					
					<cfif local.jurisdictionID gt 0>
						<s_jurisdiction expanded="#local.jurisdictionName#">#local.jurisdictionID#</s_jurisdiction>
					<cfelse>
						<s_jurisdiction/>
					</cfif>

					<s_key_all><cfif StructKeyExists(arguments.formvars,"s_key_all")>#xmlFormat(trim(arguments.formvars.s_key_all))#</cfif></s_key_all>
					<s_key_one><cfif StructKeyExists(arguments.formvars,"s_key_one")>#xmlFormat(trim(arguments.formvars.s_key_one))#</cfif></s_key_one>
					<s_key_phrase><cfif StructKeyExists(arguments.formvars,"s_key_phrase")>#xmlFormat(trim(arguments.formvars.s_key_phrase))#</cfif></s_key_phrase>
					<s_key_x><cfif StructKeyExists(arguments.formvars,"s_key_x")>#xmlFormat(trim(arguments.formvars.s_key_x))#</cfif></s_key_x>
					<s_lname><cfif StructKeyExists(arguments.formvars,"s_lname")>#xmlFormat(trim(replace(arguments.formvars.s_lname,chr(34),'','ALL')))#</cfif></s_lname>

					<cfif StructKeyExists(arguments.formvars,"s_postdatefrom") and isValid("date",arguments.formvars.s_postdatefrom)>
						<s_postdatefrom>#dateformat(arguments.formvars.s_postdatefrom,"yyyy-mm-dd")#Z</s_postdatefrom>
					<cfelse>
						<s_postdatefrom xsi:nil="true" />
					</cfif>
					
					<cfif StructKeyExists(arguments.formvars,"s_postdateto") and isValid("date",arguments.formvars.s_postdateto)>
						<s_postdateto>#dateformat(arguments.formvars.s_postdateto,"yyyy-mm-dd")#Z</s_postdateto>
					<cfelse>
						<s_postdateto xsi:nil="true" />
					</cfif>
				</search>
			</cfoutput>
		</cfsavecontent>
		<cfset local.searchID = saveSearchXML(val(arguments.formvars.bid),local.xmlSearch)>
		<cfreturn local.searchID>
	</cffunction>
	
	<cffunction name="cacheItemCount" access="public" output="yes" returntype="numeric">
		<cfargument name="searchID" required="yes" type="string">
		<cfargument name="bucketID" required="yes" type="string">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryCheckCache" datasource="#application.dsn.searchMC.dsn#">
			select count(*) as itemcount
			from dbo.tblSearchSiteResourceCache WITH(NOLOCK)
			where searchID = <cfqueryparam value="#arguments.searchID#" cfsqltype="CF_SQL_INTEGER">
			and bucketID = <cfqueryparam value="#arguments.bucketID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfreturn local.qryCheckCache.itemcount>
	</cffunction>

	<cffunction name="cacheGetItems" access="public" output="yes" returntype="query">
		<cfargument name="searchID" required="yes" type="string">
		<cfargument name="bucketID" required="yes" type="string">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryCache" datasource="#application.dsn.searchMC.dsn#">
			select siteResourceID
			from dbo.tblSearchSiteResourceCache WITH(NOLOCK)
			where searchID = <cfqueryparam value="#arguments.searchID#" cfsqltype="CF_SQL_INTEGER">
			and bucketID = <cfqueryparam value="#arguments.bucketID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfreturn local.qryCache>
	</cffunction>

	<cffunction name="cacheSplitBucketCounts" access="public" output="yes" returntype="query">
		<cfargument name="searchID" required="yes" type="string">
		<cfargument name="bucketID" required="yes" type="string">
		<cfargument name="applicationInstanceID" required="no" type="string" default="">
	
		<cfset var local = structNew()>

		<cfquery name="local.qryCacheSplitBucketCounts" datasource="#application.dsn.memberCentral.dsn#">
			set nocount on;

			declare @RTID int;
			select @RTID = dbo.fn_getResourceTypeID('Community');

			SELECT ai.applicationInstanceID, ai.applicationInstanceName,
				alwaysShowFolders = case when fs.alwaysShowFolders = 1 THEN 0 ELSE 1 END,
				fileShareName = ai.applicationInstanceName + 
				case  WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')'
				ELSE ''
				END, 
				count(*) itemCount
			FROM dbo.fs_fileshare fs WITH(NOLOCK)
			INNER JOIN dbo.cms_applicationInstances ai WITH(NOLOCK) ON ai.applicationInstanceID = fs.applicationInstanceID
			<cfif arguments.applicationInstanceID NEQ "">
				and ai.applicationInstanceID = <cfqueryparam value="#arguments.applicationInstanceID#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			INNER JOIN searchMC.dbo.tblSearchSiteResourceCache src WITH(NOLOCK) on src.applicationInstanceID = ai.applicationInstanceID
			INNER JOIN dbo.sites s WITH(NOLOCK) ON ai.siteID = s.siteID
			INNER JOIN dbo.cms_siteResources appSR WITH(NOLOCK) ON ai.siteResourceID = appSR.siteResourceID
			INNER JOIN dbo.cms_siteResources AS parentResource WITH(NOLOCK) ON parentResource.siteResourceID = appSR.parentSiteResourceID
			left outer join dbo.cms_siteResources AS grandparentResource WITH(NOLOCK)
				inner join dbo.cms_applicationInstances AS CommunityInstances WITH(NOLOCK) on communityInstances.siteResourceID = grandParentResource.siteResourceID
				on grandparentResource.siteResourceID = parentResource.parentSiteResourceID
				and grandparentResource.resourceTypeID = @RTID
			where src.searchID = <cfqueryparam value="#arguments.searchID#" cfsqltype="CF_SQL_INTEGER">
			and src.bucketID = <cfqueryparam value="#arguments.bucketID#" cfsqltype="CF_SQL_INTEGER">
			group by ai.applicationInstanceID, ai.applicationInstanceName, fs.alwaysShowFolders,
				(ai.applicationInstanceName + 
					case  WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')'
					ELSE ''
					END );
		</cfquery>
		
		<cfreturn local.qryCacheSplitBucketCounts>
	</cffunction>

	<cffunction name="getCategoryLink" access="private" output="false" returntype="string">
		<cfargument name="searchID" required="yes" type="string">
		<cfargument name="categoryID" required="yes" type="string">
		<cfargument name="filter_category1" required="yes" type="string">
		<cfargument name="filter_category2" required="yes" type="string">
		<cfargument name="categoryName" required="yes" type="string">
		<cfargument name="docCount" required="yes" type="string">
		<cfargument name="appInstanceID" required="yes" type="string">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
	
		<cfset var local = structNew()>

		<cfsavecontent variable="local.link">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/fileshare2/categoryLink.cfm">
		</cfsavecontent>

		<cfreturn local.link>
	</cffunction>

	<cffunction name="getAddress" access="public" output="yes" retuntype="string">
		<cfargument name="memberid" required="yes" type="string">
		
		<cfset var local = structNew()>

		<cfquery name="local.qyrAdd" datasource="#application.dsn.membercentral.dsn#">
			select email, address1, address2, city, code, postalcode
			from dbo.ams_members m WITH(NOLOCK)
			inner join dbo.ams_memberEmailTypes t WITH(NOLOCK) on t.orgID = m.orgID
			inner join dbo.ams_memberEmails e WITH(NOLOCK) on e.memberID = m.memberID
			inner join dbo.ams_memberAddresses a WITH(NOLOCK) on a.memberid = m.memberid
			inner join dbo.ams_states s WITH(NOLOCK) on s.stateID = a.stateID
			where e.memberid = <cfqueryparam value="#arguments.memberid#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfreturn local.qyrAdd>
	</cffunction>

	<cffunction name="showNotLoggedInResults" access="public" returntype="string" output="false">
		<cfargument name="viewDirectory" required="no" type="string" default="default">

		<cfset var local = StructNew()>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/fileshare2/showNotLoggedInResults.cfm">
		</cfsavecontent>
		<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>

		<cfreturn local.stResults>
	</cffunction>

	<cffunction name="getOptionalFilters" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="appInstanceID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="s_jurisdiction" type="string" required="true">
		<cfargument name="s_aut" type="string" required="true">
		<cfargument name="s_fname" type="string" required="true">
		<cfargument name="s_lname" type="string" required="true">
		<cfargument name="viewDirectory" required="no" type="string" default="default">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfset local.fileShareSettings = CreateObject("component","model.fileshare2.fileshare2").getFileShareSettings(val(arguments.appInstanceID))/>

		<!--- get category trees --->
		<cfquery name="local.settingsStruct.qryCategoryTrees" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			SELECT ct.categoryTreeID, ct.siteID, ct.siteResourceID, ct.categoryTreeName, ct.categoryTreeDesc, ct.categoryTreeCode, ct.controllingSiteResourceID
			FROM dbo.cms_categoryTrees ct
			INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = ct.siteResourceID AND sr.siteResourceStatusID = 1
			WHERE ct.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">
			AND ct.controllingSiteResourceID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#local.fileShareSettings.siteResourceId#">
			ORDER BY ct.sortOrder
		</cfquery>

		<cfsavecontent variable="local.retStruct.htmlcontent">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/fileshare2/optionalSearchFilters.cfm">
		</cfsavecontent>

		<cfset local.retStruct.success = true>
		<cfreturn local.retStruct>
	</cffunction>
	
	<cffunction name="getBucketSettingsHelpText" access="public" output="false" returntype="struct">
		
		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>

		<cfsavecontent variable="local.returnStruct.helpText">
			<cfoutput>
				<div class="alert align-items-center pl-2 align-content-center alert-primary show mb-2 alertSection" role="alert">
					<ul>
						<li>fileShareID -- Fileshare Id</li>
						<li>restrictToFileshares = true or false</li>
						<li>bucketHeading -- Bucket Heading</li>
						<li>categoryID -- Category Id </li>
						<li>autoExpandAdvFilters = 1 or 0, DEFAULT 0</li>
						<li>defaultBrowseMode = list or folder, DEFAULT 'folder'</li>
					</ul>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnStruct>
	</cffunction>
</cfcomponent>