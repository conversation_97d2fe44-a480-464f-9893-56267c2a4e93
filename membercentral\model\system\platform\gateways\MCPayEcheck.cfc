<cfcomponent>

	<cfif application.MCEnvironment eq "production">
		<cfset variables.apiRootURL = "https://payments.usiopay.com/2.0/">
	<cfelse>
		<cfset variables.apiRootURL = "https://devpayments.usiopay.com/2.0/">
	</cfif>
	
	<cffunction name="charge" access="package" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="returnStruct" type="struct" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="qryInfoOnFile" type="query" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
		<cfargument name="adminForm" type="boolean" required="no" default="0">
	
		<cfset var local = structNew()>
	
		<!--- copy args for xml history --->
		<cfset local.strArgsCopy = duplicate(arguments)>
		<cfset StructDelete(local.strArgsCopy,'qryGateWayID')>
		<cfset StructDelete(local.strArgsCopy,'returnStruct')>
		<cfset StructDelete(local.strArgsCopy,'qryGatewayProfileFields')>
		<cfset StructDelete(local.strArgsCopy,'qryInfoOnFile')>
	
		<!--- return back the amount since applepay/googlepay need it anyway --->
		<cfset arguments.returnStruct.x_amount = arguments.x_amount>

		<cfscript>
		if (arguments.adminForm AND arguments.qryInfoOnFile.acctType eq "Personal")
			local.seccode = "PPD";
		else if (NOT arguments.adminForm AND arguments.qryInfoOnFile.acctType eq "Personal")
			local.seccode = "WEB";
		else
			local.seccode = "CCD";

		local.tmpStr = { 
			acctType=arguments.qryInfoOnFile.acctType,
			seccode=local.seccode
		};

		var qryBankAccount = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @MPPPayProfileID int, @orgID int;
			SET @MPPPayProfileID = :MPPPayProfileID;
			SET @orgID = :orgID;
			
			SELECT firstName, lastName, address1, city, stateCode, postalCode
			FROM dbo.tr_bankAccounts
			WHERE MPPPayProfileID = @MPPPayProfileID
			AND orgID = @orgID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{ 
				MPPPayProfileID = { value=arguments.qryInfoOnFile.payProfileID, cfsqltype="CF_SQL_INTEGER" },
				orgID = { value=arguments.qryGateWayID.orgID, cfsqltype="CF_SQL_INTEGER" }
			},
			{ datasource="#application.dsn.membercentral.dsn#" }
		);

		local.qryMember = application.objMember.getMemberInfo(memberid=arguments.assignedToMemberID);
		local.qryOrgIdentity = application.objOrgInfo.getOrgIdentity(orgIdentityID=arguments.qryGateWayID.orgIdentityID);

		local.apiRequestBody = '{' & 
			'"MerchantID": "#arguments.qryGateWayID.gatewayMerchantID#",' & 
			'"Login": "#arguments.qryGateWayID.gatewayUsername#",' & 
			'"Password": "#arguments.qryGateWayID.gatewayPassword#",' & 
			'"RoutingNumber": "#arguments.qryInfoOnFile.routingNumber#",' & 
			'"AccountNumber": "#arguments.qryInfoOnFile.accountNumber#",' & 
			'"TransCode": "27",' & 
			'"Amount": "#arguments.x_amount#",' & 
			'"FirstName": "#left(encodeForHTML(len(qryBankAccount.firstName) ? qryBankAccount.firstName : local.qryMember.firstName),19)#",' & 
			'"LastName": "#left(encodeForHTML(len(qryBankAccount.lastName) ? qryBankAccount.lastName : local.qryMember.lastName),19)#",' & 
			'"EmailAddress": "<EMAIL>",' & 
			'"Address1": "#left(encodeForHTML(len(qryBankAccount.address1) ? qryBankAccount.address1 : local.qryOrgIdentity.address1),39)#",' & 
			'"City": "#left(encodeForHTML(len(qryBankAccount.city) ? qryBankAccount.city : local.qryOrgIdentity.city),39)#",' & 
			'"State": "#len(qryBankAccount.stateCode) ? qryBankAccount.stateCode : local.qryOrgIdentity.stateCode#",' & 
			'"Zip": "#encodeForHTML(len(qryBankAccount.postalCode) ? qryBankAccount.postalCode : local.qryOrgIdentity.postalCode)#",' & 
			'"StandardEntryCode": "#local.seccode#",' & 
			'"Description": "WebsitePay",' & 
			'"AddendaData": "#left(encodeForHTML(arguments.x_description),80)#",' & 
			'"DisableDuplicateCheck": true' & 
		'}';

		local.xmlPaymentInfo = '<payment gatewayid="#arguments.qryGateWayID.gatewayID#" profileid="#arguments.qryGateWayID.profileID#"><args>';
		for (local.fld in local.strArgsCopy) {
			local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.strArgsCopy[local.fld])#</#lcase(local.fld)#>";
		}
		for (local.fld in local.tmpStr) {
			local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.tmpStr[local.fld])#</#lcase(local.fld)#>";
		}
		local.xmlPaymentInfo = local.xmlPaymentInfo & '</args><gateway><![CDATA[ #local.apiRequestBody# ]]></gateway></payment>';
		
		local.returnHistory = application.objPayments.insertPaymentHistory(payerMemberID=arguments.assignedToMemberID, 
			memberPaymentProfileID=arguments.qryInfoOnFile.payProfileID, paymentInfo=local.xmlPaymentInfo,
			gatewayID=arguments.qryGateWayID.gatewayID, profileID=arguments.qryGateWayID.profileID, paymentType='payment');

		// handle response
		local.strResponse = callGateway(apiURL="#variables.apiRootURL#payments.svc/JSON/SubmitACHPayment", apiMethod="POST", apiRequestBody=local.apiRequestBody);
		local.apiResponse = parseAPIResponse(strResponse=local.strResponse, mode="charge");

		arguments.returnStruct.rawResponse = local.strResponse.rawAPIResponse;
		arguments.returnStruct.responseCode = local.apiResponse.responseCode;
		arguments.returnStruct.responseReasonText = local.apiResponse.responseReasonText;
		arguments.returnStruct.publicResponseReasonText = local.apiResponse.publicResponseReasonText;
		arguments.returnStruct.responseReasonCode = local.seccode; // sec code here so we can use it if needed in post-processing
		arguments.returnStruct.transactionid = local.apiResponse.transactionid;
		arguments.returnStruct.transactionDetail = "Payment by bank account XXXX#right(arguments.qryInfoOnFile.accountNumber,4)#";
		arguments.returnStruct.status = 'Active';
		arguments.returnStruct.GLAccountID = arguments.qryGateWayID.GLAccountID;

		// record history	
		local.xmlResponseInfo = '<response>';
		for (local.fld in arguments.returnStruct) {
			if (local.fld neq "rawResponse")
				local.xmlResponseInfo = local.xmlResponseInfo & "<#lcase(local.fld)#>#xmlformat(arguments.returnStruct[local.fld])#</#lcase(local.fld)#>";
			else
				local.xmlResponseInfo = local.xmlResponseInfo & "<rawresponse><![CDATA[ #arguments.returnStruct.rawresponse# ]]></rawresponse>";
		}
		local.xmlResponseInfo = local.xmlResponseInfo & '</response>';
		
		arguments.returnStruct.historyID = application.objPayments.updatePaymentHistory(historyID=local.returnHistory.historyID, 
			gatewayResponse=local.xmlResponseInfo, responseReasonCode=arguments.returnStruct.responseReasonCode);

		if (arguments.returnStruct.responseCode is 1)
			consolidateMemberPayProfile(orgID=arguments.qryGateWayID.orgID, siteID=arguments.qryGateWayID.siteID, profileID=arguments.qryGateWayID.profileid, payProfileID=arguments.qryInfoOnFile.payProfileID);
		</cfscript>
	
		<cfreturn arguments.returnStruct>
	</cffunction>

	<cffunction name="credit" access="package" returntype="array" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="arrReturnStructs" type="array" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="qryPaymentHistory" type="query" required="yes">
		<cfargument name="paymentTransactionID" type="numeric" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.arrReturnStructs = duplicate(arguments.arrReturnStructs)>
		<cfset structDelete(arguments,"arrReturnStructs")>
		
		<!--- grab empty returnstruct for later --->
		<cfset arguments.returnstruct = duplicate(local.arrReturnStructs[1])>

		<cfset local.arrReturnStructs[1] = doVoidOrRefund(argumentCollection=arguments)>

		<cfreturn local.arrReturnStructs>
	</cffunction>

	<cffunction name="void" access="package" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="returnStruct" type="struct" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="qryPaymentHistory" type="query" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
	
		<cfreturn doVoidOrRefund(argumentCollection=arguments)>
	</cffunction>

	<cffunction name="doVoidOrRefund" access="private" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="returnStruct" type="struct" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="qryPaymentHistory" type="query" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
	
		<cfset var local = structNew()>
	
		<!--- copy args for xml history --->
		<cfset local.strArgsCopy = duplicate(arguments)>
		<cfset StructDelete(local.strArgsCopy,'qryGateWayID')>
		<cfset StructDelete(local.strArgsCopy,'returnStruct')>
		<cfset StructDelete(local.strArgsCopy,'qryGatewayProfileFields')>
		<cfset StructDelete(local.strArgsCopy,'qryPaymentHistory')>
	
		<cftry>
			<cfscript>
			// get info about original payment
			local.rawxmlPaymentInfo = XMLParse(arguments.qryPaymentHistory.paymentInfo);
			local.rawxmlGatewayResponse = XMLParse(arguments.qryPaymentHistory.gatewayResponse);
			local.origPaymentTransactionID = xmlSearch(local.rawxmlGatewayResponse,"string(/response/transactionid/text())");
			local.origPaymentMemberPaymentProfileID = val(arguments.qryPaymentHistory.memberPaymentProfileID);
			local.origPaymentAmount = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/args/x_amount/text())");
			local.origPaymentGLAccountID = xmlSearch(local.rawxmlGatewayResponse,"string(/response/glaccountid/text())");
			local.origPaymentSecCode = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway/seccode/text())");

			local.transaction = getTransactionDetails(MerchantID=arguments.qryGateWayID.gatewayMerchantID, Login=arguments.qryGateWayID.gatewayUsername, Password=arguments.qryGateWayID.gatewayPassword,
									transactionID=local.origPaymentTransactionID);

			if (local.transaction.success) {
				local.refundType = local.transaction.transactioninfo.AuthCode EQ 'Queued For Batch' ? 'void' : 'refund';

				local.apiRequestBody = '{' & 
					'"MerchantID": "#arguments.qryGateWayID.gatewayMerchantID#",' & 
					'"Login": "#arguments.qryGateWayID.gatewayUsername#",' & 
					'"Password": "#arguments.qryGateWayID.gatewayPassword#",' & 
					'"Amount": "#local.origPaymentAmount#",' & 
					'"ConfirmationID": "#local.origPaymentTransactionID#"' & 
				'}';

				local.xmlPaymentInfo = '<#local.refundType# gatewayid="#arguments.qryGateWayID.gatewayID#" profileid="#arguments.qryGateWayID.profileID#"><args>';
				for (local.fld in local.strArgsCopy)
					local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.strArgsCopy[local.fld])#</#lcase(local.fld)#>";
				local.xmlPaymentInfo = local.xmlPaymentInfo & '</args><gateway><![CDATA[ #local.apiRequestBody# ]]></gateway></#local.refundType#>';
				
				local.returnHistory = application.objPayments.insertPaymentHistory(payerMemberID=arguments.qryPaymentHistory.assignedToMemberID, 
					memberPaymentProfileID=local.origPaymentMemberPaymentProfileID, paymentInfo=local.xmlPaymentInfo, 
					gatewayID=arguments.qryGateWayID.gatewayID, profileID=arguments.qryGateWayID.profileID, paymentType=local.refundType);

				// void/refund
				local.strResponse = callGateway(apiURL="#variables.apiRootURL#payments.svc/JSON/SubmitACHVoidReturn", apiMethod="POST", apiRequestBody=local.apiRequestBody);
				local.apiResponse = parseAPIResponse(strResponse=local.strResponse, mode=local.refundType);

				arguments.returnStruct.rawResponse = local.strResponse.rawAPIResponse;
				arguments.returnStruct.responseCode = local.apiResponse.responseCode;
				arguments.returnStruct.responseReasonText = local.apiResponse.responseReasonText;
				arguments.returnStruct.publicResponseReasonText = local.apiResponse.publicResponseReasonText;
				arguments.returnStruct.responseReasonCode = local.origPaymentSecCode; // sec code here so we can use it if needed in post-processing
				arguments.returnStruct.transactionid = local.apiResponse.transactionid;
				arguments.returnStruct.transactionDetail = "#ucFirst(local.refundType)# by bank account #local.transaction.transactioninfo.AccountMask#";
				arguments.returnStruct.refundType = local.refundType;
				arguments.returnStruct.status = "Active";
				arguments.returnStruct.refundAmt = local.origPaymentAmount;
				arguments.returnStruct.GLAccountID = local.origPaymentGLAccountID;
				arguments.returnStruct.historyID = local.returnHistory.historyID;

				// record history
				local.xmlResponseInfo = '<response>';
				for (local.fld in arguments.returnStruct) {
					if (local.fld neq "rawResponse")
						local.xmlResponseInfo = local.xmlResponseInfo & "<#lcase(local.fld)#>#xmlformat(arguments.returnStruct[local.fld])#</#lcase(local.fld)#>";
					else
						local.xmlResponseInfo = local.xmlResponseInfo & "<rawresponse><![CDATA[ #arguments.returnStruct.rawResponse# ]]></rawresponse>";
				}		
				local.xmlResponseInfo = local.xmlResponseInfo & '</response>';

				arguments.returnStruct.historyID = application.objPayments.updatePaymentHistory(historyID=local.returnHistory.historyID, 
					gatewayResponse=local.xmlResponseInfo, responseReasonCode=arguments.returnStruct.responseReasonCode);
			} else {
				var errMsg = "";
				local.transaction.arrErrors.each(function(thisErr) {
					errMsg = "#errMsg#[#arguments.thisErr.msg#]";
				});
				throw(message="Error retrieving transaction details. #errMsg#");
			}
			</cfscript>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset arguments.returnStruct.responseReasonText = cfcatch.message>
			<cfset arguments.returnStruct.publicResponseReasonText = "Refund Failed">
			<cfreturn arguments.returnStruct>
		</cfcatch>
		</cftry>
	
		<cfreturn arguments.returnStruct>
	</cffunction>
	
	<cffunction name="history" access="package" returntype="struct" output="no">
		<cfargument name="qryHistory" type="query" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="maskSensitive" type="boolean" required="yes">
		<cfargument name="typeID" type="numeric" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStr = { strHistory=structNew(), htmlHistory='' }>
		<cfset local.jsonRequest = deserializeJSON(XMLSearch(arguments.qryHistory.paymentInfo,"string(/payment/gateway/text())"))>
				
		<cfsavecontent variable="local.returnStr.htmlHistory">
			<cfoutput>
			<tr class="tsAppBodyText" valign="top">
				<td><b>Routing Number:</b> &nbsp;</td>
				<td>#local.jsonRequest.RoutingNumber#</td>
			</tr>
			<cfset StructInsert(local.returnStr.strHistory,"Routing Number",local.jsonRequest.RoutingNumber,true)>

			<tr class="tsAppBodyText" valign="top">
				<td><b>Account Number:</b> &nbsp;</td>
				<td>XXXX#right(local.jsonRequest.AccountNumber,4)#</td>
			</tr>
			<cfset StructInsert(local.returnStr.strHistory,"Account Number","XXXX"&right(local.jsonRequest.AccountNumber,4),true)>

			<tr class="tsAppBodyText" valign="top">
				<td><b>Type of Account:</b> &nbsp;</td>
				<td>#XMLSearch(arguments.qryHistory.paymentInfo,"string(//args/accttype/text())")#</td>
			</tr>
			<cfset StructInsert(local.returnStr.strHistory,"Type of Account",XMLSearch(arguments.qryHistory.paymentInfo,"string(//args/accttype/text())"),true)>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnStr>
	</cffunction>
	
	<cffunction name="consolidateMemberPayProfile" access="private" returntype="void" output="no">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileID" type="numeric" required="yes">
		<cfargument name="payProfileID" type="numeric" required="yes">
	
		<cfset var local = StructNew()>

		<cfset local.recordedByMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.orgID)>
		<cfif NOT local.recordedByMemberID>
			<cfset local.recordedByMemberID = application.objCommon.getMCSystemMemberID()>
		</cfif>

		<cftry>
			<cfstoredproc procedure="ams_consolidateMemberPayProfiles" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.recordedByMemberID#">
				<cfprocresult name="local.qryConsolidatedProfiles">
			</cfstoredproc>

			<cfif local.qryConsolidatedProfiles.recordcount>
				<cfquery name="local.gatewaySettings" datasource="#application.dsn.membercentral.dsn#">
					SELECT pr.siteID, pr.profileID, pr.profileCode, ga.gatewayType, pr.gatewayUsername, pr.gatewayPassword, pr.gatewayMerchantID
					FROM dbo.mp_profiles as pr
					INNER JOIN dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
					WHERE pr.profileID = <cfqueryparam value="#local.qryConsolidatedProfiles.profileID#" cfsqltype="cf_sql_integer">
					AND pr.[status] = 'A'
				</cfquery>

				<cfset local.threadID = createUUID()>
				<cfthread action="run" name="MPPConsolidation-#local.threadID#" threadid="#local.threadID#" consolidateProfiles="#local.qryConsolidatedProfiles#" gatewaySettings="#local.gatewaySettings#" orgID="#arguments.orgID#">
					<cfloop query="attributes.consolidateProfiles">
						<cftry>
							<cfset thread.tokenArgs = { mcproxy_orgID=attributes.orgID, pmid=attributes.consolidateProfiles.memberID, payProfileID=attributes.consolidateProfiles.payProfileID } >
							<cfset thread.removeResult = CreateObject("component","BankDraft").removePaymentProfile(argumentcollection=thread.tokenArgs)>
						<cfcatch type="any">
							<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=attributes)>
						</cfcatch>			
						</cftry>
					</cfloop>
				</cfthread>
			</cfif>

		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="getTransactionDetails" access="private" output="false" returntype="struct">
		<cfargument name="MerchantID" type="string" required="true">
		<cfargument name="Login" type="string" required="true">
		<cfargument name="Password" type="string" required="true">
		<cfargument name="transactionID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "arrErrors":[], "transactionInfo":{} }>

		<cfset local.apiRequestBody = '{' & 
			'"MerchantID": "#arguments.MerchantID#",' & 
			'"Login": "#arguments.Login#",' & 
			'"Password": "#arguments.Password#",' & 
			'"ReturnAccountMask": true,' & 
			'"Confirmation": "#arguments.transactionID#"' &
		'}'>

		<cfset local.strResponse = callGateway(apiURL="#variables.apiRootURL#payments.svc/JSON/GetTransactionDetails", apiMethod="POST", apiRequestBody=local.apiRequestBody)>
		<cfif local.strResponse.success>
			<cfif local.strResponse.strAPIResponse.Status EQ 'success' AND arrayLen(local.strResponse.strAPIResponse.Transactions)>
				<cfset local.returnStruct = { "success": true, "arrErrors": [], "transactionInfo": local.strResponse.strAPIResponse.Transactions[1] }>
			<cfelse>
				<cfset local.returnStruct.arrErrors.append({ "msg": local.strResponse.strAPIResponse.Message, "errorCode": "INV_TRANSACTIONID" })>
			</cfif>
		<cfelse>
			<cfset local.returnStruct.arrErrors = local.strResponse.arrErrors>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="callGateway" access="private" returntype="struct" output="no">
		<cfargument name="apiURL" type="string" required="true">
		<cfargument name="apiMethod" type="string" required="true">
		<cfargument name="apiRequestBody" type="string" required="false" default="">
		<cfargument name="arrRequestPayload" type="array" required="false" default="#arrayNew(1)#">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "arrErrors":[], "rawAPIResponse":"", "strAPIResponse":{} }>

		<cftry>
			<cfhttp url="#arguments.apiURL#" method="#arguments.apiMethod#" throwonerror="Yes" result="local.APIResult" charset="utf-8" useragent="MemberCentral Payments">
				<cfhttpparam type="HEADER" name="Content-Type" value="application/json">
				<cfif len(arguments.apiRequestBody)>
					<cfhttpparam type="BODY" value="#arguments.apiRequestBody#">
				</cfif>
				<cfif arguments.arrRequestPayload.len()>
					<cfloop array="#arguments.arrRequestPayload#" index="local.param">
						<cfhttpparam type="#local.param.type#" name="#local.param.name#" value="#local.param.value#">
					</cfloop>
				</cfif>
			</cfhttp>

			<cfset local.returnStruct.rawAPIResponse = toString(trim(local.APIResult.fileContent))>
			<cfset local.returnStruct.strAPIResponse = deserializeJSON(local.returnStruct.rawAPIResponse)>

			<cfset local.strLog = { 
				request = { 
					method=arguments.apiMethod, 
					endpoint=arguments.apiURL,
					bodycontent=deserializeJSON(arguments.apiRequestBody)
				}, response = {
					bodycontent=local.returnStruct.strAPIResponse,
					headers=local.APIResult.responseheader,
					statuscode=local.APIResult.status_code
				}}>
			<cfset logAPICall(strCall=local.strLog)>

			<cfif local.returnStruct.strAPIResponse.Status EQ 'failure'>
				<cfset local.returnStruct.arrErrors.append({ "msg":local.returnStruct.strAPIResponse.Message, "errorCode":"callGateway_API_ERR" })>
			</cfif>

			<cfset local.returnStruct.success = local.returnStruct.strAPIResponse.Status EQ 'success'>
		<cfcatch type="Any">
			<cfset local.returnStruct.arrErrors.append({ "msg":cfcatch.message, "errorCode":"callGateway_catch" })>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="logAPICall" output="false" access="private" returntype="void">
		<cfargument name="strCall" type="struct" required="true">

		<cfset var local = structnew()>

		<cftry>
			<cfset local.strRequest = {
				"c":"MCPayECheck",
				"d": {
					"request": {
						"method":arguments.strCall.request.method,
						"endpoint":arguments.strCall.request.endpoint,
						"bodycontent":arguments.strCall.request.bodycontent
					},
					"response": {
						"bodycontent":arguments.strCall.response.bodycontent,
						"headers":arguments.strCall.response.headers,
						"statuscode":arguments.strCall.response.statuscode
					},
					"timestamp":now()
				}
			}>
	
			<cfquery name="local.qryInsertMongoQueue" datasource="#application.dsn.membercentral.dsn#">
				INSERT INTO platformQueue.dbo.queue_mongo (msgjson) 
				VALUES (<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#serializeJSON(local.strRequest)#">)
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="parseAPIResponse" access="private" output="false" returntype="struct">
		<cfargument name="strResponse" type="struct" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = StructNew()>

		<cfswitch expression="#arguments.mode#">
			<cfcase value="charge">
				<cfset local.response = { "responseCode"=3, "responseReasonText"='', "publicResponseReasonText"='', "transactionid"=0 }>
			</cfcase>
			<cfcase value="refund">
				<cfset local.response = { "responseCode"=3,  "responseReasonText"='', "publicResponseReasonText"='', "transactionid"=0 }>
			</cfcase>
			<cfcase value="void">
				<cfset local.response = { "responseCode"=3,  "responseReasonText"='', "publicResponseReasonText"='', "transactionid"=0 }>
			</cfcase>
			<cfdefaultcase>
				<cfset local.response = {}>
			</cfdefaultcase>
		</cfswitch>

		<cftry>
			<cfif arguments.strResponse.arrErrors.len()>
				<cfset local.response.responseCode = 3>
				<cfset local.response.publicResponseReasonText = "Unable to Complete Transaction">				
				<cfloop array="#arguments.strResponse.arrErrors#" index="local.thisErr">
					<cfset local.response.responseReasonText = local.response.responseReasonText & "#local.thisErr.msg# ">
				</cfloop>
			<cfelse>

				<cfswitch expression="#arguments.mode#">
					<cfcase value="charge">
						<cfscript>
						local.response.responseCode = 1;
						local.response.responseReasonText = arguments.strResponse.strAPIResponse.Message;
						local.response.publicResponseReasonText = "";
						local.response.transactionID = arguments.strResponse.strAPIResponse.Confirmation;
						</cfscript>
					</cfcase>
					<cfcase value="void,refund">
						<cfscript>
						local.response.responseCode = 1;
						local.response.responseReasonText = arguments.strResponse.strAPIResponse.Message;
						local.response.publicResponseReasonText = "";
						local.response.transactionID = arguments.strResponse.strAPIResponse.Confirmation;
						</cfscript>
					</cfcase>
				</cfswitch>

			</cfif>
		<cfcatch type="any">
			<cfset local.response.responseCode = 3>
			<cfset local.response.responseReasonText = cfcatch.message & " " & cfcatch.detail>
			<cfset local.response.publicResponseReasonText = "Unable to Complete Transaction">
		</cfcatch>
		</cftry>

		<cfreturn local.response>
	</cffunction>

</cfcomponent>