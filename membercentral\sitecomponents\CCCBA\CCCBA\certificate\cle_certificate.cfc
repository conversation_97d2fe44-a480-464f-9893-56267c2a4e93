<cfcomponent output="false">

	<cffunction name="getCertMergeCodes" access="public" output="false" returntype="struct">
		<cfscript>
			var strCertMergeCodes = {
				"member": "prefix,firstname,middlename,lastname,suffix,professionalsuffix"
			};
			return strCertMergeCodes;
		</cfscript>
	</cffunction>

	<cffunction name="generateCertBody" access="public" output="false" returntype="string">
		<cfargument name="strCertMergeCodes" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.creditTotal = 0>
		<cfset local.arrSpeakers = ArrayNew(1)> 
		<cfloop query="arguments.strCertMergeCodes.credit.qryPossibleCredits">
			<cfif arguments.strCertMergeCodes.credit.qryPossibleCredits.creditValue>
				 <cfset local.creditTotal = local.creditTotal + arguments.strCertMergeCodes.credit.qryPossibleCredits.creditValue>
			</cfif>
		</cfloop>  
		<cfif structKeyExists(arguments.strCertMergeCodes.event,"qryEventRoles")>
			<cfloop query="arguments.strCertMergeCodes.event.qryEventRoles">
				<cfif FindNoCase("Speaker",arguments.strCertMergeCodes.event.qryEventRoles.categoryName)>
					<cfset ArrayAppend(local.arrSpeakers,arguments.strCertMergeCodes.event.qryEventRoles.fullName)>	
				</cfif>
			</cfloop>	
		</cfif> 
		<cfquery name="local.qryPendingCredit" dbtype="query">
			select creditValue, creditType from arguments.strCertMergeCodes.credit.qryPossibleCredits where status = 'Pending'
		</cfquery>
		<cfquery name="local.qryApprovedCredit" dbtype="query">
			select creditValue, creditType from arguments.strCertMergeCodes.credit.qryPossibleCredits where status = 'Approved'
		</cfquery>   
		<cfsavecontent variable="local.registrantName">
			<cfoutput>#UCASE(arguments.strCertMergeCodes.member.firstname & " " & arguments.strCertMergeCodes.member.middlename & " " & arguments.strCertMergeCodes.member.lastname & " " & arguments.strCertMergeCodes.member.suffix)#</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<html>
				<head>
				<title>Official Certificate of Attendance</title>
				<style>
					@font-face { font-family:Tahoma;panose-1:2 11 6 4 3 5 4 4 2 4;}
					@font-face { font-family:Verdana;panose-1:2 11 6 4 3 5 4 4 2 4; }
					.borders{position:relative; z-index:10; margin:4px;  border:2px solid ##740730; }
					p.MsoNormal, td.MsoNormal, li.MsoNormal, div.MsoNormal { margin:0in; margin-bottom:.0001pt;font-size:12.0pt;font-family:Verdana; }
					.Section1{ size:8.5in 11.0in; border:8px solid ##1e7cab; padding:15px; margin-right:30px;
					background-image: url(#arguments.strCertMergeCodes.certificate.imagesurl#certificate_watermark.png); background-position:center;background-repeat: no-repeat;}
					div.Section1{ page:Section1; }
					tr { vertical-align:top; }
					div.MsoNormal, td.MsoNormal { font-size:8.0pt; font-family:Verdana; }
					table { page-break-inside:auto }
					tr    { page-break-inside:avoid; page-break-after:auto }
					.headWrapTr td div{	
							text-align:left;
							padding-top: 8px;
							font-family: Verdana;
						}
					.midheading{
						color:##1e7cab;
					}
					.text-center{
						text-align:center;
					}
					.text-right{
						text-align:right;
					}
					.text-left{
						text-align:left;
					}
					div.MsoNormal, td.MsoNormal { font-size:7.2pt; font-family:Verdana; }
					body { height:11in;width:8.5in;padding:.2in;}
					
				</style>
				</head>
				<body>
					<div class="Section1">	
						<p  align=center style="margin:0px;"><img src="#arguments.strCertMergeCodes.certificate.imagesurl#certHeading.jpg" height="50px"></p>
						<table width="100%" cellpadding="2" cellspacing="0" style="margin-bottom: 0px; " >
									
							<tr class="headWrapTr">
								<td class="text-left" style="font-size:8pt;">
									<b>Please keep this copy for your record</b>
								</td>
								<td class="text-center midheading">
									<b>for California MCLE</b>
								</td>
								<td class="text-right" style="font-size:8pt;">
										<b>PROVIDER ##393</b> <br/> 
										Contra Costa County Bar Association <br/>2300 Clayton Rd., 520, Concord, CA 94520 <br/> 
										(************* <br/>	
								</td>
							</tr>
						</table>
						<br/>
						<p class=MsoNormal><span style='font-size:8pt;line-height:9pt;'><b>Certificate of Attendance:</b> 
							This is an Official Certificate of Attendance which is serialized and can be verified by MemberCentral. The certificate includes a list of continuing education authorities which have approved this program for credit. This information may be useful for presumptive approval in other jurisdictions, however, this Certificate may or may not be accepted by other continuing education authorities in other jurisdictions. Please consult the specific rules of your continuing education authority for clarification. MemberCentral cannot provide additional information or file for certification in other continuing education jurisdictions once a program has completed. You are responsible for keeping a copy of the materials and Certificate of Attendance for your records and for following the continuing education guidelines with your continuing education authority.
						</span></p>
						<p class=MsoNormal><span style='font-size:8pt;'>&nbsp;</span></p>
						<p class=MsoNormal><span style='font-size:8pt;line-height:9pt;'><b>Registrar Statement of Authenticity:</b> 
							This certificate is submitted to <b>#UCASE(local.registrantName)#</b> because MemberCentral can certify that the participant attended this program for the specified duration of the program, and <b>#UCASE(local.registrantName)#</b> additionally attests to his/her/their attendance by his/her/their signature. This Certificate of Attendance is NOT provided to participants who did not attend the program; this policy is strictly enforced.
						</span></p>
						<p class=MsoNormal><span style='font-size:8pt;'>&nbsp;</span></p>
						<p class=MsoNormal><span style='font-size:8pt;line-height:9pt;'><b>Program Status by Continuing Education Accrediting Authority: (as of #dateformat(now(),"mmmm d, yyyy")#)</b><br/>
							Credit Approved:<cfif local.qryApprovedCredit.recordcount><cfloop query="local.qryApprovedCredit"><nobr> <cfif len(local.qryApprovedCredit.creditType)>CA -#numberFormat(local.qryApprovedCredit.creditValue,'__.00')# - #local.qryApprovedCredit.creditType#</cfif></nobr><cfif local.qryApprovedCredit.currentrow neq local.qryApprovedCredit.recordcount>; </cfif></cfloop><cfelse>(none)</cfif><br/>
							Credit Pending: <cfif local.qryPendingCredit.recordcount><cfloop query="local.qryPendingCredit"><cfif len(local.qryPendingCredit.creditType)> CA - #numberFormat(local.qryPendingCredit.creditValue,'__.00')# - #local.qryPendingCredit.creditType#</cfif> <cfif local.qryPendingCredit.currentrow neq local.qryPendingCredit.recordcount>, </cfif></cfloop><cfelse>(none)</cfif><br/>
						</span></p>
						<br/>
						<table width="100%" cellpadding="1" cellspacing="0">
							<tr>
								<td class=MsoNormal><b>Title:</b> #arguments.strCertMergeCodes.event.qryEventMeta.eventContentTitle#</td>
							</tr>
							<cfif arraylen(local.arrSpeakers)>
								<tr>
									<td class=MsoNormal><b>Speaker(s):</b>#arrayToList(local.arrSpeakers,"; ")#</td>
								</tr>
							</cfif>
							<tr><td> &nbsp;</td></tr>
							<tr>
								<td class=MsoNormal><b>Date of Activity:</b> #DateFormat(arguments.strCertMergeCodes.event.qryEventTimes.endTime,"mm/dd/yyyy")#  </td>
								</tr>
							<tr>
								<td class=MsoNormal><b>Time of Activity:</b> #timeformat(arguments.strCertMergeCodes.event.qryEventTimes.startTime,"h:mm tt")# - #timeformat(arguments.strCertMergeCodes.event.qryEventTimes.endTime,"h:mm tt")# #arguments.strCertMergeCodes.event.qryEventTimes.timeZoneAbbr#</td>
								
							</tr>
							<tr>
								<td class=MsoNormal><b>Location:</b> #arguments.strCertMergeCodes.event.qryEventMeta.locationContentTitle#</td>
								
							</tr>
							<tr><td> &nbsp;</td></tr>
							<tr>
								<td class=MsoNormal><b>Format:</b> Live</td>
							</tr>
							<tr>
								<td class=MsoNormal><b>Total Eligible California MCLE Credit Hours:</b>#numberFormat(local.creditTotal,'__.00')#</td>
							</tr>
							<tr><td> &nbsp;</td></tr>
							<tr><td class="MsoNormal" > To Be Completed by the Attorney after Participation in the Above-Named Activity</td></tr>
							<tr><td> &nbsp;</td></tr>
							<tr><td class="MsoNormal" > By signing below, I certify that I participated in the activity described above and am entitled to claim the following California <b>MCLE Credit Hours</b>:<br/>
								<span class="b">
								<cfset local.count = 0>
								<cfloop query="arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate">
									<cfset local.count = local.count+1>
									<cfif arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded>
										#arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditType#: #NumberFormat(arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded, "____.__")#<cfif local.count GT 0 AND local.count NEQ arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.recordcount>, </cfif>
									</cfif>
								</cfloop>                           
								</span>
							</td></tr>
							<tr><td> &nbsp;</td></tr>
							<tr><td class=MsoNormal>Print your name clearly: <div style="border-bottom: 1px solid ##000; display: inline-block; width: 200px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</div></td></tr>
							
							<tr><td class=MsoNormal>Your State Bar Number: <div style="border-bottom: 1px solid ##000; display: inline-block; width: 200px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</div></td></tr>
							
							<tr><td class=MsoNormal>Signature: <div style="border-bottom: 1px solid ##000; display: inline-block; width: 200px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</div></td></tr>
							<tr><td> &nbsp;</td></tr>
							<tr><td> &nbsp;</td></tr>	
							<tr>
								<td class="MsoNormal" ><b>Reminder:</b>&nbsp;Sign and keep this record of attendance for 4 years. In the event that you are audited by the State Bar, you may be requested to submit this record of attendance to the State Bar.
								<br/>
								Send this to the State Bar only if you are audited. If the provider has not granted credit for legal ethics, elimination of bias or competence issues, you cannot claim credit in this area.</td>
							</tr>
						</table>
					</div>
					
				</body>
			</html>        
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

</cfcomponent>
