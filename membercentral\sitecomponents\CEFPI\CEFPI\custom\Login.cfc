<cfcomponent>
	<cfset variables.ssowsdlURL = "https://www.a4le.org/AsiCommon/Services/Membership/MembershipWebService.asmx?wsdl">
	<cfset variables.ssoTokenVerify = "https://www.a4le.org/Custom/SSO/A4LE_SSO_GetInfo.aspx">
	<cfset variables.ssoapiKey = ToBase64("ABCDEFGH")>
	<cfset variables.ssoapiBaseVector = ToBinary(ToBase64("PlCKeyWS"))>

	<cffunction name="showLoginForm" access="public" returntype="string" output="no">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfif structKeyExists(URL, "token")>
			<cfset local.result = loginUser("","","",event)>
			<cfif local.result eq "true">
				<cflocation url="/" addtoken="false">			
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<table width="60%" border="0" cellspacing="0" cellpadding="8" align="center" bgcolor="##fff" class="loginpagetext" style="border:1px solid ##1C4076;;">
			<tr>
				<td colspan="2" bgcolor="##1C4076" class="loginPageTitle"><span style="font-family:garamond;color:##fff;">Please Log In To A4LE</span></td>
			</tr>
			<tr style="background:##fff;">
				<td class="loginPage" valign="top" colspan="2">
					<h4>Please use your A4LE Login if you do not have an A4LE account <a href="https://www.a4le.org/A4LE/Create_Account/A4LE/Contacts/Create_Account_General.aspx"> click here</a> to create an account</a> </h4>								
				</td>
			</tr>
			<tr style="background:##fff;">
				<td class="loginPage" valign="top" width="250">
					<div style="line-height:1.5em;" style="font-family:verdana;font-size:12px;">	
						<form method="post" action="/?pg=login" class="mc_form_login">
							#application.objUser.renderSecurityKeyElement()#
							<span style="color:##4a4d38;font-family:arial;font-size:14px;">Username:</span><br/>
							<input class="loginPage" type="text" name="username" id="username" size="25" value="#session.cfcuser.memberdata.username#"><br/>
							<span style="color:##4a4d38;font-family:arial;font-size:14px;">Password:</span><br>
							<input class="loginPage" type="password" name="password" id="password" size="25" value=""><br/>
							<button class="loginPage" type="submit" name="btnLogin" style="font-family:verdana;font-size:12px;">LOGIN</button>
						</form><br />
						<a style="font:12px arial;color:##1C4076;" href="https://www.a4le.org/AsiCommon/Controls/Shared/FormsAuthentication/RecoverPassword.aspx">Forgot Username or Password</a>
					</div>
				</td>
				<td class="loginPage"><div id="loginSponsorContainer" style="max-width:180px;margin-left:auto;margin-right:auto;"></div></td>
			</tr>
			<tr>
				<td colspan="2" valign="top" class="loginPage">
					<div id="loginErrorDIV" class="loginError" style="border:none;">
						<cfif arguments.event.getValue('showErrMessage',0) gt 0>
								<cfif arguments.event.getValue('showErrMessage') is 1>
								Login failed. Try again.
							</cfif>
						</cfif>
						<div id="cookies-disabled" style="display:none" class="tsAppBodyText">Your computer is not accepting cookies from our site. You must accept cookies in order to login.</div>
					</div>
				</td>
			</tr>
			</table>
			<br /><br />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="loginUser" access="public" returntype="string" output="false">
		<cfargument name="username" type="string" required="true">
		<cfargument name="password" type="string" required="true">
		<cfargument name="sitecode" type="string" required="true">
		<cfargument name="Event" type="any">
		
		<cfset var local = StructNew()>

		<cfsetting requesttimeout="100">

		<cfscript>
		// trim incoming vars
		arguments.username = left(trim(arguments.username),75);
		arguments.password = left(trim(arguments.password),100);
		local.token = "";
		local.success = "false";

		// if no username/password, reject
		// if token is not passed in.
		if (NOT structKeyExists(URL, "token")) {
			if (len(arguments.username) is 0 or len(arguments.password) is 0)
			return "false";
		}
		else {
			local.token = URL.token;
			structDelete(URL, "token");
		}
		</cfscript>
		
		<!--- attempt login for superusers --->
		<cfset local.strLoginAttemptSU = application.objUser.login_attemptSuperUser(sessionID=session.cfcuser.statsSessionID, siteID=application.objSiteInfo.getSiteInfo(arguments.siteCode).siteid, 
			username=arguments.username, password=arguments.password)>
		<cfif local.strLoginAttemptSU.result eq "pass">
			<cfset session.cfcuser.loggedin = 1>
			<cfset session.cfcuser.memberdata.memberid = local.strLoginAttemptSU.memberID>
			<cfset session.cfcuser.memberdata.identifiedAsMemberID = 0>
			<cfset structAppend(session.cfcuser,application.objUser.refresh(cfcuser=session.cfcuser, siteID=application.objSiteInfo.getSiteInfo(arguments.siteCode).siteID, orgcode=application.objSiteInfo.getSiteInfo(arguments.siteCode).orgcode),true)>
			<cfset local.success = "true">
			<cfset createObject("component","model.login.login").recordLogin(sitecode=arguments.sitecode, requestByMemberID=0)>
			<cfset application.objPlatformStats.updateStatsSessionID()>
			<cfreturn local.success>
		</cfif>

		<!--- IMIS Login if no token--->
		<cfif local.token EQ "">
			<cftry>
				<cfinvoke webservice="#variables.ssowsdlURL#" method="LoginUserAndProvideCookies" returnvariable="local.imisResult" timeout="20">
					<cfinvokeargument name="username" value="#arguments.username#">
					<cfinvokeargument name="password" value="#arguments.password#">
					<cfinvokeargument name="staffUser" value="false">
				</cfinvoke>
				<cfcatch type="any">
					<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>
					<cfset local.success = "false">		
					<cfreturn local.success>
				</cfcatch>
			</cftry>

			<!--- create necessary cookies --->
			<cfif len(trim(local.imisResult))>
				<cfset local.imisResult = Replace(trim(local.imisResult), "|Login=|Login=", "|Login=")>
				<cfloop list="#local.imisResult#" index="local.cookieStr" delimiters="|">
					<cfset local.cookieParts = ListToArray(local.cookieStr,"=")>
					<cfcookie name="#local.cookieParts[1]#" value="#local.cookieParts[2]#" secure="No" domain="a4le.org">
				</cfloop>

				<cfset local.CookieLoginIDValue = encrypt(arguments.username, variables.ssoapiKey, "DES/CBC/PKCS5Padding", "BASE64", variables.ssoapiBaseVector)>
				<cfcookie name="A4LE.Username" value="#local.CookieLoginIDValue#" secure="No" domain="a4le.org">
			<cfelse>
				<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>			
				<cfset local.success = "false">		
				<cfreturn local.success>	
			</cfif>
		</cfif>

		<!--- GET THE IMIS ID and member data --->
		<cftry>
			<cfif local.token NEQ "">
				<cfhttp url="#variables.ssoTokenVerify#?token=#local.token#" method="get" useragent="MemberCentral.com" throwonerror="true" result="local.apiResult"></cfhttp>
				<cfset local.imisJSON = left(local.apiResult.filecontent, findNoCase('<span id="lblMessage">', local.apiResult.filecontent)-1)>
			<cfelse>
				<cfhttp url="#variables.ssoTokenVerify#" method="post" useragent="MemberCentral.com" throwonerror="true" result="local.apiResult">
					<cfhttpparam  type="formfield" name="txtLogin" value="#arguments.username#">
					<cfhttpparam type="formfield" name="txtPassword" value="#arguments.password#">
				</cfhttp>
		
				<cfset local.imisJSON = left(local.apiResult.filecontent, findNoCase('<span id="lblMessage">', local.apiResult.filecontent)-1)>
			</cfif>
			<!--- get member data from IMIS SSO --->
			<cfset local.jsonResponse = deserializeJSON(local.imisJSON)>
			<cfif local.jsonResponse.lastName EQ "">
				<cfset local.jsonResponse.lastName = local.jsonResponse.firstName>
			</cfif>
			
			<cfif local.jsonResponse.status EQ "false">
				<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>			
				<cfset local.success = "false">		
				<cfreturn local.success>
			</cfif>

			<cfset local.IMISID = local.jsonResponse.UserId>
			
			<cfcatch type="any">
				<cfset local.aaCatch = cfcatch>
				<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>			
				<cfset local.success = "false">		
				<cfreturn local.success>
			</cfcatch>
		</cftry>

		<cftry>
			<cfquery name="local.qryGetMemberIDFromMemberNumber" datasource="#application.dsn.membercentral.dsn#">
				select top 1 memberID
				from dbo.ams_members 
				where orgID = #arguments.event.getValue('mc_siteinfo.orgID')#
				and membernumber = <cfqueryparam value="#local.IMISID#" cfsqltype="CF_SQL_VARCHAR"> 
				and status <> 'D'
			</cfquery>

			<cfscript>
			local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=val(local.qryGetMemberIDFromMemberNumber.memberID));
			local.objSaveMember.setDemo(
				membernumber=local.IMISID,
				firstname= local.jsonResponse.firstName,
				lastname=local.jsonResponse.lastName
			);
			local.objSaveMember.setMemberStatus(memberStatus='Active');
			local.objSaveMember.setMemberType(memberType='User');
			local.objSaveMember.setEmail(type='Email', value= local.jsonResponse.emailAddress);
			local.objSaveMember.setCustomField(field='CustomerType', value= local.jsonResponse.CustomerType);

			local.strResult = local.objSaveMember.saveData();
			</cfscript>

			<cfif NOT local.strResult.success>
				<cfthrow message="Saving Member Info was not successful.">
			</cfif>

			<!--- If iMIS gives us a redirect URL then use it after we verify login --->
			<cfif IsDefined("cookie.returnURL")>
				<cfset structDelete(cookie,"returnURL",false)>
			</cfif>				

			<cfset local.objPlatformMember = structNew()>

			<!--- try to login --->
			<cfset local.objPlatformMember.profileID = application.objMember.getMemberProfileID(local.strResult.memberID, arguments.event.getValue('mc_siteinfo.siteID'), arguments.event.getValue('mc_siteinfo.orgID'))>
			<cfset local.objPlatformMember.qryMember = application.objMember.getMemberInfo(local.strResult.memberID, arguments.event.getValue('mc_siteinfo.orgID'))>
			
			<cfif local.objPlatformMember.profileID gt 0>
				<cfset session.cfcuser.loggedin = 1>
				<cfset session.cfcuser.memberdata.memberid = val(local.objPlatformMember.qryMember.memberid)>
				<cfset session.cfcuser.memberdata.identifiedAsMemberID = 0>
				<cfset StructAppend(session.cfcuser,application.objUser.refresh(cfcuser=session.cfcuser, siteID=arguments.event.getValue('mc_siteinfo.siteid'), orgcode=arguments.event.getValue('mc_siteinfo.orgCode')),true)>
				<cfset local.success = true>
				<cfset createObject("component","model.login.login").recordLogin(sitecode=arguments.event.getValue('mc_siteinfo.sitecode'))>
				<cfset application.objPlatformStats.updateStatsSessionID()>
			<cfelse>
				<cfset local.success = autoLoginSiteUser(arguments.event.getValue('mc_siteinfo.siteCode'), local.strResult.memberID)>
				<!--- This is needed to peform the affiliation step --->
				<cfif local.token NEQ "">
					<cfif not structKeyExists(session,"MCSecurityKey")>
						<cfset session["MCSecurityKey"] = hash(createUUID(), 'SHA-512', 'UTF-8')>
						<cfset arguments.event.setValue('mcSecKey',session.MCSecurityKey)>						
					</cfif>
					<cflocation url="/?pg=login&logact=affiliate" addtoken="No">
				</cfif>
			</cfif>

			<cfset CreateObject("component","model.login.login").setCredentialsCookie(username=arguments.username)>

			<cfif isDefined("session.forceRedirectURL") AND len(session.forceRedirectURL)>
				<cfset session.remoteReturnURL = session.forceRedirectURL>
			</cfif>
			<!--- If in the affiliation process don't redirect --->
			<cfif local.token NEQ "" AND local.success NEQ "affiliate">
				<cfif structKeyExists(local.jsonResponse, "returnURL") AND len(local.jsonResponse.returnURL) GT 0>
					<cflocation url="#local.jsonResponse.returnURL#" addtoken="false">			
				<cfelse>
					<cflocation url="/" addtoken="false">			
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>			
			<cfset local.success = "false">		
			<cfreturn local.success>
		</cfcatch>
		</cftry>

		<cfreturn local.success>		
	</cffunction>

	<cffunction name="logout" access="public" returntype="void">
		<cfargument name="Event" type="any">

		<!--- try to logout of imis --->
		<cftry>
			<cfinvoke webservice="#variables.ssowsdlURL#" method="Logout" returnVariable="local.logoutResponse" />
		<cfcatch type="any">
		</cfcatch>
		</cftry>

		<!--- delete all domain level cookies set during login --->
		<cfloop collection="#cookie#" item="local.cookieName">
			<cfcookie name="#local.cookieName#" value="" secure="No" domain="a4le.org" expires="now">
		</cfloop>

		<!--- standard MC logout code --->
		<cfset application.objPlatform.resetSession(doRedirect=true, customRedirectURL="")>
	</cffunction>

	<cffunction name="autoLoginSiteUser" access="public" returntype="string">
		<cfargument name="sitecode" type="string" required="true">
		<cfargument name="memberid" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<!--- Lookup member data for first time user process. --->
		<cfquery name="local.checkMemberDefault" datasource="#application.dsn.membercentral.dsn#">
			select top 1 m.memberid, msd.siteid, msd.status as defaultStatus, m.status as memberStatus
			from dbo.ams_memberSiteDefaults as msd
			inner join dbo.ams_members as m on m.memberid = msd.memberID and m.status in ('A','I')
			where msd.siteID = <cfqueryparam value="#application.objSiteInfo.getSiteInfo(arguments.siteCode).siteid#" cfsqltype="CF_SQL_INTEGER">
			and msd.status in ('A','I')
			and m.memberid = <cfqueryparam value="#arguments.memberid#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<!--- Has a default username/password. --->
		<cfif local.checkMemberDefault.RecordCount is 1>

			<!--- inactive user --->
			<cfif local.checkMemberDefault.defaultStatus neq "A" or local.checkMemberDefault.memberStatus neq "A">
				<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>
				<cfset local.success = "inactive">

			<!--- active user, so send to affiliate ---> 
			<cfelse>
				<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>
				<cfset session.cfcuser.memberdata.identifiedAsMemberID = val(local.checkMemberDefault.memberid)>
				<cfset local.success = "affiliate">
			</cfif>

		<!--- Does not have a default username/password --->
		<cfelse>
			<cfset StructAppend(session.cfcuser,application.objUser.initUser(),true)>
			<cfset local.success = "false">
		</cfif>

		<cfreturn local.success>
	</cffunction>
	
</cfcomponent>