<cfoutput>
    <div style="padding:5px">
        <h5>AI Expert List</h5>
        #getPaginationHTML(topOrBottom="top",recordCount=local.qryAllAIExperts.recordCount,itemCount=val(local.qryAllAIExperts.itemCount), docType="expert",maxRows=local.maxRows,startRow=local.startrow,tab='PTSAI',viewDirectory=local.viewDirectory,noRecordsMsg="You have not set up and TrialSmith AI Experts.")#

        <cfoutput query="local.qryAllAIExperts">
            <div class="row-fluid s_row <cfif local.qryAllAIExperts.currentrow mod 2 is 0>s_row_alt</cfif>">
                <div class="span12">
                    <div class="row-fluid">
                        <div class="span10">
                            <b>#local.qryAllAIExperts.first_name# #local.qryAllAIExperts.last_name#</b>&nbsp;(#local.qryAllAIExperts.caseReference#)
                            <div class="s_dtl">
                                <div align="left"><b class="s_label">Purchased:</b> #dateformat(local.qryAllAIExperts.caseDateCreated,"m/d/yyyy")#</div>
                            </div>
                        </div>
                        <div class="span2 s_act">
                            <div class="s_opt">
                                <a href="/?pg=myDocuments&tab=trialsmithchat&mode=direct&expertid=#local.qryAllAIExperts.caseExpertID#&caseid=#local.qryAllAIExperts.caseID#" title="Start Chat"><i class="icon icon-file-text" style="line-height:23px;"></i> Start Chat</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </cfoutput>
        #getPaginationHTML(topOrBottom="bottom",recordCount=local.qryAllAIExperts.recordCount,itemCount=val(local.qryAllAIExperts.itemCount), docType="Expert",maxRows=local.maxRows,startRow=local.startrow,tab='PTSAI',viewDirectory=local.viewDirectory,noRecordsMsg="You have not purchased any verdict reports.")#
    </div>
</cfoutput>		