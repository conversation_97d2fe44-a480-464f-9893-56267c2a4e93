﻿<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="400">
		
		<cfscript>
			local.arrTaskFields = [ 
				{ name="BatchSize", type="INTEGER", desc="Batch Size", value="50" },
				{ name="Threads", type="INTEGER", desc="Number of Threads", value="4" } 
			];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>		

		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<cfset local.processQueueResult = processQueue(batchSize=local.strTaskFields.batchSize, threads=local.strTaskFields.threads)>
			<cfif NOT local.processQueueResult.success>
				<cfthrow message="Error running processQueue()">
			</cfif>

			<cfset local.messageTypeID = arguments.strTask.scheduledTasksUtils.getMessageTypeID(messageTypeCode="CACLEREPORT")>
			<cfset local.success = processEmailBatch(messageTypeID=local.messageTypeID)>
			<cfif NOT local.success>
				<cfthrow message="Error running processEmailBatch()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="batchSize" type="numeric" required="true">
		<cfargument name="threads" type="numeric" required="true">
		
		<cfset var local = structnew()>
		<cfset var reportdesign = application.birtSystem.engine.openReportDesign("/app/models/reports/events/clereport.rptdesign")>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfstoredproc procedure="job_CLEReport_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#application.paths.SharedTemp.pathUNC#clereportmember_">
				<cfprocresult name="local.qryMembers" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qryMembers.recordCount>
		
			<cfscript>
				// loop per member
				QueryEach(local.qryMembers, function(struct thisMember, numeric currentrow, query qryMembers) {

					var PDF_OPTIONS = createObject("java","org.eclipse.birt.report.engine.api.PDFRenderOption").init();
					var tempFolder = application.paths.SharedTemp.path & "clereport/";
					var pdfFileName = "";
					var reportXMLFile = "";
					var tempPDFFileName = "";
					var tempXMLFileName = "";
					var task = application.birtSystem.engine.createRunAndRenderTask(reportdesign) ;
					var paramSet = arrayNew(1);
					
					if (not directoryExists(tempFolder)) {
						DirectoryCreate(tempFolder);
					}					

					// item must still be in the grabbedForProcessing state for this job. else skip it. 
					// this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. 
					if (queueItemHasStatus(queueType='caCLECredits',queueStatus='grabbedForProcessing',itemUID=thisMember.itemUID,jobUID=thisMember.jobUID)) {
						try {
							queueSetStatus(queueType='caCLECredits',queueStatus='processingReport',itemUID=thisMember.itemUID);
		
							PDF_OPTIONS.setOutputFormat("pdf");
							PDF_OPTIONS.setEmbededFont(false);
							
							pdfFileName = "#thisMember.fullname#_#thisMember.memberNumber#";
							pdfFileName = replace(replace(rereplaceNoCase(pdfFileName,'[^A-Z0-9 ]','','ALL'),' ','_','ALL'),'__','_','ALL');
							pdfFileName = "#application.paths.SharedTemp.path#clereportmember_#thisMember.itemGroupUID#/#pdfFileName#.pdf" ;
							reportXMLFile = "#application.paths.SharedTemp.path#clereportmember_#thisMember.itemGroupUID#/#thisMember.itemUID#.xml" ;

							tempPDFFileName = tempFolder & thisMember.itemUID & ".pdf" ;
							tempXMLFileName = tempFolder & thisMember.itemUID & ".xml" ;

							if (fileExists(tempPDFFileName)) {
								fileDelete(tempPDFFileName);
							}
							if (fileExists(tempXMLFileName)) {
								fileDelete(tempXMLFileName);
							}

							fileCopy(reportXMLFile,tempXMLFileName);

							PDF_OPTIONS.setOutputFileName(toString(tempPDFFileName)) ;

							task.setParameterValue("xmlFilePath", toString(tempXMLFileName));
							paramSet = XMLSearch(thisMember.xmlConfigParam,'/params/param');
							for (var thisParam in paramSet) {
								task.setParameterValue(toString(thisParam.reportParam.xmlText), toString(thisParam.paramvalue.xmlText));
							}
							task.setRenderOption(PDF_OPTIONS);
							task.validateParameters();
							task.run();
							task.close();

							fileDelete(tempXMLFileName);
							if (fileExists(pdfFileName)) {
								fileDelete(pdfFileName);
							}
							fileMove(tempPDFFileName,pdfFileName);

							queueSetStatus(queueType='caCLECredits',queueStatus='readyToNotify',itemUID=thisMember.itemUID);

						} catch (e) {
							application.objError.sendError(cfcatch=e, objectToDump=local);
							rethrow;
						}
					}

				}, true, arguments.threads);
			</cfscript>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
					
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="processEmailBatch" access="private" output="false" returntype="boolean">
		<cfargument name="messageTypeID" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.success = true>

		<cfset local.batchCount = 1>

		<cftry>
			<!--- ----------------------------------------------------------------------------------- --->
			<!--- post processing: process any notifications for itemGroupUIDs that are readyToNotify --->
			<!--- ----------------------------------------------------------------------------------- --->
			<cfquery name="local.qryNotifications" datasource="#application.dsn.platformQueue.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					EXEC dbo.job_CLEReport_grabForNotification @batchsize=#local.batchCount#;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfif local.qryNotifications.recordcount>
				<cfoutput query="local.qryNotifications">
					<cftry>
						<!--- compile pdfs into one zip --->
						<cfif directoryExists("#application.paths.SharedTemp.path#clereportmember_#local.qryNotifications.itemGroupUID#")>
							<cfzip action="zip" source="#application.paths.SharedTemp.path#clereportmember_#local.qryNotifications.itemGroupUID#" 
								file="#application.paths.SharedTemp.path#clereportmember_#local.qryNotifications.itemGroupUID#/CLEReport.zip" filter="*.pdf"
								recurse="false" storepath="false"></cfzip>
						<cfelse>
							<cfthrow message="Folder of PDFs was not found">
						</cfif>						

						<!--- prep and send email --->
						<cfset local.thisReportEmail = local.qryNotifications.reportEmail>
						<cfset local.thisSiteName = local.qryNotifications.siteName>
						<cfset local.thisSiteCode = local.qryNotifications.siteCode>

						<cfif len(local.thisReportEmail) and fileExists("#application.paths.SharedTemp.path#clereportmember_#local.qryNotifications.itemGroupUID#/CLEReport.zip")>
							<!--- Generate download link instead of attachment --->
							<cfset local.objReportHelper = CreateObject("component","models.reports.report")>
							<cfset local.reportDocResult = local.objReportHelper.generateReportDocument(
								siteID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteID,
								siteCode=local.thisSiteCode,
								orgCode=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].orgCode,
								directoryPath=application.paths.SharedTemp.path & "clereportmember_" & local.qryNotifications.itemGroupUID,
								memberID=val(local.qryNotifications.memberID),
								reportFileName="CLEReport.zip",
								reportTitle="CLE Credit Report for " & local.thisSiteName,
								reportDescription="ZIP file containing CLE credit PDFs for selected members"
							)>

							<cfsavecontent variable="local.thisEmailContent">
								<div>
									We have completed processing your CLE Credit Report.<br/><br/>
									<cfif local.reportDocResult.success>
										#local.objReportHelper.generateDownloadLinkHTML(local.reportDocResult.downloadLink, "CLEReport.zip", "Download CLE Credit Report", local.reportDocResult.expirationDays)#
									<cfelse>
										<p>Error generating download link for CLE Credit Report.</p>
									</cfif>
								</div>
								<br/>
								<div><b>Questions?</b><br/>Contact <NAME_EMAIL> if you have any questions about this report.</div>
							</cfsavecontent>

							<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name="MemberCentral", email=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].networkEmailFrom},
								emailto=[{ name="#local.qryNotifications.firstname# #local.qryNotifications.lastname#", email=local.thisReportEmail }],
								emailreplyto=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].supportProviderEmail,
								emailsubject="CLE Credit Report for #local.thisSiteName#",
								emailtitle="#local.thisSiteName# CLE Credit Report",
								emailhtmlcontent=local.thisEmailContent,
								siteID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteID,
								memberID=val(local.qryNotifications.memberID),
								messageTypeID=arguments.messageTypeID,
								sendingSiteResourceID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteSiteResourceID
								)>
						</cfif>

						<!--- update status --->
						<cfif fileExists("#application.paths.SharedTemp.path#clereportmember_#local.qryNotifications.itemGroupUID#/CLEReport.zip")>
							<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
								set nocount on;
		
								declare @newstatus int;
								select @newstatus = qs.queueStatusID 
									from dbo.tblQueueStatuses as qs
									inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
									where qt.queueType = 'caCLECredits'
									and qs.queueStatus = 'done';
								
								update qi WITH (UPDLOCK, HOLDLOCK)
								set qi.queueStatusID = @newstatus,
									qi.dateUpdated = getdate()
								from dbo.tblQueueItems as qi
								inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
								where qid.itemGroupUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#local.qryNotifications.itemGroupUID#">;
							</cfquery>
						</cfif>

					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfcatch>
					</cftry>

				</cfoutput>
			</cfif>

			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cfquery name="local.qryClear" datasource="#application.dsn.platformQueue.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					EXEC dbo.job_CLEReport_clearDone;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>
					
		<cfreturn local.success>
	</cffunction>

	<cffunction name="queueSetStatus" access="private" returntype="void" output="false">
		<cfargument name="queueType" type="string" required="true">
		<cfargument name="queueStatus" type="string" required="true">
		<cfargument name="itemUID" type="string" required="true">

		<cfstoredproc procedure="queue_setStatus" datasource="#application.dsn.platformQueue.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueType#">
			<cfprocparam type="In" cfsqltype="CF_SQL_IDSTAMP" value="#arguments.itemUID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueStatus#">
		</cfstoredproc>
	</cffunction>
	
	<cffunction name="queueItemHasStatus" access="private" returntype="boolean" output="false">
		<cfargument name="queueType" type="string" required="true">
		<cfargument name="queueStatus" type="string" required="true">
		<cfargument name="itemUID" type="string" required="true">
		<cfargument name="jobUID" type="string" required="true">

		<cfset var local = structnew()>

		<cfquery name="local.checkItemUID" datasource="#application.dsn.platformQueue.dsn#">
			select count(qi.itemUID) as itemCount
			from dbo.tblQueueItems as qi
			INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.queueStatusID 
			INNER JOIN dbo.tblQueueTypes AS qt ON qt.queueTypeID = qs.queueTypeID
			where qi.itemUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.itemUID#">
			AND qt.queueType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueType#">
			AND qs.queueStatus = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueStatus#">
			AND qi.jobUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.jobUID#">
		</cfquery>
		<cfreturn (local.checkItemUID.itemCount gt 0)>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(qi.itemUID) as itemCount
			from dbo.tblQueueItems as qi
			inner join dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
			inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
			where qt.queueType = 'caCLECredits';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>	

</cfcomponent>