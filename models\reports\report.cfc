<cfcomponent output="false">

	<cffunction name="generateReportDocument" access="public" output="false" returnType="struct" hint="Consolidated method to generate report documents with download links">		
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="directoryPath" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="reportFileName" type="string" required="true">
		<cfargument name="reportTitle" type="string" required="true">
		<cfargument name="reportDescription" type="string" required="true">
		<cfargument name="reportID" type="numeric" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.result = { success=false, reportDocumentUID='', downloadLink='' }>

		<cfquery name="local.qryGetReportSection" datasource="#application.dsn.membercentral.dsn#">
			SELECT sectionID
			FROM dbo.cms_pageSections
			WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			AND sectionCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="MCAMSReportDocuments">
		</cfquery>

		<cfif val(local.qryGetReportSection.sectionID) is 0>
			<cfreturn local.result>
		</cfif>

		<cfscript>
			local.objDocument = CreateObject("component","models.system.platform.document");
			
			// Determine file extension from filename
			local.reportFileExt = listLast(arguments.reportFileName, ".");
			local.sourceFile = "#arguments.directoryPath#/#arguments.reportFileName#";

			// Create document in MCAMSReportDocuments section
			local.documentResult = local.objDocument.insertDocument(
				siteID=arguments.siteID, 
				sectionID=local.qryGetReportSection.sectionID,
				resourceType="ApplicationCreatedDocument", 
				docTitle=arguments.reportTitle,
				docDesc=arguments.reportDescription, 
				fileName=arguments.reportFileName, 
				fileExt=local.reportFileExt, 
				contributorMemberID=arguments.memberID, 
				recordedByMemberID=arguments.memberID
			);

			if (local.documentResult.documentID) {
				// Copy file to document storage location
				local.destinationFile = application.paths.RAIDSiteDocuments.path & arguments.orgCode & "/" & arguments.siteCode & "/" & local.documentResult.documentVersionID & ".#local.reportFileExt#";
				fileCopy(local.sourceFile, local.destinationFile);
				
				// Create report document record with 7-day expiration and reportID tracking
				local.reportDocumentResult = local.objDocument.insertReportDocument(
					siteID=arguments.siteID, 
					documentID=local.documentResult.documentID,
					reportID=arguments.reportID
				);
				
				// Add to S3 upload queue
				local.objDocument.addToS3UploadQueue(
					orgCode=arguments.orgCode, 
					siteCode=arguments.siteCode, 
					documentVersionID=local.documentResult.documentVersionID, 
					fileExt=local.reportFileExt
				);
				
				// Generate download link
				local.result.reportDocumentUID = local.reportDocumentResult.documentUID;
				local.result.downloadLink = "https://" & application.objSiteInfo.getSiteHostName(arguments.siteCode) & "/reportDownload/" & local.reportDocumentResult.documentUID;
				local.result.destinationFile = local.destinationFile;
				local.result.success = true;
			}

			return local.result;
		</cfscript>	
	</cffunction>

	<cffunction name="generateDownloadLinkHTML" access="public" output="false" returnType="string" hint="Generate HTML for download link with 7-day expiration notice">
		<cfargument name="downloadLink" type="string" required="true">
		<cfargument name="fileName" type="string" required="true">
		<cfargument name="linkText" type="string" required="false" default="Download Report">

		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.downloadHTML">
			<cfoutput>
			<p><a href="#arguments.downloadLink#" target="_blank">#arguments.linkText#</a></p>
			<p><em>(Note: The file is only available for download within 7 days.)</em></p>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.downloadHTML>
	</cffunction>

</cfcomponent>
