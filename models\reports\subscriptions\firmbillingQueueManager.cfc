<cfcomponent output="false" cache="true">

	<cfproperty name="birtRunner" inject="reports.birtRunner">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="400">
		
		<cfscript>
			local.arrTaskFields = [ 
				{ name="BatchSize", type="INTEGER", desc="Batch Size", value="50" },
				{ name="Threads", type="INTEGER", desc="Number of Threads", value="4" } 
			];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>	

		<cfset local.messageTypeID = arguments.strTask.scheduledTasksUtils.getMessageTypeID(messageTypeCode="FIRMBILLRPT")>
		<cfset local.processQueueResult = processQueue(messageTypeID=local.messageTypeID, batchSize=local.strTaskFields.batchSize, threads=local.strTaskFields.threads)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="messageTypeID" type="numeric" required="true">
		<cfargument name="batchSize" type="numeric" required="true">
		<cfargument name="threads" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset var reportPath = "/app/models/reports/subscriptions/firmbilling.rptdesign">
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfstoredproc procedure="queue_firmSubStatements_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#application.paths.SharedTemp.pathUNC#firm_">
				<cfprocresult name="local.qryFirms" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qryFirms.recordCount>

			<cfscript>
				// loop per firm
				QueryEach(local.qryFirms, function(struct thisFirm, numeric currentrow, query qryFirms) {

					var outputFileName = "";
					var reportXMLFile = "";
					var paramSet = arrayNew(1);
					var reportParams = arrayNew(1);
					var success = false;

					// item must still be in the grabbedForProcessing state for this job. else skip it. --->
					//this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. --->
					if (queueItemHasStatus(queueType='FirmSubStatements',queueStatus='grabbedForProcessing',itemUID=thisFirm.itemUID,jobUID=thisFirm.jobUID)) {
						try {
							<!--- UPDATE STATUS --->
							queueSetStatus(queueType='FirmSubStatements',queueStatus='processingFirm',itemUID=thisFirm.itemUID);
							// filename for the pdf
							outputFileName = "#thisFirm.company# #thisFirm.memberNumber#";
							outputFileName = replace(replace(rereplaceNoCase(outputFileName,'[^A-Z0-9 ]','','ALL'),' ','_','ALL'),'__','_','ALL');
							outputFileName = "#application.paths.SharedTemp.path#firm_#thisFirm.itemGroupUID#/#outputFileName#.pdf" ;

							<!--- 6/2020 : ticket 353905: client kept getting blank PDFs with all null values; this code attempts to alert us when this happens so we can track it down --->
							if (thisFirm.memberNumber eq "" OR outputFileName eq "_") {
								//local.tmpCatch = { type="", message="Firm Billing Report Queue Runner created null pdf.", detail="ItemUID: #thisFirm.itemUID#", tagContext=arrayNew(1) };
								local.tmpExtraInfo = duplicate(thisFirm);
								local.tmpExtraInfo.qryItemData = queryExecute(
									"select * from tblQueueItemData where itemUID = :itemUID", 
									{ itemUID = { value=thisFirm.itemUID, cfsqltype="CF_SQL_IDSTAMP" } },
									{ datasource=application.dsn.platformQueue.dsn }
								);
								try {
									throw(message="Firm Billing Report Queue Runner created null pdf", detail="ItemUID: #thisFirm.itemUID#");
								} catch (e) {
									application.objError.sendError(cfcatch=e, objectToDump=local.tmpExtraInfo);
								}
							}

							reportXMLFile = "#application.paths.SharedTemp.path#firm_#thisFirm.itemGroupUID#/#thisFirm.itemUID#.xml" ;

							arrayAppend(reportParams,{
								name="xmlFilePath",
								value=reportXMLFile,
								datatype="filepath",
								needsEvaluation=false
							});

							paramSet = XMLSearch(thisFirm.xmlConfigParam,'/params/param');
							for (var thisParam in paramSet) {
								if (listfindnocase("frmincludesection,showtax,frmrenewonlineoptions",thisParam.reportParam.xmlText)) {
									arrayAppend(reportParams,{
										name=thisParam.reportParam.xmlText,
										value=thisParam.paramvalue.xmlText,
										datatype="boolean",
										needsEvaluation=false
									});
								} else if (thisParam.reportParam.xmlText eq "invoiceheaderimgurl") { 
									arrayAppend(reportParams,{
										name=thisParam.reportParam.xmlText,
										value=application.paths.MCPlatform.internalUrl & "userassets/common/invoices/",
										datatype="string",
										needsEvaluation=false
									});
								} else {
									arrayAppend(reportParams,{
										name=thisParam.reportParam.xmlText,
										value=thisParam.paramvalue.xmlText,
										datatype="string",
										needsEvaluation=false
									});
								}
							}
							success = birtRunner.runReport(
								reportPath=reportPath,
								renderFormat="pdf",
								destinationPath=outputFileName,
								reportParams=reportParams);

							// UPDATE STATUS
							if (success) 
								queueSetStatus(queueType='FirmSubStatements',queueStatus='readyToNotify',itemUID=thisFirm.itemUID);

						} catch (e) {
							application.objError.sendError(cfcatch=e, objectToDump=local);
							rethrow;
						}
					}
				}, true, arguments.threads);
			</cfscript>

			<!--- ----------------------------------------------------------------------------------- --->
			<!--- post processing: process any notifications for itemGroupUIDs that are readyToNotify --->
			<!--- ----------------------------------------------------------------------------------- --->
			<cftry>
				<cfquery name="local.qryNotifications" datasource="#application.dsn.platformQueue.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						EXEC dbo.queue_firmSubStatements_grabForNotification;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfif local.qryNotifications.recordcount>
					<!--- Generate CSV of Firms --->
					<cfset local.generatedCSV = generateFirmCSV(qryFirms=local.qryFirms, filePath="#application.paths.SharedTemp.pathUNC#firm_#local.qryNotifications.itemGroupUID#")>

					<cfoutput query="local.qryNotifications">

						<cftry>
							<!--- compile pdfs into one zip --->
							<cfif directoryExists("#application.paths.SharedTemp.path#firm_#local.qryNotifications.itemGroupUID#")>
								<cfzip action="zip" source="#application.paths.SharedTemp.path#firm_#local.qryNotifications.itemGroupUID#" 
									file="#application.paths.SharedTemp.path#firm_#local.qryNotifications.itemGroupUID#/FirmBilling.zip"
									filter="*.pdf,*.csv" recurse="false" storepath="false"></cfzip>
							<cfelse>
								<cfthrow message="Folder of PDFs was not found">
							</cfif>
						
							<!--- prep and send email --->
							<cfset local.thisReportEmail = local.qryNotifications.reportEmail>
							<cfset local.thisSiteName = local.qryNotifications.siteName>
							<cfset local.thisSiteCode = local.qryNotifications.siteCode>

							<cfif len(local.thisReportEmail) and fileExists("#application.paths.SharedTemp.path#firm_#local.qryNotifications.itemGroupUID#/FirmBilling.zip")>
								<cfsavecontent variable="local.thisEmailContent">
									<div>
										We have completed processing your firm billing statements.<br/><br/>
										The report contains a PDF for each firm you selected.
									</div>
									<br/>
									<div><b>Questions?</b><br/>Contact <NAME_EMAIL> if you have any questions about this report.</div>
								</cfsavecontent>

								<!--- Generate download link instead of attachment --->
								<cfset local.objReportHelper = CreateObject("component","models.reports.report")>
								<cfset local.reportDocResult = local.objReportHelper.generateReportDocument(
									siteID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteID,
									siteCode=local.thisSiteCode,
									orgCode=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].orgCode,
									reportFilePath="#application.paths.SharedTemp.path#firm_#local.qryNotifications.itemGroupUID#/FirmBilling.zip",
									reportFileName="FirmBilling.zip",
									reportTitle="Firm Billing Statements",
									reportDescription="ZIP file containing PDF statements for selected firms",
									memberID=val(local.qryNotifications.memberID)
								)>
								<cfif local.reportDocResult.success>
									<cfset local.thisEmailContent = local.thisEmailContent & local.reportDocResult.downloadHTML>
								<cfelse>
									<cfset local.thisEmailContent = local.thisEmailContent & "<p>Error generating download link for report.</p>">
								</cfif>

								<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name="MemberCentral", email=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].networkEmailFrom},
									emailto=[{ name="#local.qryNotifications.firstname# #local.qryNotifications.lastname#", email=local.thisReportEmail }],
									emailreplyto=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].supportProviderEmail,
									emailsubject="Firm Billing Statements for #local.thisSiteName#",
									emailtitle="#local.thisSiteName# Firm Billing Statements",
									emailhtmlcontent=local.thisEmailContent,
									siteID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteID,
									memberID=val(local.qryNotifications.memberID),
									messageTypeID=arguments.messageTypeID,
									sendingSiteResourceID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteSiteResourceID
									)>
							</cfif>

							<!--- update status --->
							<cfif fileExists("#application.paths.SharedTemp.path#firm_#local.qryNotifications.itemGroupUID#/FirmBilling.zip")>
								<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
									set nocount on;
			
									declare @newstatus int;
									select @newstatus = qs.queueStatusID 
										from dbo.tblQueueStatuses as qs
										inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
										where qt.queueType = 'FirmSubStatements'
										and qs.queueStatus = 'done';
									
									update qi WITH (UPDLOCK, HOLDLOCK)
									set qi.queueStatusID = @newstatus,
										dateUpdated = getdate()
									from dbo.tblQueueItems as qi
									inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
									where qid.itemGroupUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#local.qryNotifications.itemGroupUID#">;
								</cfquery>
							</cfif>

						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
						</cfcatch>
						</cftry>

					</cfoutput>
				</cfif>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>			

			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfquery name="local.qryClear" datasource="#application.dsn.platformQueue.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						EXEC dbo.queue_firmSubStatements_clearDone;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>	

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
					
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="queueSetStatus" access="private" returntype="void" output="false">
		<cfargument name="queueType" type="string" required="true">
		<cfargument name="queueStatus" type="string" required="true">
		<cfargument name="itemUID" type="string" required="true">

		<cfstoredproc procedure="queue_setStatus" datasource="#application.dsn.platformQueue.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueType#">
			<cfprocparam type="In" cfsqltype="CF_SQL_IDSTAMP" value="#arguments.itemUID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueStatus#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="queueItemHasStatus" access="private" returntype="boolean" output="false">
		<cfargument name="queueType" type="string" required="true">
		<cfargument name="queueStatus" type="string" required="true">
		<cfargument name="itemUID" type="string" required="true">
		<cfargument name="jobUID" type="string" required="true">

		<cfset var local = structnew()>

		<cfquery name="local.checkItemUID" datasource="#application.dsn.platformQueue.dsn#">
			select count(qi.itemUID) as itemCount
			from dbo.tblQueueItems as qi
			INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.queueStatusID 
			INNER JOIN dbo.tblQueueTypes AS qt ON qt.queueTypeID = qs.queueTypeID
			where qi.itemUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.itemUID#">
			AND qt.queueType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueType#">
			AND qs.queueStatus = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueStatus#">
			AND qi.jobUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.jobUID#">
		</cfquery>

		<cfreturn (local.checkItemUID.itemCount gt 0)>
	</cffunction>

	<cffunction name="generateFirmCSV" access="private" output="false" returntype="boolean">
		<cfargument name="qryFirms" type="query" required="true">
		<cfargument name="filePath" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.success = true>		

		<cftry>
			<cfset local.arrFS = arrayNew(1)>
			<cfset local.fieldSet = xmlsearch(arguments.qryFirms.XMLFieldSets,"/fieldsets/fieldset")>
			<cfscript>
			for (var thisParam in local.fieldSet) {
				if (thisParam.reportParam.xmlText eq "fieldset") {
					arrayAppend(local.arrFS, thisParam.paramvalue.xmlText );
				}
			}
			</cfscript>

			<cfif arrayLen(local.arrFS)>
				<!--- get fieldSetIDs --->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFS">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT mfs.fieldsetID, MIN(tmp.autoID) AS fieldSetOrder
					FROM dbo.ams_memberFieldSets AS mfs
					INNER JOIN dbo.fn_varcharListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arrayToList(local.arrFS)#">,',') AS tmp ON tmp.listitem = mfs.[uid]
					GROUP BY mfs.fieldsetID
					ORDER BY fieldSetOrder;
					
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfset local.fieldSetIDList = valueList(local.qryFS.fieldSetID)>
			<cfelse>
				<cfset local.fieldSetIDList = "">
			</cfif>

			<cfset local.download_HoldingPath = "#arguments.filePath#\firms.csv">
			<cfset local.tempTableName = "rpt#getTickCount()#">

			<!--- Generate CSV --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData" result="local.qryDataResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
						DROP TABLE ###local.tempTableName#;
					IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
						DROP TABLE ###local.tempTableName#_2;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;
					CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);

					DECLARE @orgID int, @itemGroupUID uniqueidentifier, @outputFieldsXML xml;
					DECLARE @tmpMembers TABLE (memberID int PRIMARY KEY);
					
					SELECT @orgID = dbo.fn_getOrgIDFromSiteID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryFirms.siteID#">);
					SET @itemGroupUID = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.qryFirms.itemGroupUID#">;

					INSERT INTO @tmpMembers
					SELECT DISTINCT dataKey
					FROM platformQueue.dbo.tblQueueItemData AS qid
					INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns AS dc ON dc.columnID = qid.columnID
						AND dc.columnname IN ('FirmChildSub','FirmChildNoSub')
					WHERE qid.itemGroupUID = @itemGroupUID;

					SELECT m.memberid, m.lastname as [Last Name], m.firstname as [First Name], m.membernumber as [Member Number], m.company as [Company]
					INTO ###local.tempTableName#_2
					FROM dbo.ams_members AS m
					INNER JOIN @tmpMembers AS tmp ON tmp.memberID = m.memberID
					WHERE m.orgID = @orgID
					AND m.memberID = m.activeMemberID;

					CREATE NONCLUSTERED INDEX IX_memberid ON ###local.tempTableName#_2 (memberID);

					-- get fieldset data and set back to snapshot because proc ends in read committed
					EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
						@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.fieldSetIDList#">,
						@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
						@ovNameFormat='LSXPFM', @ovMaskEmails=0, @membersTableName='###local.tempTableName#_2', 
						@membersResultTableName='##tmpMembersFS', @linkedMembers=0, @mode='export', 
						@outputFieldsXML=@outputFieldsXML OUTPUT;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT tm.[Last Name], tm.[First Name], tm.[Member Number], tm.Company, tmp.*
					INTO ###local.tempTableName#
					FROM ###local.tempTableName#_2 AS tm
					INNER JOIN ##tmpMembersFS AS tmp ON tmp.memberID = tm.memberID;

					<!--- drop mc_ and any other fields from temp table that we dont want in the csv --->
					ALTER TABLE ###local.tempTableName# DROP COLUMN memberid;
					ALTER TABLE ###local.tempTableName# DROP COLUMN [Extended MemberNumber];
					ALTER TABLE ###local.tempTableName# DROP COLUMN [Extended Name];

					<!--- produce csv --->
					DECLARE @selectsql varchar(max) = '
						SELECT *, ROW_NUMBER() OVER(order by [Last Name], [First Name], [Member Number]) as mcCSVorder 
						*FROM* ###local.tempTableName#';
					EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#local.download_HoldingPath#', @returnColumns=0;

					IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
						DROP TABLE ###local.tempTableName#;
					IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
						DROP TABLE ###local.tempTableName#_2;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>	

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>			

		<cfreturn local.success>
	</cffunction>

</cfcomponent>
