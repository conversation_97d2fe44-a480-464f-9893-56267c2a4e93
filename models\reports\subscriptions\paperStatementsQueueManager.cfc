<cfcomponent output="false" cache="true">
	<cfproperty name="birtRunner" inject="reports.birtRunner">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="400">
		
		<cfscript>
			local.arrTaskFields = [ 
				{ name="BatchSize", type="INTEGER", desc="Batch Size", value="200" },
				{ name="Threads", type="INTEGER", desc="Number of Threads", value="4" } 
			];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>	

		<cfset local.messageTypeID = arguments.strTask.scheduledTasksUtils.getMessageTypeID(messageTypeCode="SUBPAPERSTM")>
		<cfset local.processQueueResult = processQueue(messageTypeID=local.messageTypeID, batchSize=local.strTaskFields.batchSize, threads=local.strTaskFields.threads)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="messageTypeID" type="numeric" required="true">
		<cfargument name="batchSize" type="numeric" required="true">
		<cfargument name="threads" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset var reportPath = "/app/models/reports/subscriptions/paperstatements.rptdesign">
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfstoredproc procedure="queue_paperStatements_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#application.paths.SharedTemp.pathUNC#member_">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#application.paths.SharedTemp.path#member_">
				<cfprocresult name="local.qryMembers" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount = local.qryMembers.recordcount>

			<cfscript>
				// loop per member
				QueryEach(local.qryMembers, function(struct thisMember, numeric currentrow, query qryMembers) {

					var outputFileName = "";
					var reportXMLFile = "";
					var reportFolderPath = ""
					var paramSet = arrayNew(1);
					var reportParams = arrayNew(1);
					var success = false;

					// item must still be in the grabbedForProcessing state for this job. else skip it. --->
					//this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. --->
					if (queueItemHasStatus(queueType='PaperStatements',queueStatus='grabbedForProcessing',itemUID=thisMember.itemUID,jobUID=thisMember.jobUID)) {
						try {
							<!--- UPDATE STATUS --->
							queueSetStatus(queueType='PaperStatements',queueStatus='processingItem',itemUID=thisMember.itemUID);
							
							// Set up the report folder path
							reportFolderPath = application.paths.SharedTemp.path & "member_#thisMember.itemGroupUID#";
							
							// Generate the filename for the PDF
							outputFileName = thisMember.lastName & "_" & thisMember.firstName & "_" & thisMember.memberNumber;
							outputFileName = rereplaceNoCase(outputFileName, "[^A-Z0-9_]", "", "ALL");
							outputFileName = reportFolderPath & "/" & outputFileName & ".pdf";

							// Path for the XML report file
							reportXMLFile = reportFolderPath & "/" & thisMember.itemUID & ".xml";

							arrayAppend(reportParams,{
								name="xmlFilePath",
								value=reportXMLFile,
								datatype="filepath",
								needsEvaluation=false
							});

							var renewalsuburl = "";
							var frmrenewonlineoptions = false;
							paramSet = XMLSearch(thisMember.xmlConfigParam,'/params/param');
							for (var thisParam in paramSet) {
								if (listfindnocase("frmrenewonlineoptions",thisParam.reportParam.xmlText)) {
									arrayAppend(reportParams,{
										name=thisParam.reportParam.xmlText,
										value=thisParam.paramvalue.xmlText,
										datatype="boolean",
										needsEvaluation=false
									});
									frmrenewonlineoptions = thisParam.paramvalue.xmlText;
								} else if (thisParam.reportParam.xmlText eq "invoiceheaderimgurl") { 
									arrayAppend(reportParams,{
										name=thisParam.reportParam.xmlText,
										value=application.paths.MCPlatform.internalUrl & "userassets/common/invoices/",
										datatype="string",
										needsEvaluation=false
									});
								} else {
									arrayAppend(reportParams,{
										name=thisParam.reportParam.xmlText,
										value=thisParam.paramvalue.xmlText,
										datatype="string",
										needsEvaluation=false
									});
								}
								if (thisParam.reportParam.xmlText eq "renewalsuburl") {
									renewalsuburl = thisParam.paramvalue.xmlText;
								}
							}
							
							// Parse the XML data
							
							if(len(renewalsuburl) && frmrenewonlineoptions){
								var xmlData = xmlParse(reportXMLFile);
								var accountNodes = xmlSearch(reportXMLFile, "//account");
								if (arrayLen(accountNodes)) {
									var qrcodeobj = CreateObject("component","models.system.platform.qrCodeGenerator").init();
									if(structKeyExists(accountNodes[1].xmlAttributes, "renewalcode") && len(accountNodes[1].XmlAttributes.renewalcode)){
										var qrcodeimage = qrcodeobj.getQRCodeImage(contents="#renewalsuburl#/#accountNodes[1].XmlAttributes.renewalcode#", width=85, height=85);																				
										var qrfilepath = replace(accountNodes[1].XmlAttributes.renewalsubqrcodeimgurl, "file://", "", "all");
										imageWrite(name=qrcodeimage,destination=qrfilepath,overwrite=true);								
									}
								}
							}
							success = birtRunner.runReport(
								reportPath=reportPath,
								renderFormat="pdf",
								destinationPath=outputFileName,
								reportParams=reportParams);

							// UPDATE STATUS
							if (success) 
								queueSetStatus(queueType='PaperStatements',queueStatus='readyToNotify',itemUID=thisMember.itemUID);

						} catch (e) {
							application.objError.sendError(cfcatch=e, objectToDump=local);
							rethrow;
						}
					}
				}, true, arguments.threads);
			</cfscript>

			<!--- ----------------------------------------------------------------------------------- --->
			<!--- post processing: process any notifications for itemGroupUIDs that are readyToNotify --->
			<!--- ----------------------------------------------------------------------------------- --->
			<cftry>
				<cfquery name="local.qryNotifications" datasource="#application.dsn.platformQueue.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						EXEC dbo.queue_paperStatements_grabForNotification;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfif local.qryNotifications.recordcount>
					<cfoutput query="local.qryNotifications">
						<cftry>
							<cfset local.directoryPath = "#application.paths.SharedTemp.path#member_#local.qryNotifications.itemGroupUID#">
							
							<cfif directoryExists("#local.directoryPath#")>
								<cfscript>
									local.qryPDFs = DirectoryList(path=local.directoryPath, recurse=false, listInfo="query", sort="asc", type="file", filter="*.pdf");
									if (local.qryPDFs.recordCount) {
										if (local.qryNotifications.downloadMode EQ 'pdf') {
											// merge pdfs. Need to do it this way because cfexecute wouldnt handle the wildcard correctly.
											local.reportFileName = "PaperStatements.pdf";
											fileWrite("#local.directoryPath#/merge.sh","/usr/bin/pdftk #local.directoryPath#/*.pdf cat output #local.directoryPath#/#local.reportFileName#");
										} else {
											// zip pdfs. Need to do it this way because cfexecute wouldnt handle the wildcard correctly.
											local.reportFileName = "PaperStatements.zip";
											fileWrite("#local.directoryPath#/merge.sh","/usr/bin/zip -j #local.directoryPath#/#local.reportFileName# #local.directoryPath#/*.pdf");
										}
										cfexecute(name="/bin/bash", arguments="#local.directoryPath#/merge.sh", variable="local.standardOut", errorVariable="local.errorMessage", timeout="600", terminateOnTimeout=true);
									}
								</cfscript>
							<cfelse>
								<cfthrow message="Folder of PDFs was not found">
							</cfif>

							<!--- record site document --->
							<cfset local.documentResult = generateReportDocument(siteID=local.qryNotifications.siteID, siteCode=local.qryNotifications.siteCode, 
								orgCode=local.qryNotifications.orgCode, directoryPath=local.directoryPath, memberID=local.qryNotifications.memberID,
								downloadMode=local.qryNotifications.downloadMode)>
							<cfif not local.documentResult.success>
								<cfthrow message="Error generating report document">
							</cfif>					

							<!--- prep and send email --->
							<cfset local.thisReportEmail = local.qryNotifications.reportEmail>
							<cfset local.thisSiteName = local.qryNotifications.siteName>
							<cfset local.thisSiteCode = local.qryNotifications.siteCode>

							<cfif len(local.thisReportEmail) and fileExists("#local.documentResult.destinationFile#")>
								<cfsavecontent variable="local.thisEmailContent">
									<div>
										#local.qryNotifications.firstname#:<br/><br/>
										We have completed processing your requested Paper Statements from Manage Subscribers.<br/><br/>
										<a href="#application.objSiteInfo.mc_siteInfo[local.qryNotifications.siteCode].scheme#://#application.objSiteInfo.mc_siteInfo[local.qryNotifications.siteCode].mainhostname#/reportDownload/#local.documentResult.reportDocumentUID#">Download #local.reportFileName#</a><br/><br/>
										(Note: The file is only available for download within 7 days.)
									</div>
									<br/>
									<div><b>Questions?</b><br/>Contact <NAME_EMAIL> if you have questions about this file.</div>
								</cfsavecontent>

								<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name="MemberCentral", email=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].networkEmailFrom},
									emailto=[{ name="#local.qryNotifications.firstname# #local.qryNotifications.lastName#", email=local.thisReportEmail }],
									emailreplyto=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].supportProviderEmail,
									emailsubject="Paper Statements for #local.thisSiteName#",
									emailtitle="#local.thisSiteName# Paper Statements",
									emailhtmlcontent=local.thisEmailContent,
									siteID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteID,
									memberID=val(local.qryNotifications.memberID),
									messageTypeID=arguments.messageTypeID,
									sendingSiteResourceID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteSiteResourceID
									)>
							</cfif>

							<!--- update status --->
							<cfif fileExists("#local.documentResult.destinationFile#")>
								<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
									set nocount on;
			
									declare @newstatus int;
									select @newstatus = qs.queueStatusID 
										from dbo.tblQueueStatuses as qs
										inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
										where qt.queueType = 'PaperStatements'
										and qs.queueStatus = 'done';
									
									update qi WITH (UPDLOCK, HOLDLOCK)
									set qi.queueStatusID = @newstatus,
										dateUpdated = getdate()
									from dbo.tblQueueItems as qi
									inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
									where qid.itemGroupUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#local.qryNotifications.itemGroupUID#">;
								</cfquery>
							</cfif>

						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
						</cfcatch>
						</cftry>

					</cfoutput>
				</cfif>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>			

			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfquery name="local.qryClear" datasource="#application.dsn.platformQueue.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						EXEC dbo.queue_paperStatements_clearDone;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>	

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
					
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="generateReportDocument" access="private" output="false" returnType="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="directoryPath" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="downloadMode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.objReportHelper = CreateObject("component","models.reports.report")>

		<cfscript>
			if (arguments.downloadMode eq 'pdf') {
				local.reportFileName = "PaperStatements.pdf";
				local.reportFileDesc = "PDF file of Manage Subscribers Paper Statements";
			} else {
				local.reportFileName = "PaperStatements.zip";
				local.reportFileDesc = "Zip file of Manage Subscribers Paper Statements";
			}

			return local.objReportHelper.generateReportDocument(
				siteID=arguments.siteID,
				siteCode=arguments.siteCode,
				orgCode=arguments.orgCode,
				directoryPath=arguments.directoryPath,
				memberID=arguments.memberID,
				reportFileName=local.reportFileName,
				reportTitle="Manage Subscribers Paper Statements",
				reportDescription=local.reportFileDesc
			);
		</cfscript>
	</cffunction>

	<cffunction name="queueSetStatus" access="private" returntype="void" output="false">
		<cfargument name="queueType" type="string" required="true">
		<cfargument name="queueStatus" type="string" required="true">
		<cfargument name="itemUID" type="string" required="true">

		<cfstoredproc procedure="queue_setStatus" datasource="#application.dsn.platformQueue.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueType#">
			<cfprocparam type="In" cfsqltype="CF_SQL_IDSTAMP" value="#arguments.itemUID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueStatus#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="queueItemHasStatus" access="private" returntype="boolean" output="false">
		<cfargument name="queueType" type="string" required="true">
		<cfargument name="queueStatus" type="string" required="true">
		<cfargument name="itemUID" type="string" required="true">
		<cfargument name="jobUID" type="string" required="true">

		<cfset var local = structnew()>

		<cfquery name="local.checkItemUID" datasource="#application.dsn.platformQueue.dsn#">
			select count(qi.itemUID) as itemCount
			from dbo.tblQueueItems as qi
			INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.queueStatusID 
			INNER JOIN dbo.tblQueueTypes AS qt ON qt.queueTypeID = qs.queueTypeID
			where qi.itemUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.itemUID#">
			AND qt.queueType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueType#">
			AND qs.queueStatus = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueStatus#">
			AND qi.jobUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.jobUID#">
		</cfquery>

		<cfreturn (local.checkItemUID.itemCount gt 0)>
	</cffunction>

</cfcomponent>
